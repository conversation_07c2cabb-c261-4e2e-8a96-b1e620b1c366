﻿layui.define(['laytpl', 'form', 'laydate', 'request', 'tableRequest', 'common', 'workWxApp', 'kfCase'], function (exports) {
    var func = {
        /**
        * 渲染企微应用下拉选框
        * */
        initApp: function (id) {
            layui.workWxApp.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企微' });
                var getTpl = document.getElementById("workwx-app-tpl").innerHTML
                    , view = document.getElementById('workwx-app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('workwx-app-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
        * 通过id获取客服用户信息
        * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.alertAutoClose("参数有误");
                setTimeout(function () { history.back(-1) }, 2000);
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-user/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('#id').text(res.result.id);
                    layui.$('#workWxApp').text(res.result.workWxAppName + ' [ID:' + res.result.workWxAppId + ']');
                    layui.$('#kfCase').text(res.result.kfCaseName + ' [ID:' + res.result.kfCaseId + ']');
                    layui.$('#scene').text(res.result.scene);
                    layui.$('#sceneParam').text(res.result.sceneParam);
                    if (res.result.tencentAdAccountId != '') {
                        layui.$('#tencentAdAccount').text(res.result.tencentAdAccountName + ' [ID:' + res.result.tencentAdAccountId + ']');
                    }
                    layui.$('#openKfId').text(res.result.openKfId);
                    layui.$('#isAddWx').text(res.result.isAddWx ? '是' : '否');
                    layui.$('#externalUser').html('编号：' + res.result.externalUserID + '<br/>姓名：' + (res.result.externalUserName == '' ? '-' : res.result.externalUserName) + '</br>分配用户：' + (res.result.followUserId == '' ? '-' : res.result.followUserId));
                    layui.$('#workState').text(res.result.workState == '' ? '-' : res.result.workState);
                    if (res.result.isAddWx) {
                        if (res.result.callbackContent != '') {
                            var callbackContentJson = JSON.stringify(JSON.parse(res.result.callbackContent), null, 2);
                            layui.$('#callbackContent').html(callbackContentJson);
                        }

                        layui.$('#callbackTime').html(layui.common.timeFormat(res.result.callbackTime));
                    }
                    else {
                        layui.$('#callbackTime').html('-');
                    }

                    layui.$('#workWxDeleted').text(res.result.workWxDeleted ? '是' : '否');
                    layui.$('#isReport').text(res.result.isReport ? '是' : '否');
                    if (res.result.isReport) {
                        var reportEventJson = JSON.stringify(res.result.reportEvent, null, 2);
                        layui.$('#reportEvent').html(reportEventJson);
                    }
                    layui.$('#clickId').text(res.result.clickId == null ? '-' : res.result.clickId);
                    layui.$('#createdAt').text(layui.common.timeFormat(res.result.createdAt));
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 获取客服用户列表
        * */
        query: function () {
            layui.tableRequest.request('resource', true, 'kf-user-list', '/admin/workwx/kf-user/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'workWxAppName', title: '企微应用名称' },
                { field: 'kfCaseName', title: '加粉方案' },
                {
                    field: 'isAddWx', width: 90, align: 'center', title: '添加企微', templet: function (e) {
                        if (e.isAddWx) {
                            return e.workWxDeleted ? '<span style="color:#f00">已删除</span>' : '是';
                        }
                        else {
                            return '否';
                        }
                    }
                },
                {
                    field: 'isReport', width: 90, align: 'center', title: '是否回传', templet: function (e) {
                        return e.isReport ? '是' : '否';
                    }
                },
                { field: 'externalUserID', title: '外部联系人ID' },
                {
                    field: 'externalUserName', title: '外部联系人名称', templet: function (e) {
                        return e.externalUserName == '' ? '-' : e.externalUserName;
                    }
                },
                {
                    field: 'followUserId', title: '分配用户ID', templet: function (e) {
                        return e.followUserId == '' ? '-' : e.followUserId;
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 80, align: 'left', toolbar: '#kf-user-bar' }
            ]);
            //监听表格事件
            layui.kfUser.tableEvent();
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    location.href = 'detail.html?id=' + data.id;
                    //layui.common.openIframe('微信客服加粉方案', 740, 700, 'update.html?id=' + data.id);
                }
            });
        },
        /**
         * 绑定事件
         * */
        bindEvent: function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            //监听查询按钮
            layui.form.on('submit(kf-user-search)', function (data) {
                var field = data.field;
                if (field.callbackStatus == '') {
                    field.callbackStatus = -1;
                }
                if (field.reportStatus == '') {
                    field.reportStatus = -1;
                }
                //执行重载
                layui.tableRequest.reload("kf-user-list", {
                    where: field
                });
            });
            //切换企微应用时加载加粉方案
            layui.form.on('select(workwx-app-id)', function (data) {
                layui.kfCase.queryByWorkWxAppId(data.value, function (res) {
                    if (res.result == null) {
                        res.result = [{ id: '', name: '请选择加粉方案' }]
                    }
                    else {
                        res.result.unshift({ id: '', name: '请选择加粉方案' });
                    }
                    var getTpl = document.getElementById("kf-case-tpl").innerHTML
                        , view = document.getElementById('kf-case-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.form.render('select');
                });
            });
        }
    };
    exports('kfUser', func);
});