﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 400px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">应用名称</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入应用名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">应用AppID</label>
            <div class="layui-input-inline">
                <input type="text" name="appId" lay-verify="required" placeholder="请输入应用AppID" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">应用秘钥</label>
            <div class="layui-input-inline">
                <input type="text" name="secret" lay-verify="required" placeholder="请输入应用秘钥" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">授权范围</label>
            <div class="layui-input-inline">
                <input type="text" name="scope" placeholder="请输入授权范围" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">回调地址</label>
            <div class="layui-input-inline">
                <input type="text" name="callbackUrl" lay-verify="required" placeholder="请输入授权回调地址" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="app-create-submit" value="确认添加">
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'oceanengineApp'], function () {
            //监听提交事件
            layui.form.on('submit(app-create-submit)', function (data) {
                layui.oceanengineApp.create(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>