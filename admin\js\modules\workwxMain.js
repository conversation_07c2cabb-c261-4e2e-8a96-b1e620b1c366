﻿layui.define(['request', 'form', 'workWxApp'], function (exports) {
    var myChart;
    var option;
    var myChart2;
    var option2;
    var func = {
        init: function () {
            //生成客服链路曲线图
            var chartDom = document.getElementById('main');
            myChart = echarts.init(chartDom);
            option = {
                title: {
                    text: '客服链路企微转化用户数曲线图',
                },
                tooltip: {
                    trigger: 'axis'
                },
                grid: {
                    left: '0',
                    right: '20%',
                    bottom: '0',
                    containLabel: true
                },
                toolbox: {
                    feature: {
                        saveAsImage: {}
                    }
                },
                yAxis: {
                    type: 'value'
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: []
                },
                legend: {
                    data: [],
                    type: 'scroll',
                    orient: 'vertical',
                    right: 20,
                    top: 40,
                    //bottom: 20,
                },
                series: []
            };
            myChart.setOption(option)

            //生成小程序链路曲线图
            //生成客服链路曲线图
            var chartDom2 = document.getElementById('main-mini');
            myChart2 = echarts.init(chartDom2);
            option2 = {
                title: {
                    text: '小程序链路企微转化用户数曲线图',
                },
                tooltip: {
                    trigger: 'axis'
                },
                grid: {
                    left: '0',
                    right: '20%',
                    bottom: '0',
                    containLabel: true
                },
                toolbox: {
                    feature: {
                        saveAsImage: {}
                    }
                },
                yAxis: {
                    type: 'value'
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: []
                },
                legend: {
                    data: [],
                    type: 'scroll',
                    orient: 'vertical',
                    right: 20,
                    top: 40,
                    //bottom: 20,
                },
                series: []
            };
            myChart2.setOption(option2)

            //加载企微应用列表
            layui.workWxApp.getAll(function (res) {
                var getTpl = document.getElementById("workwx-app-tpl").innerHTML
                    , view = document.getElementById('workwx-app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                //首次进入默认显示第一个企微应用的客服账号
                layui.$("#workwx-app-view").val(res.result[0].id);
                workWxAppId = res.result[0].id;
                layui.form.render('select');
                //加载统计数据
                layui.workwxMain.getKfUserStat(res.result[0].id);
                layui.workwxMain.getMiniUserStat(res.result[0].id);
                layui.workwxMain.getKfUserChatStat(res.result[0].id);
                layui.workwxMain.getMiniUserChatStat(res.result[0].id);
                layui.workwxMain.getKfMsgStat(res.result[0].id);
                layui.workwxMain.getClickRecordStat(res.result[0].id);

                //绑定搜索事件
                layui.form.on('select(workwx-app-search)', function (data) {
                    layui.workwxMain.getKfUserStat(data.value);
                    layui.workwxMain.getMiniUserStat(data.value);
                    layui.workwxMain.getKfUserChatStat(data.value);
                    layui.workwxMain.getMiniUserChatStat(data.value);
                    layui.workwxMain.getKfMsgStat(data.value);
                    layui.workwxMain.getClickRecordStat(data.value);
                });
                var time1;
                //绑定自动刷新数据事件
                var showTime1;
                var showTime2;
                layui.form.on('checkbox(auto-refresh-data)', function (data) {
                    if (data.elem.checked) {
                        var sec1 = 10;
                        showTime1 = setInterval(function () {
                            layui.$('.tips').text('数据将在' + sec1 + '秒后更新...');
                            sec1--;
                            if (sec1 == 0) {
                                window.clearInterval(showTime1);
                            }
                        }, 1000);
                        time1 = window.setInterval(function () {
                            var sec2 = 10;
                            showTime2 = setInterval(function () {
                                layui.$('.tips').text('数据将在' + sec2 + '秒后更新...');
                                sec2--;
                                if (sec2 == 0) {
                                    window.clearInterval(showTime2);
                                }
                            }, 1000);

                            var val = layui.$('[name=workWxAppId]').val();
                            layui.workwxMain.getKfUserStat(val);
                            layui.workwxMain.getMiniUserStat(val);
                            layui.workwxMain.getKfUserChatStat(val);
                            layui.workwxMain.getMiniUserChatStat(val);
                            layui.workwxMain.getKfMsgStat(val);
                            layui.workwxMain.getClickRecordStat(val);
                            layui.$('.tips').text('数据刷新成功');
                        }, 11000);
                    }
                    else {
                        layui.$('.tips').text('');
                        window.clearInterval(time1);
                        if (showTime1) {
                            window.clearInterval(showTime1);
                        }
                        if (showTime2) {
                            window.clearInterval(showTime2);
                        }
                    }
                });
            });
        },
        getKfUserStat: function (workWxAppId) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-user/statistics/get?workWxAppId=' + workWxAppId,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('#userTotalCount').text(res.result.totalCount);
                    layui.$('#userYesterdayCount').text(res.result.yesterdayCount);
                    layui.$('#userTodayCount').text(res.result.todayCount);
                    layui.$('#userTotalConversionCount').text(res.result.totalConversionCount);
                    layui.$('#userYesterdayConversionCount').text(res.result.yesterdayConversionCount);
                    layui.$('#userTodayConversionCount').text(res.result.todayConversionCount);
                }
            })
        },
        getMiniUserStat: function (workWxAppId) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/mini-user/statistics/get?workWxAppId=' + workWxAppId,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('#userMiniTotalCount').text(res.result.totalCount);
                    layui.$('#userMiniYesterdayCount').text(res.result.yesterdayCount);
                    layui.$('#userMiniTodayCount').text(res.result.todayCount);
                    layui.$('#userMiniTotalConversionCount').text(res.result.totalConversionCount);
                    layui.$('#userMiniYesterdayConversionCount').text(res.result.yesterdayConversionCount);
                    layui.$('#userMiniTodayConversionCount').text(res.result.todayConversionCount);
                }
            })
        },
        getKfUserChatStat: function (workWxAppId) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-user/chat-statistics/get?workWxAppId=' + workWxAppId,
            }).then(function (res) {
                if (res.isSuccess) {
                    var series = [];
                    if (res.result.series.length > 0) {
                        for (var i = 0; i < res.result.series.length; i++) {
                            series.push({
                                name: res.result.series[i].name,
                                type: 'line',
                                smooth: true,
                                data: res.result.series[i].data
                            })
                        }
                    }
                    option.series = series;
                    option.legend.data = res.result.kfCaseNames;
                    option.xAxis.data = res.result.times
                    myChart.setOption(option, true);
                }
            })
        },
        getMiniUserChatStat: function (workWxAppId) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/mini-user/chat-statistics/get?workWxAppId=' + workWxAppId,
            }).then(function (res) {
                if (res.isSuccess) {
                    var series = [];
                    if (res.result.series.length > 0) {
                        for (var i = 0; i < res.result.series.length; i++) {
                            series.push({
                                name: res.result.series[i].name,
                                type: 'line',
                                smooth: true,
                                data: res.result.series[i].data
                            })
                        }
                    }
                    option2.series = series;
                    option2.legend.data = res.result.miniNames;
                    option2.xAxis.data = res.result.times
                    myChart2.setOption(option2, true);
                }
            })
        },
        getKfMsgStat: function (workWxAppId) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-msg/statistics/get?workWxAppId=' + workWxAppId,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('#kfMsgTotalCount').text(res.result.totalCount);
                    layui.$('#kfMsgOpenSessionTotalCount').text(res.result.openSessionTotalCount);
                    layui.$('#kfMsgSendTextMsgTotalCount').text(res.result.sendTextMsgTotalCount);
                    //layui.$('#kfMsgSendOtherTotalCount').text(res.result.sendOtherTotalCount);
                    layui.$('#kfMsgYesterdayCount').text(res.result.yesterdayCount);
                    layui.$('#kfMsgOpenSessionYesterdayCount').text(res.result.openSessionYesterdayCount);
                    layui.$('#kfMsgSendTextMsgYesterdayCount').text(res.result.sendTextMsgYesterdayCount);
                    //layui.$('#kfMsgSendOtherYesterdayCount').text(res.result.sendOtherYesterdayCount);
                    layui.$('#kfMsgTodayCount').text(res.result.todayCount);
                    layui.$('#kfMsgOpenSessionTodayCount').text(res.result.openSessionTodayCount);
                    layui.$('#kfMsgSendTextMsgTodayCount').text(res.result.sendTextMsgTodayCount);
                    //layui.$('#kfMsgSendOtherTodayCount').text(res.result.sendOtherTodayCount);
                }
            })
        },
        getClickRecordStat: function (workWxAppId) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/click-record/statistics/get?workWxAppId=' + workWxAppId,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('#clickRecordTotalCount').text(res.result.totalCount);
                    layui.$('#clickRecordYesterdayCount').text(res.result.yesterdayCount);
                    layui.$('#clickRecordTodayCount').text(res.result.todayCount);
                }
            })
        }
    }
    exports('workwxMain', func);
});