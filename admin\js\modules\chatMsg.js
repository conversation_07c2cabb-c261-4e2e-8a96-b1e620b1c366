﻿layui.define(['laytpl', 'laydate', 'request', 'tableRequest', 'common', 'workWxApp'], function (exports) {
    var func = {
        /**
         * 渲染企微应用下拉选框
         * @param {any} id
         */
        initApp: function (id) {
            layui.workWxApp.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企微' });
                var getTpl = document.getElementById("workwx-app-tpl").innerHTML
                    , view = document.getElementById('workwx-app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('workwx-app-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
        * 获取外部联系人列表
        * */
        query: function () {
            layui.tableRequest.request('resource', true, 'chat-msg-list', '/admin/workwx/chat-msg/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'workWxAppName', title: '企微应用名称' },
                {
                    field: 'msgType', title: '消息类型', templet: function (e) {
                        var res = '-';
                        if (e.msgType == 'text') res = '文本';
                        else if (e.msgType == 'image') res = '图片';
                        else if (e.msgType == 'revoke') res = '撤回消息';
                        else if (e.msgType == 'agree') res = '同意会话聊天';
                        else if (e.msgType == 'disagree') res = '不同意会话聊天';
                        else if (e.msgType == 'voice') res = '语音';
                        else if (e.msgType == 'video') res = '视频';
                        else if (e.msgType == 'card') res = '名片';
                        else if (e.msgType == 'location') res = '位置';
                        else if (e.msgType == 'emotion') res = '自定义表情';
                        else if (e.msgType == 'file') res = '文件';
                        else if (e.msgType == 'link') res = '链接';
                        else if (e.msgType == 'weapp') res = '小程序消息';
                        else if (e.msgType == 'chatrecord') res = '聊天记录';
                        else if (e.msgType == 'todo') res = '待办消息';
                        else if (e.msgType == 'vote') res = '投票消息';
                        else if (e.msgType == 'collect') res = '填表消息';
                        else if (e.msgType == 'redpacket') res = '红包消息';
                        else if (e.msgType == 'meeting') res = '会议邀请';
                        else if (e.msgType == 'switch') res = '切换企业';
                        else if (e.msgType == 'docmsg') res = '在线文档';
                        else if (e.msgType == 'markdown') res = 'MarkDown';
                        else if (e.msgType == 'news') res = '图文消息';
                        else if (e.msgType == 'calendar') res = '日程消息';
                        else if (e.msgType == 'mixed') res = '混合消息';
                        else if (e.msgType == 'meeting_voice_call') res = '音频存档消息';
                        else if (e.msgType == 'voip_doc_share') res = '音频共享消息';
                        else if (e.msgType == 'external_redpacket') res = '互通红包消息';
                        else if (e.msgType == 'sphfeed') res = '视频号';
                        return res;
                    }
                },
                { field: 'sequnce', title: '消息序列号' },
                { field: 'msgId', title: '消息ID' },
                {
                    title: '消息动作', templet: function (e) {
                        var res = '-';
                        if (e.action == 'send')
                            res = '发送消息';
                        else if (e.action == 'recall')
                            res = '撤回消息';
                        else if (e.action == 'switch')
                            res = '切换企业';
                        return res;
                    }
                },
                { field: 'from', title: '消息发送方ID' },
                {
                    field: 'tolist', title: '消息接收方ID', templet: function (e) {
                        var tolist = '';
                        if (e.tolist != null && e.tolist.length > 0) {
                            for (var i = 0; i < e.tolist.length; i++) {
                                if (tolist != '')
                                    tolist += '，';
                                tolist += e.tolist[i];
                            }
                        }
                        return tolist;
                    }
                },
                {
                    title: '消息发送时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.msgTime);
                    }
                },
                { field: 'content', title: '发送内容' },
                {
                    field: 'createdAt', title: '创建时间', fixed: 'right', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                }
            ]);
        },
        /**
        * 绑定事件
        * */
        bindEvent: function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            //监听查询按钮
            layui.form.on('submit(chat-msg-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("chat-msg-list", {
                    where: field
                });
            });
        }
    }

    exports('chatMsg', func);
});