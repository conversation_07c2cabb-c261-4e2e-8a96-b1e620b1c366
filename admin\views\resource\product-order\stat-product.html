﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/lib/layui/css/modules/opTable.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .link { color: #50b1fb }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">

                    <div class="layui-inline" style="display:none;">
                        <label class="layui-form-label">产品</label>
                        <div class="layui-input-inline" style="width:300px;">
                            <select name="productId" id="product-view" lay-search>
                                <option value="">请选择购买产品</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">投资顾问</label>
                        <div class="layui-input-inline" style="width:300px;" id="teacher-item">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">查询时间</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="stat-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="stat-list" lay-filter="list"></table>
            </div>
        </div>
    </div>
    <script id="product-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.4"></script>
    <script type="text/javascript">
        var xmSel;
        layui.use(['form', 'laydate', 'common', 'productOrder'], function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime',
                value: new Date(new Date(new Date().toLocaleDateString()).getTime())
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime',
                value: new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
            });
            xmSel = layui.xmSelect.render({
                el: '#teacher-item',
                language: 'zn',
                filterable: true,
                tips: '请选择投资顾问',
                theme: { color: '#0081ff ' },
                data: [],
                toolbar: { show: true },
                autoRow: true,
            });
            layui.productOrder.initTeacher();
            layui.productOrder.initProduct2();
            layui.productOrder.queryStatForProduct();
            //监听查询按钮
            layui.form.on('submit(stat-search)', function (data) {
                layui.productOrder.queryStatForProduct();
            });
        })
    </script>
</body>
</html>