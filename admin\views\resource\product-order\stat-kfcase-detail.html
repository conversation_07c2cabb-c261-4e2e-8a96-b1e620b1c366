﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .link { color: #50b1fb }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">查询时间</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="stat-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="stat-kfcase-list" lay-filter="list"></table>
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.1"></script>
    <script type="text/javascript">
        layui.use(['form', 'laydate', 'common', 'productOrder'], function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'date',
                value: new Date(new Date(new Date().toLocaleDateString()).getTime())
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'date',
                value: new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
            });
            layui.productOrder.queryStatDetailForKfCase();
            //监听查询按钮
            layui.form.on('submit(stat-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("stat-kfcase-list", {
                    where: field
                });
            });
        })
    </script>
</body>
</html>