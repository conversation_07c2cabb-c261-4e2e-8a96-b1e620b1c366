﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">推广账户</label>
                            <div class="layui-input-inline" style="width:250px;">
                                <select name="advertiserId" id="advertiser-view" lay-search>
                                    <option value="">请选择推广账户</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">时间</label>
                            <div class="layui-input-inline">
                                <input id="startDate" name="startDate" type="text" class="layui-input" placeholder="开始时间" autocomplete="off" />
                            </div>
                            <div class="layui-input-inline">
                                <input id="endDate" name="endDate" type="text" class="layui-input" placeholder="结束时间" autocomplete="off" />
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="report-search">
                                <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                            </button>
                        </div>
                    </div>
                </div>
                <table id="report-list" lay-filter="list"></table>
                <script type="text/html" id="report-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>
    <script id="advertiser-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.accountId}}">{{item.accountName}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'laydate', 'oceanengineReport'], function () {
            layui.laydate.render({
                elem: '#startDate',
                type: 'date'
            });
            layui.laydate.render({
                elem: '#endDate',
                type: 'date'
            });
            layui.oceanengineReport.initAdvertiser();
            layui.oceanengineReport.query();

            layui.form.on('submit(report-search)', function (data) {
                if (data.field.advertiserId == '') {
                    data.field.advertiserId = -1;
                }
                //执行重载
                layui.tableRequest.reload("report-list", {
                    where: data.field,
                    page: {
                        curr: 1
                    }
                });
            });
        });
    </script>
</body>
</html>