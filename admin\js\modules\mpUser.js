﻿layui.define(['laytpl', 'request', 'tableRequest', 'common'], function (exports) {
    var func = {
        /**
         * 获取微信公众号用户列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'mp-user-list', '/admin/notification/mp-user/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'appId', title: '公众号ID' },
                { field: 'openId', title: '微信openid' },
                {
                    field: 'nickname', title: '微信昵称', minWidth: 180, templet: function (e) {
                        return '<a target="_blank" href="' + e.headImgUrl + '"><img src="' + e.headImgUrl + '" style="height:25px"/></a> ' + e.nickname;
                    }
                },
                //{ field: 'sex', title: '性别' },
                //{
                //    field: 'address', title: '所在地', templet: function (e) {
                //        var address = '-';
                //        if (e.province != '') {
                //            address = e.province;
                //        }
                //        if (e.city != '') {
                //            address += '-' + e.city;
                //        }
                //        return address;
                //    }
                //},
                {
                    title: '订阅状态', templet: function (e) {
                        return e.subscribeStatus == 1 ? '未订阅' : '已订阅';
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { field: 'notificationStatus', title: '推送状态', templet: '#switchTpl', unresize: true }
            ]);
        },
        /**
         * 编辑微信用户推送通知启用状态
         * @param {any} data
         */
        updateStatus: function (data) {
            console.log(data);
            layui.request({
                method: 'post',
                url: '/admin/notification/mp-user/notification-status/update',
                data: JSON.stringify({
                    id: data.value,
                    notificationStatus: data.elem.checked ? 1 : 2
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("mp-user-list");
                    layui.common.alertAutoClose("状态编辑成功");
                }
                else {
                    data.elem.checked = !data.elem.checked;
                    layui.form.render('checkbox');
                    layui.common.alert(res.message, 0);
                }
            })
        }
    }

    exports('mpUser', func);
});