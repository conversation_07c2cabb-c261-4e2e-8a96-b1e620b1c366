﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 80%; max-width: 900px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
        .tb { width: 500px; }
        .tb th { padding: 9px 0; text-align: left; }
        .tb td { padding: 5px 20px 5px 0; }
        .tb td a { margin-right: 10px; font-size: 16px; }
        .tb td a i { font-size: 22px; }
        .tag-con { display: inline-block; }
        .tag-con a { padding: 3px 8px; background: #0094ff; border-radius: 4px; color: #fff; margin-right: 5px; margin-bottom: 5px; display: inline-block; }
        .tag-con a i { font-size: 13px; margin-left: 5px; }
        a.select { display: inline-block; color: #0094ff }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 20px 0;">
                <div class="layui-form-item">
                    <label class="layui-form-label">所属企微</label>
                    <div class="layui-input-inline">
                        <select name="workWxAppId" id="workwx-app-view" lay-verify="required" lay-filter="workwx-app" lay-search></select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">类型</label>
                    <div class="layui-input-inline">
                        <input type="radio" name="type" value="1" title="渠道活码(可提供给第三方系统使用)" checked>
                        <input type="radio" name="type" value="2" title="引流活码(仅限自研系统落地页使用)">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">分配上限</label>
                    <div class="layui-input-inline">
                        <input type="radio" name="statisticsMethods" value="1" title="按天统计" checked>
                        <input type="radio" name="statisticsMethods" value="2" title="汇总统计">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">分组</label>
                    <div class="layui-input-inline">
                        <select name="groupId" id="group-view" lay-search></select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-inline">
                        <input type="text" name="name" lay-verify="required" placeholder="请输入企微活码名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">员工</label>
                    <div class="layui-input-inline">
                        <table class="tb">
                            <thead><tr><th style="width:250px;">分配员工</th><th>人数上限</th><th style="width:100px;">操作</th></tr></thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <select name="workwxuser" class="item workwxuser" lay-verify="required" lay-search>
                                            <option value="">请选择员工</option>
                                        </select>
                                    </td>
                                    <td> <input type="text" name="maxcount" lay-verify="required|number" placeholder="人数上限" autocomplete="off" class="layui-input item maxcount"></td>
                                    <td>
                                        <a href="javascript:;" class="remove-item"><i class="layui-icon">&#xe640;</i></a>
                                    </td>
                                </tr>
                            </tbody>
                            <tfoot>

                                <tr><td><button type="button" class="layui-btn layui-btn layui-btn-sm layui-btn-primary add-item"> <i class="layui-icon">&#xe654;</i>添加</button></td></tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">备用员工</label>
                    <div class="layui-input-inline">
                        <div id="spareServices"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标签</label>
                    <div class="layui-input-inline" style="padding:9px 0">
                        <div class="tag-con" id="corp-tag">
                        </div>
                        <a href="javascript:;" class="select" data-type="1"><i class="layui-icon">&#xe654;</i>选择标签</a>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">锁客标签</label>
                    <div class="layui-input-inline" style="padding:9px 0">
                        <div class="tag-con" id="look-corp-tag">
                        </div>
                        <a href="javascript:;" class="select" data-type="2"><i class="layui-icon">&#xe654;</i>选择标签</a>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-inline">
                        <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="qrcode-submit" value="确认添加">
                        <input type="button" class="layui-btn layui-btn-primary" onclick="location.href ='list.html'" value="返回上一页">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="group-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        var workWxUsers = [];
        var xmSelUser;
        layui.use(['qrcode'], function () {
            layui.qrcode.initApp();
            layui.qrcode.initGroupForOp();
            //企微应用选择后，加载账号列表
            layui.form.on('select(workwx-app)', function (data) {
                layui.qrcode.initWorkWxUser(data.value);
            });
            //添加行
            layui.$('.add-item').click(function () {
                var tr = ' <tr><td> <select name="workwxuser" class="item workwxuser" lay-verify="required" lay-search>';
                tr += '<option value="">请选择员工</option>';
                if (workWxUsers.length > 0) {
                    for (var j = 0; j < workWxUsers.length; j++) {
                        tr += '<option value="' + workWxUsers[j].userId + '">' + workWxUsers[j].name + '</option>'
                    }
                }
                tr += '</select></td>';
                tr += '<td> <input type="text" name="maxcount" lay-verify="required|number" placeholder="人数上限" autocomplete="off" class="layui-input item maxcount"></td>';
                tr += '<td><a href="javascript:;" class="remove-item"><i class="layui-icon">&#xe640;</i></a></td></tr>';
                layui.$('.tb tbody').append(tr);
                layui.form.render('select');
            });
            //删除行
            layui.$(document).on("click", '.remove-item', function () {
                if (layui.$('.workwxuser').length <= 1) {
                    layui.common.alertAutoClose("至少保留1个员工");
                }
                else {
                    layui.$(this).parent().parent('tr').remove();
                }
            });
            //删除标签
            layui.$(document).on("click", '.remove-tag', function () {
                layui.$(this).parent('a').remove();
            });
            //初始化
            xmSelUser = layui.xmSelect.render({
                el: '#spareServices',
                language: 'zn',
                filterable: true,
                tips: '请选择备用员工',
                theme: { color: '#0081ff ' },
                data: [],
                toolbar: { show: true },
                autoRow: true,
            });
            layui.$('.select').click(function () {
                var type = layui.$(this).data('type');
                if (layui.$('#workwx-app-view').val() == '') {
                    layui.common.alertAutoClose("请选择所属企微");
                    return;
                }
                layui.common.openIframe('选择企业标签', 700, 600, 'select-tag.html?type=' + type);
            });
            layui.form.on('submit(qrcode-submit)', function (data) {
                //员工
                var services = [];
                var serviceTrList = layui.$('.tb tbody tr');
                if (serviceTrList.length > 0) {
                    for (var i = 0; i < serviceTrList.length; i++) {
                        services.push({
                            userId: serviceTrList.eq(i).find('.workwxuser').val(), userName: serviceTrList.eq(i).find('.workwxuser option:selected').text(), maxCount: serviceTrList.eq(i).find('.maxcount').val()
                        })
                    }
                }
                data.field.services = services;
                //备用员工
                var spareServiceList = xmSelUser.getValue();
                var spareServices = [];
                if (spareServiceList.length > 0) {
                    for (var i = 0; i < spareServiceList.length; i++) {
                        spareServices.push({
                            userId: spareServiceList[i].value, userName: spareServiceList[i].name, maxCount: 100000
                        })
                    }
                }
                data.field.spareServices = spareServices;
                //标签
                var corpTags = [];
                var corpTagsList = layui.$('#corp-tag a');
                if (corpTagsList.length > 0) {
                    for (var i = 0; i < corpTagsList.length; i++) {
                        corpTags.push({
                            groupName: corpTagsList.eq(i).data('groupname'), tagId: corpTagsList.eq(i).data('id'), tagName: corpTagsList.eq(i).data('name')
                        })
                    }
                }
                data.field.corpTags = corpTags;
                //锁客标签
                var lookCorpTags = null;
                var lookCorpTagsList = layui.$('#look-corp-tag a');
                if (lookCorpTagsList.length > 0) {
                    lookCorpTags = {
                        groupName: lookCorpTagsList.eq(0).data('groupname'), tagId: lookCorpTagsList.eq(0).data('id'), tagName: lookCorpTagsList.eq(0).data('name')
                    }
                }
                data.field.lookCorpTags = lookCorpTags;
                layui.qrcode.createQrCode(data);
                return false; //阻止表单跳转
            });
        });
    </script>
</body>
</html>