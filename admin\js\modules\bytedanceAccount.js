﻿layui.define(['laytpl', 'request', 'tableRequest', 'common', 'workWxApp'], function (exports) {
    var func = {
        /**
         * 渲染企微应用下拉选框
         * */
        initApp: function (id) {
            layui.workWxApp.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企微' });
                var getTpl = document.getElementById("workwx-app-tpl").innerHTML
                    , view = document.getElementById('workwx-app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('workwx-app-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过id获取字节推广账户
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/bytedance-account/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=accountId]').val(res.result.accountId);
                    layui.$('input[name=clueAccountId]').val(res.result.clueAccountId);
                    layui.$('input[name=token]').val(res.result.token);
                    layui.$('input[name=reportUrl]').val(res.result.reportUrl);
                    layui.bytedanceAccount.initApp(res.result.workWxAppId);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取字节推广账户列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'bytedance-account-list', '/admin/workwx/bytedance-account/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'workWxAppName', title: '所属企微' },
                { field: 'name', title: '推广账户名称' },
                { field: 'accountId', title: '推广账户ID' },
                { field: 'clueAccountId', title: '推广账户线索ID' },
                { field: 'token', title: '线索回传Token' },
                { field: 'reportUrl', title: '线索回传地址' },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 150, align: 'left', toolbar: '#bytedance-account-bar' }
            ]);
            //监听表格事件
            layui.bytedanceAccount.tableEvent();
        },
        /**
        * 创建字节推广账户
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/bytedance-account/create',
                data: JSON.stringify({
                    name: data.field.name,
                    workWxAppId: data.field.workWxAppId,
                    accountId: data.field.accountId,
                    clueAccountId: data.field.clueAccountId,
                    token: data.field.token,
                    reportUrl: data.field.reportUrl
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("bytedance-account-list");
                    parent.layui.common.alertAutoClose("字节推广账户创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑字节推广账户
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/bytedance-account/update',
                data: JSON.stringify({
                    id: data.field.id,
                    name: data.field.name,
                    accountId: data.field.accountId,
                    clueAccountId: data.field.clueAccountId,
                    token: data.field.token,
                    reportUrl: data.field.reportUrl,
                    workWxAppId: data.field.workWxAppId
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("bytedance-account-list");
                    parent.layui.common.alertAutoClose("字节推广账户编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除字节推广账户
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/bytedance-account/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("bytedance-account-list");
                    layui.common.alertAutoClose("字节推广账户删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑字节推广账户', 650, 480, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('删除字节推广账户后，转化回传将失效，确定删除该账户吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.bytedanceAccount.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('bytedanceAccount', func);
});