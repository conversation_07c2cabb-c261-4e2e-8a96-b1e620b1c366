﻿layui.define(['layer', 'setter', 'axios', 'common'], function (exports) {
    var setter = layui.setter;
    var layer = layui.layer;
    var axios = layui.axios;
    const instance = axios.create({
        timeout: 60000
    });
    instance.interceptors.request.use(
        config => {
            if (config.lodding != false) {
                layer.load(1, {
                    shade: [0.1, '#fff'] //0.1透明度的白色背景
                });
            }
            config.headers[setter.request.tokenName] = layui.common.getCookie(setter.request.tokenName);
            var orderToken = localStorage.getItem('x-order-token');
            if (orderToken != '') {
                config.headers['x-order-token'] = orderToken
            }
            var userStr = localStorage.getItem('ly-admin-user');
            if (userStr != null) {
                var userObj = JSON.parse(userStr);
                config.headers['uid'] = userObj.id;
            }

            if (config.requestBase == "sso") {
                config.baseURL = setter.request.ssoBaseUrl;
            }
            else if (config.requestBase == "form") {
                config.baseURL = setter.request.formBaseUrl;
            }
            else if (config.requestBase == "log") {
                config.baseURL = setter.request.logBaseUrl;
            }
            else if (config.requestBase == "pay") {
                config.baseURL = setter.request.payBaseUrl;
            }
            else {
                config.baseURL = setter.request.resourceBaseUrl
            }
            return config
        },
        error => {
            // do something with request error
            layer.msg(error.message, { time: 3000, icon: 2 });
            return error
        }
    );

    // response interceptor
    instance.interceptors.response.use(
        response => {
            layer.closeAll('loading');
            var appId = layui.common.getUrlParam('appId') || '';
            const res = response.data
            // if the custom code is not 20000, it is judged as an error.
            if (!res.isSuccess && res.code == 22000) {
                layui.common.delCookie(setter.request.tokenName);
                var loginUrl = '/admin/login.html';
                if (appId != '') {
                    loginUrl += '?appId=' + appId;
                }
                location.href = loginUrl;
                //换取新的token
                //Message({
                //    message: res.message || 'Error',
                //    type: 'error',
                //    duration: 5 * 1000
                //})

                //// 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
                //if (res.code === 22000) {
                //    // to re-login
                //    store.dispatch('user/resetToken').then(() => {
                //        location.reload()
                //    })
                //}
                //return Promise.reject(new Error(res.message || 'Error'))
            } else {
                return res
            }
        },
        error => {
            layer.msg(error.message, { time: 3000, icon: 2 });
            return error
        }
    );

    //输出 request 接口
    exports('request', instance);
});