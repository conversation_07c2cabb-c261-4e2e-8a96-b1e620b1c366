﻿layui.define(['jquery', 'layer', 'setter', 'request', 'common', 'seats', 'systemNotice'], function (exports) {
    var $ = layui.jquery;
    var setter = layui.setter;
    var title = setter.name;
    $("#siteName").text(title);
    if (setter.logo != '') {
        //$("#siteLogo").attr('src', setter.logo);
    };
    document.title = title;
    var token = layui.common.getCookie(layui.setter.request.tokenName);
    if (token == null) {
        var appId = layui.common.getUrlParam('appId') || '';
        var loginUrl = '/admin/login.html';
        if (loginUrl != '') {
            loginUrl += '?appId=' + appId
        }
        location.href = loginUrl;
    }
    var ippbxAccount = '';
    var ippbxPassword = '';
    //获取用户信息
    var func = {
        /**
         * 初始化云呼叫中心
         * */
        initAliCloudCallCenter: function () {
            layui.seats.isExist(function (res) {
                if (res.result != null && res.result == 'true') {
                    var headers = {};
                    headers[layui.setter.request.tokenName] = layui.common.getCookie(layui.setter.request.tokenName);
                    window.workbench = window.CCCWorkbenchSDK.BroadcastChannelMain({
                        header: false,
                        instanceId: layui.setter.aliCloudCallInstanceId,
                        dom: "workbench-container",
                        ajaxPath: "/admin/callcenter/alicloud",
                        ajaxMethod: "post",
                        ajaxOrigin: layui.setter.request.resourceBaseUrl,
                        ajaxHeaders: headers,
                        afterCallRule: 5, //挂机后的话后处理时间
                        useLocalStorageToCall: true,
                        exportErrorOfApi: true,
                        mainContentVisible: false,
                        allowCalleeEncryption: true,
                        functionSwitch: {
                            closeable: false,
                            //allowCallOutbound: false,
                            allowStartConference: false,
                            showFlashSmsConfig: false,
                            allowCreateTicket: false,
                            allowCreateSessionSummary: false,
                        },
                        onInit() {
                            //获取所有技能组
                            var getSkillGroupsRes = window.workbench.getSkillGroups();
                            //自动上线
                            window.workbench.register({ currentSkillGroups: getSkillGroupsRes.allSkillGroupList })
                        },
                        onLogIn() {
                            // window.workbench.renderIM(); // 渲染IM组件
                        },
                        onLogOut() {
                            console.log('onLogOut');
                        },
                        onErrorNotify(error) {
                            console.warn(error);
                        },
                        onCallComing(event) {
                            //显示弹框
                            window.workbench.changeUIConfig({ mainContentVisible: true });
                            //console.log(event);
                        },
                        onCallDialing(event) {
                            //console.log(event);
                        },
                        onStatusChange(event) {
                            //console.log(event);
                        },
                        onCallEstablish(event) {
                            //console.log("这里是通话建立时触发的回调函数", event);
                        },
                        onCallRelease(event) {
                            //console.log("这里是通话结束时触发的回调函数", event);
                        },
                        // onBeforeCallHangup(event) {
                        //   console.log(event);
                        //   event.callback(); // callback 必须调用
                        // },
                        onHangUp(type) {
                            //console.log(`这里是onHangUp事件，type = ${type}`);
                        },
                    });
                }
            })
        },
        /**
         * 获取用户信息
         * */
        getUserInfo: function () {
            layui.request({
                requestBase: 'sso',
                method: 'post',
                url: '/admin/sso/user/by-token/get',
            }).then(function (res) {
                if (res.isSuccess) {
                    $('#admin-username').text(res.result.name);
                    localStorage.setItem('ly-admin-user', JSON.stringify(res.result));
                    layui.index.getPermission(res.result.id);
                    if (!res.result.isResetPwd) {
                        layer.alert('当前账号密码为初始密码，为了你的账号安全，请前往修改后使用', {
                            shadeClose: true,
                            icon: 0,
                            btn: ['前往修改'],
                            closeBtn: 0,
                            shadeClose: false,
                            btn1: function () {
                                layui.common.openIframe('修改密码', 680, 350, 'update-Password.html');
                            },
                        });
                    }
                    else {
                        //if (layui.setter.callConfig.isOpend) {
                        //    layui.index.initSeats(res.result.id);
                        //}
                        //else {
                        //    layui.$('#seat-item').hide();
                        //}
                    }
                    // 调用生成画布
                    var adminUser = localStorage.getItem('ly-admin-user');
                    var obj = JSON.parse(adminUser);
                    canvasWM(obj.name + '（' + obj.encAccount + '）');
                    layui.$(window).resize(function () {
                        layui.$('#LAY_app_body_canvas').html('');
                        canvasWM(obj.name + '（' + obj.encAccount + '）');
                    });
                    setInterval(function () {
                        layui.$('#LAY_app_body_canvas').html('');
                        canvasWM(obj.name + '（' + obj.encAccount + '）');
                    }, 60000);

                    //站内信通知
                    console.log('浏览器通知状态：' + Notification.permission);
                    console.log('default=待授权，granted=允许通知，denied=拒绝通知');

                    if (Notification.permission != "granted") {
                        // 请求授权
                        Notification.requestPermission().then(function (permission) { });
                    }
                    layui.systemNotice.queryUnRead();
                    setInterval(function () { layui.systemNotice.queryUnRead(); }, 20000)
                }
                else {
                    var appId = layui.common.getUrlParam('appId') || '';
                    var loginUrl = '/admin/login.html';
                    if (loginUrl != '') {
                        loginUrl += '?appId=' + appId
                    }
                    location.href = loginUrl;
                }
            })
        },

        /**
         * 获取用户权限
         * @param {any} uid
         */
        getPermission: function (uid) {
            var appId = layui.common.getUrlParam('appId') || '';
            layui.request({
                requestBase: 'sso',
                method: 'post',
                url: '/admin/sso/user/permission/get',
                data: JSON.stringify({
                    appId: appId,
                    userId: uid,
                    excludeMenuIds: layui.setter.excludeMenuIds
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$('.init-lodding').hide();
                if (res.isSuccess) {
                    if (res.result.curentAppInfo != null && res.result.curentAppInfo.name != '') {
                        //$("#siteName").text(res.result.curentAppInfo.name);
                        localStorage.setItem('ly-admin-p-' + res.result.curentAppInfo.id, JSON.stringify(res.result.permissions));
                        if (res.result.curentAppInfo.id == '6369c6efd544954399357c36') {
                            //加载阿里云外呼
                            layui.index.initAliCloudCallCenter();
                        }
                        //layui.common.setCookie('ly-admin-p-' + res.result.curentAppInfo.id, JSON.stringify(res.result.permissions));
                    }
                    //应用
                    if (res.result.apps.length > 0) {
                        if (res.result.apps.length > 1) {
                            for (var i = 0; i < res.result.apps.length; i++) {
                                layui.$('#nav-menu').append('<li style="margin:0 5px" class="layui-nav-item ' +
                                    (appId == res.result.apps[i].id ? 'layui-this' : '') +
                                    '"><a href="index.html?appId=' + res.result.apps[i].id + '">' +
                                    res.result.apps[i].name + '</a></li>');
                            }
                        }

                        //var getTopTpl = document.getElementById("top-menu-tpl").innerHTML
                        //    , topView = document.getElementById('LAY-system-top-menu');
                        //layui.laytpl(getTopTpl).render(res.result.apps, function (html) {
                        //    topView.innerHTML = html;
                        //});
                    }
                    else {
                        layui.$('#LAY-system-top-menu').parent().hide();
                    }
                    //菜单
                    var getTpl = document.getElementById("left-menu-tpl").innerHTML
                        , view = document.getElementById('LAY-system-side-menu');
                    layui.laytpl(getTpl).render(res.result.menus, function (html) {
                        view.innerHTML = html;
                    });

                    layui.$('#LAY-system-side-menu .layui-nav-child dd').bind('click', function () {
                        $('#LAY-system-side-menu .layui-nav-child dd').removeClass('layui-this');
                        $(this).addClass('layui-this');
                    });
                    //切换一级选项卡
                    layui.$(".layui-nav-left li").eq(0).addClass("layui-nav-itemed");
                    layui.$(".layui-nav-left li").click(function () {
                        layui.$(".layui-nav-left li").siblings(".layui-nav-itemed").removeClass("layui-nav-itemed");
                        layui.$(this).addClass("layui-nav-itemed");
                    });
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 退出登录
         * */
        loginOut: function () {
            var confirmIndex = layui.layer.confirm('确定退出登录吗？', {
                icon: 3,
                title: '提示',
                btn: ['确定', '取消']
            }, function () {
                layui.request({
                    requestBase: 'sso',
                    method: 'post',
                    url: '/admin/sso/user/login-out',
                }).then(function (res) {
                    if (res.isSuccess) {
                        var cookies = document.cookie.split("; ");
                        for (var c = 0; c < cookies.length; c++) {
                            document.cookie = cookies[c] + '=0;path=/;expires=' + new Date(0).toUTCString()
                        }
                        localStorage.clear();
                        location.reload();
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            }, function () {
                layui.layer.close(confirmIndex);
            });
        },
        /**
        * 通话记录绑定订单
        * */
        callRecordBindOrder: function (orderId, guid) {
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/product/order/call-guid/update',
                data: JSON.stringify({ id: orderId, callGuid: guid }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                console.log('绑定订单完成，返回结果：' + JSON.stringify(res));
            })
        },
        /**
         * 初始化坐席状态
         * @param {any} id
         */
        initSeats: function (id) {
            layui.seats.getByUserId(id, function (res) {
                if (res.result != null && res.result.account != '' && res.result.password != '') {
                    ippbxAccount = res.result.account;
                    ippbxPassword = res.result.password;
                    layui.index.ippbxInitURL();
                    url_SetIP(0, layui.setter.callConfig.ip, layui.setter.callConfig.port);
                    url_SetAjaxTimeout(0, 3000);//修改ajax超时3秒
                    //查询服务器是否网络正常
                    url_DoEcho(0, "ping", 0);
                }
                else {
                    layui.$('#seats-status').text('未配置');
                }
            })
        },
        /**
         * 初始化全局参数
         * */
        ippbxInitURL: function () {
            //初始化最大监控分机数量为1, 可以设置监控分机相关参数的通道可以为0,1
            url_InitListCount(1);
            //设置操作命令结果回调js函数
            url_SetCmdCallback(layui.index.ippbxCmdJsonCallback);
            //设置分机事件回调js函数
            url_SetEventCallback(layui.index.ippbxEventJsonCallback);
        },
        /**
         * 设置分机参数
         * @param {any} ip
         * @param {any} port
         * @param {any} from
         * @param {any} pwd
         */
        ippbxSetParam: function (ip, port, from, pwd) {
            //layui.index.ippbxInitURL();
            //var account = from.split(',');
            //var pwds;
            //if (undefined != pwd) {
            //    pwds = pwd.split(',');
            //}

            url_SetIP(0, ip, port);
            url_SetFrom(0, from, pwd);
            url_QueryAccount(0);
            url_StartEvent();

            url_ExeclljPhone();
            url_SetSipIP(0, ip, layui.setter.callConfig.registerPort);
            url_SetExtIP(0, '127.0.0.1', "9935");//此为固定项，默认不变更，特殊情况除外
            //url_EnableEvent(0);
            url_DoLogin(0);
        },
        /**
         * 接收到的回调事件信息
         * @param {any} tagaccount
         * @param {any} cmd
         * @param {any} url
         * @param {any} jsonObj
         */
        ippbxCmdJsonCallback: function (tagaccount, cmd, url, jsonObj) {
            if (cmd == u_cmd_doecho) {
                if (jsonObj.ret > 0) {
                    layui.$('#seats-status').text('连接服务器网络正常');
                    console.log("Echo连接服务器网络正常");
                    layui.index.ippbxSetParam(layui.setter.callConfig.ip, layui.setter.callConfig.port, ippbxAccount, ippbxPassword);
                }
                else {
                    layui.$('#seats-status').text('连接服务器失败');
                    console.error("Echo**Echo连接服务器失败**");
                }
            }
            else if (cmd == u_cmd_querylastevent) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("最后状态获取失败");
                    console.error("查询节点DTMFCmd失败:" + url)
                }
                else {
                    console.log("查询节点DTMFCmd成功:" + url_GetJsonString(jsonObj))
                }
            }
            else if (cmd == u_cmd_querydtmfcmd) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("查询节点DTMFCmd失败");
                    console.error("查询节点DTMFCmd失败:" + url);
                }
                else {
                    if (jsonObj.ret <= 0) {
                        layui.$('#seats-status').text("获取节点DTMFCmd错误");
                        console.error("获取节点DTMFCmd错误:" + tagaccount.m_from);
                    }
                    else {
                        console.log("[" + tagaccount.m_from + "]" + "获取节点DTMFCmd信息: itemname:" + jsonObj.itemname + " dtmfcmd:" + jsonObj.dtmfcmd);
                    }
                }
            }
            else if (cmd == u_cmd_dogoivr) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("跳转IVR失败");
                    console.error("跳转IVR失败:" + url);
                }
                else {
                    if (jsonObj.ret <= 0) {
                        layui.$('#seats-status').text("跳转IVR错误");
                        console.error("跳转IVR错误:" + tagaccount.m_from);
                    }
                    else {
                        console.log("[" + tagaccount.m_from + "]" + "跳转IVR完成");
                    }
                }
            }
            else if (cmd == u_cmd_queryaccount) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("获取账号状态失败");
                    console.error("获取账号状态失败:" + url);
                }
                else {
                    if (jsonObj.ret <= 0) {
                        layui.$('#seats-status').text("获取数据错误");
                        console.error("获取数据错误:" + tagaccount.m_from);
                    }
                    else {
                        layui.$('#seats-status').text("[" + jsonObj.list[0].account + "]已登录");
                        console.log("获取账号信息:" + jsonObj.list[0].account + " online:" + jsonObj.list[0].online + " dobusy:" + jsonObj.list[0].dobusy);
                    }
                }
            }
            else if (cmd == u_cmd_clearevent) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("清除事件执行失败");
                    console.error("清除事件执行失败:" + url)
                }
                else {
                    layui.$('#seats-status').text("清除事件执行完成");
                    console.log("清除事件执行完成:" + jsonObj.ret);
                }
            }
            else if (cmd == u_cmd_setuploadparam) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("设置参数失败");
                    console.error("设置参数失败:" + url)
                }
                else {
                    layui.$('#seats-status').text("设置参数完成");
                    console.log("设置参数完成:" + jsonObj.ret)
                }
            }
            else if (cmd == u_cmd_dohang) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("挂机执行失败");
                    console.error("挂机执行失败:" + url);
                }
                else {
                    url_SetGuid(tagaccount.m_id, '');
                    layui.$('#seats-status').text("挂机完成");
                    console.log("挂机完成:" + jsonObj.ret);
                }
            }
            else if (cmd == u_cmd_dohangex) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("强拆执行失败");
                    console.error("强拆执行失败:" + url);
                }
                else {
                    url_SetGuid(tagaccount.m_id, '');
                    layui.$('#seats-status').text("强拆完成");
                    console.log("强拆完成:" + jsonObj.ret);
                }
            }
            else if (cmd == u_cmd_dohold) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("保持执行失败");
                    console.error("保持执行失败:" + url);
                }
                else {
                    layui.$('#seats-status').text("保持完成");
                    console.log("保持完成:" + jsonObj.ret);
                }
            }
            else if (cmd == u_cmd_doinsert) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("强插执行失败");
                    console.error("强插执行失败:" + url);
                }

                else {
                    layui.$('#seats-status').text("强插完成");
                    console.log("强插完成:" + jsonObj.ret);
                }
            }
            else if (cmd == u_cmd_domonitor) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("监听执行失败");
                    console.error("监听执行失败:" + url);
                }
                else {
                    layui.$('#seats-status').text("监听完成");
                    console.log("监听完成:" + jsonObj.ret);
                }
            }
            else if (cmd == u_cmd_doinvite) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("三方邀请呼叫失败");
                    console.error("三方邀请呼叫失败:" + url);
                }
                else {
                    if (jsonObj.ret > 0) {
                        layui.$('#seats-status').text("三方邀请完成");
                        console.log("三方邀请完成:" + jsonObj.ret + "  newguid=" + jsonObj.newguid);
                    }
                    else {
                        layui.$('#seats-status').text("三方邀请呼叫失败");
                        console.error("三方邀请呼叫失败:" + jsonObj.ret);
                    }
                }
            }
            else if (cmd == u_cmd_send_dtmf) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("二次拨号失败");
                    layui.error("二次拨号失败:" + url);
                }
                else {
                    if (jsonObj.ret > 0) {
                        layui.$('#seats-status').text("二次拨号完成");
                        layui.log("二次拨号完成: dtmf=" + jsonObj.dtmf);
                    }
                    else {
                        layui.$('#seats-status').text("二次拨号失败");
                        layui.error("二次拨号失败:ret=" + jsonObj.ret + "  dtmf=" + jsonObj.dtmf);
                    }
                }
            }
            else if (cmd == u_cmd_call) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("外线呼叫执行失败");
                    console.error("外线呼叫执行失败:" + url);
                }
                else {
                    if (jsonObj.ret == 1) {
                        //保存guid，并监控该guid呼叫状态
                        url_SetGuid(tagaccount.m_id, jsonObj.guid);
                        layui.$('#seats-status').text("开始外线呼叫");
                        console.log("开始外线呼叫");
                    }
                    else {
                        layui.$('#seats-status').text("外线呼叫失败");
                        console.error("外线呼叫失败:" + jsonObj.ret + " from:" + jsonObj.from + " to:" + jsonObj.to);
                    }
                }
            }
            else if (cmd == u_cmd_dial) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("分机拨号执行失败");
                    console.error("分机拨号执行失败:" + url);
                }
                else {
                    if (jsonObj.ret == 1) {
                        url_SetGuid(tagaccount.m_id, jsonObj.guid);
                        layui.$('#seats-status').text("分机开始呼叫");
                        console.log("分机开始呼叫");
                    }
                    else {
                        layui.$('#seats-status').text("分机呼叫失败");
                        console.error("分机呼叫失败:" + jsonObj.ret + " from:" + jsonObj.from + " to:" + jsonObj.to);
                    }
                }
            }
            else if (cmd == u_cmd_dobusy || cmd == u_cmd_dobusyex) {
                if (url_IsAjaxErrObj(jsonObj)) AppendStatus("示忙执行失败:" + url);
                else {
                    if (jsonObj.ret == 1 && jsonObj.busy > 0) {
                        layui.$('#seats-status').text("执行示忙成功");
                        console.log("执行示忙成功:" + jsonObj.from);
                    }
                    else if (jsonObj.ret == 0 && jsonObj.busy > 0) {
                        layui.$('#seats-status').text("执行示忙失败");
                        console.error("执行示忙失败:" + jsonObj.from);
                    }
                    else if (jsonObj.ret == 1 && jsonObj.busy == 0) {
                        layui.$('#seats-status').text("执行示闲成功");
                        console.log("执行示闲成功:" + jsonObj.from);
                    }
                    else if (jsonObj.ret == 0 && jsonObj.busy == 0) {
                        layui.$('#seats-status').text("执行示闲失败");
                        console.error("执行示闲失败:" + jsonObj.from);
                    }
                }
            }

            else if (cmd == u_cmd_queryaccountex) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("获取账号状态失败");
                    console.error("获取账号状态失败:" + url);
                }
                else {
                    if (jsonObj.ret <= 0) {
                        layui.$('#seats-status').text("获取数据错误");
                        console.error("获取数据错误:" + tagaccount.m_from);
                    }
                    else {
                        var objToStr = JSON.stringify(jsonObj.list);
                        layui.$('#seats-status').text("分机状态:" + objToStr);
                        console.log("分机状态:" + objToStr);
                        if (jsonObj.list.length == 1) {
                            if (undefined != jsonObj.list[0].guid) {
                                layui.$('#seats-status').text("通道GUID：" + jsonObj.list[0].guid);
                                console.log("通道GUID：" + jsonObj.list[0].guid);
                            }
                        }
                    }
                }
            }
            else if (cmd == u_cmd_group_querygroup) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("获取技能组失败");
                    console.error("获取技能组失败:" + url);
                }
                else {
                    if (jsonObj.ret <= 0) {
                        layui.$('#seats-status').text("获取技能组错误");
                        console.error("获取技能组错误:" + tagaccount.m_from);
                    }
                    else {
                        var objToStr = JSON.stringify(jsonObj.list)
                        layui.$('#seats-status').text("获取技能组成功");
                        console.log("获取技能组信息:" + objToStr);
                    }
                }
            }
            else if (cmd == u_cmd_group_querywaitin) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("获取技能组排队信息失败");
                    console.error("获取技能组排队信息失败:" + url);
                }
                else {
                    if (jsonObj.ret <= 0) {
                        layui.$('#seats-status').text("获取技能组排队信息错误");
                        console.error("获取技能组排队信息错误:" + tagaccount.m_from);
                    }
                    else {
                        var objToStr = JSON.stringify(jsonObj.list)
                        layui.$('#seats-status').text("获取技能组排队数量:" + objToStr);
                        console.log("获取技能组排队数量:" + objToStr);
                        if (undefined != groupwaitin[j].list) {
                            var objToStr1 = JSON.stringify(jsonObj.list[0].list)
                            console.log("获取技能组排队信息:" + objToStr1);
                        }
                    }
                }
            }
            else if (cmd == u_cmd_group_queryaccount) {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("获取技能组成员失败");
                    console.error("获取技能组成员失败:" + url);
                }
                else {
                    if (jsonObj.ret <= 0) {
                        layui.$('#seats-status').text("获取技能组成员错误");
                        console.error("获取技能组成员错误:" + tagaccount.m_from);
                    }
                    else {
                        var objToStr = JSON.stringify(jsonObj.list);
                        layui.$('#seats-status').text("获取技能组成员完成");
                        console.error("获取技能组成员状态信息:" + objToStr);
                    }
                }
            }
            else {
                if (url_IsAjaxErrObj(jsonObj)) {
                    layui.$('#seats-status').text("执行命令失败");
                    console.error("执行命令失败:" + url);
                }
                else {
                    layui.$('#seats-status').text("执行命令完成");
                    console.log("执行命令完成: cmd:" + cmd + " url:" + url + " " + url_GetJsonString(jsonObj));
                }
            }
        },
        /**
         * 接收分机事件回调
         * @param {any} tagaccount
         * @param {any} url
         * @param {any} ret
         * @param {any} eventObj
         */
        ippbxEventJsonCallback: function (tagaccount, url, ret, eventObj) {
            if (url_IsAjaxErrRet(ret)) {
                //当前没有事件
                return;
            }
            else if (ret < 0) {
                if (tagaccount.m_login == 0) {
                    layui.$('#seats-status').text('账号未登陆');
                    console.error("账号未登陆:" + tagaccount.m_from + " url:" + url);
                }
                else {
                    layui.$('#seats-status').text('获取事件失败');
                    console.error("获取事件失败:" + ret);
                }
                return;
            }
            else if (ret == 0) {
                //没有事件,等候...
                return;
            }
            else {
                if (eventObj.type == OSIPSEVENT_CALLIN
                    || eventObj.type == OSIPSEVENT_CALLOUT) {
                    if (ACCOUNT_SRCLINE_FXO == eventObj.acctype) {
                        //处理中继的呼叫
                        if (eventObj.type == OSIPSEVENT_CALLIN) {
                            layui.index.ippbxFxoEventCallin(tagaccount, eventObj);
                        }
                        else if (eventObj.type == OSIPSEVENT_CALLOUT) {
                            layui.index.ippbxFxoEventCallout(tagaccount, eventObj)
                        };
                    }
                    else {
                        switch (eventObj.uctype) {
                            case 11:
                                {
                                    layui.index.ippbxEventCallin(tagaccount, eventObj);
                                } break;
                            case 12:
                                {
                                    layui.index.ippbxEventCallout(tagaccount, eventObj);
                                } break;
                            case 13:
                                {
                                    layui.index.ippbxEventCallext(tagaccount, eventObj);
                                } break;
                            case 30:
                                {
                                    layui.index.ippbxEventCalloutAct(tagaccount, eventObj);
                                } break;
                            default: break;
                        }
                    }
                }
                else if (eventObj.type == OSIPSEVENT_LOGIN) {
                    layui.$('#seats-status').text('[' + tagaccount.m_from + ']已登录');
                    console.log("账号在线..." + tagaccount.m_from);
                }
                else if (eventObj.type == OSIPSEVENT_LOGOUT) {
                    layui.$('#seats-status').text('[' + tagaccount.m_from + ']已离线');
                    console.log("账号离线..." + tagaccount.m_from);
                }
                else if (eventObj.type == OSIPSEVENT_UPDATESTATE) {
                    if (eventObj.result == OSIPS_STATE_BUSY) {
                        if (eventObj.param > 0) {
                            console.log("[" + tagaccount.m_from + "]示忙...");
                        }
                        else {
                            console.log("[" + tagaccount.m_from + "]示闲...");
                        }
                    }
                    else if (eventObj.result == OSIPS_STATE_MIC) {
                        console.log("[" + tagaccount.m_from + "-guid:" + eventObj.guid + "]MIC..." + eventObj.param);
                    }
                    else if (eventObj.result == OSIPS_STATE_SPK) {
                        console.log("[" + tagaccount.m_from + "-guid:" + eventObj.guid + "]SPK..." + eventObj.param);
                    }
                }
                else if (eventObj.type == OSIPSEVENT_RECVDTMF) {
                    console.log("接收到按键：" + eventObj.dtmf);
                }
                else if (eventObj.type == OSIPSEVENT_OWNERIAT || eventObj.type == OSIPSEVENT_PEERIAT) {
                    var v;
                    if (eventObj.type == OSIPSEVENT_OWNERIAT) v = "本方";
                    else v = "对方";

                    if (eventObj.result == AI_EVENT_STATE) {
                        if (eventObj.param == AI_STATE_IDLE) console.log(v + "识别语音空闲中：" + eventObj.account);
                        else if (eventObj.param == AI_STATE_READY) console.log(v + "识别语音准备中，等待激活：" + eventObj.account);
                        else if (eventObj.param == AI_STATE_WORKING) console.log(v + "正在识别语音中..." + eventObj.account);
                    }
                    else if (eventObj.result == AI_EVENT_VAD) {
                        if (eventObj.param == AI_VAD_BOS) console.log(v + "识别语音开始：" + eventObj.account);
                        else if (eventObj.param == AI_VAD_EOS) console.log(v + "识别结束：" + eventObj.account);
                    }
                    else if (eventObj.result == AI_EVENT_RESULT) {
                        var vJsonData = base64_decode(eventObj.data);
                        console.log(v + "识别完成：" + vJsonData);
                    }
                }
                else if (eventObj.type == OSIPSEVENT_PLAYFILE) {
                    if (eventObj.result == OSIPSEVENT_PLAYFILE_START) {
                        console.log("开始播放文件：" + eventObj.file);
                    }
                    else if (eventObj.result == OSIPSEVENT_PLAYFILE_STOP) {
                        console.log("停止播放文件：" + eventObj.file);
                    }
                    else if (eventObj.result == OSIPSEVENT_PLAYFILE_END) {
                        console.log("放音结束：" + eventObj.file);
                    }
                }
            }
        },
        /**
         * 中继呼叫【呼入】
         * @param {any} tagaccount
         * @param {any} eventObj
         */
        ippbxFxoEventCallin: function (tagaccount, eventObj) {
            switch (eventObj.result) {
                case 202:
                    {
                        console.log("[" + tagaccount.m_from + "]中继来电:" + eventObj.to);
                    } break;
                case 204:
                    {
                        console.log("[" + tagaccount.m_from + "]中继来电响铃弹屏:" + eventObj.to);
                    } break;
                case 208:
                    {
                        console.log("[" + tagaccount.m_from + "]中继来电接通:" + eventObj.to);
                    } break;
                case 221:
                    {
                        console.log("[" + tagaccount.m_from + "]中继来电结束:" + eventObj.to);
                    } break;
                default: break;
            }
        },
        /**
         * 中继呼叫【呼出】
         * @param {any} tagaccount
         * @param {any} eventObj
         */
        ippbxFxoEventCallout: function (tagaccount, eventObj) {
            switch (eventObj.result) {
                case 102:
                    {
                        console.log("[" + tagaccount.m_from + "]中继呼叫:" + eventObj.to);
                    } break;
                case 104:
                    {
                        console.log("[" + tagaccount.m_from + "]中继呼出响铃弹屏:" + eventObj.to);
                    } break;
                case 108:
                    {
                        console.log("[" + tagaccount.m_from + "]中继呼出接通:" + eventObj.to);
                    } break;
                case 121:
                    {
                        console.log("[" + tagaccount.m_from + "]中继呼出结束:" + eventObj.to);
                    } break;
                default: break;
            }
        },
        /**
         * 来电
         * @param {any} tagaccount
         * @param {any} eventObj
         */
        ippbxEventCallin: function (tagaccount, eventObj) {
            var from = eventObj.from;
            if (eventObj.from.length == 11) {
                from = eventObj.from.slice(0, 3) + '****' + eventObj.from.slice(7, 11);
            }
            switch (eventObj.result) {
                case 102:
                    {
                        url_SetCaller(0, url_GetJsonCaller(eventObj));
                        console.log("[" + tagaccount.m_from + "]来电呼叫:" + from + "&caller=" + url_GetJsonCaller(eventObj));
                    } break;
                case 104:
                    {
                        layui.$('.call-mask').show();
                        layui.$('.call-dialog').show();
                        layui.$('.answer').show();
                        layui.$('.call-text').text('振铃中...');
                        layui.$('.mobile-info').text(from);
                        layui.$('.name-info').text('未知');
                        layui.index.startCall();
                        console.log("[" + tagaccount.m_from + "]eventObj:" + url_GetJsonString(eventObj));
                        console.log("[" + tagaccount.m_from + "]tagaccount:" + url_GetJsonString(tagaccount));
                        console.log("[" + tagaccount.m_from + "]来电响铃弹屏:" + from + "&uctype=" + eventObj.uctype + "&caller=" + url_GetJsonCaller(eventObj));
                        if (url_GetJsonCaller(eventObj).length > 0) {
                            //查询是否从ivr按键后弹屏
                            //T_QueryDTMFCmdEx(document.getElementById('itemname').value, url_GetJsonCaller(eventObj));
                        }
                    } break;
                case 108:
                    {
                        layui.$('.call-text').text('通话中...');
                        layui.$('.answer').hide();
                        console.log("[" + tagaccount.m_from + "]来电接通:" + from);
                    } break;
                case 121:
                    {
                        layui.index.endCall();
                        setTimeout(function () {
                            layui.$('.call-mask').hide();
                            layui.$('.call-dialog').hide();
                        }, 1000);
                        console.log("[" + tagaccount.m_from + "]来电结束:" + from);
                    } break;
                default: break;
            }
        },
        /**
         * 去电
         * @param {any} tagaccount
         * @param {any} eventObj
         */
        ippbxEventCallout: function (tagaccount, eventObj) {
            switch (eventObj.result) {
                case 202:
                    {
                        layui.$('.call-text').text('正在呼叫...');
                        layui.$('.call-mask').show();
                        layui.$('.call-dialog').show();
                        console.log("[" + tagaccount.m_from + "]电话拨号去电:" + eventObj.to);
                    } break;
                case 204:
                    {
                        layui.$('.call-text').text('振铃中...');
                        console.log("[" + tagaccount.m_from + "]电话拨号去电响铃弹屏:" + eventObj.to);
                    } break;
                case 208:
                    {
                        layui.$('.call-text').text('通话中...');
                        layui.index.startCall();
                        console.log("[" + tagaccount.m_from + "]电话拨号去电接通:" + eventObj.to);
                    } break;
                case 221:
                    {
                        layui.$('.call-text').text('通话结束');
                        layui.index.endCall();
                        setTimeout(function () {
                            layui.$('.call-mask').hide();
                            layui.$('.call-dialog').hide();
                        }, 1000);
                        console.log("[" + tagaccount.m_from + "]电话拨号去电结束:" + eventObj.to);
                    } break;
                default: break;
            }
        },
        /**
         * 软拨号
         * @param {any} tagaccount
         * @param {any} eventObj
         */
        ippbxEventCallext: function (tagaccount, eventObj) {
            switch (eventObj.result) {
                case 102:
                    {
                        layui.$('.call-text').text('正在呼叫...');
                        layui.$('.call-mask').show();
                        layui.$('.call-dialog').show();
                        console.log("[" + tagaccount.m_from + "]软拨号正在呼叫:" + eventObj.from.slice(0, 3) + '****' + eventObj.from.slice(7, 11) + " guid=" + eventObj.guid);
                        var orderId = layui.$('#orderId').val();
                        if (orderId != '') {
                            //建立订单通话记录绑定关系
                            layui.index.callRecordBindOrder(orderId, eventObj.guid);
                        }
                    } break;
                case 104:
                    {
                        layui.$('.call-text').text('响铃中...');
                        console.log("[" + tagaccount.m_from + "]软拨号去电响铃弹屏:" + eventObj.from.slice(0, 3) + '****' + eventObj.from.slice(7, 11));
                    } break;
                case 108:
                    {
                        layui.$('.call-text').text('通话中...');
                        layui.index.startCall();
                        console.log("[" + tagaccount.m_from + "]软拨号分机接通:" + eventObj.from.slice(0, 3) + '****' + eventObj.from.slice(7, 11));
                    } break;
                case 121:
                    {
                        layui.$('.call-text').text('通话结束');
                        layui.index.endCall();
                        setTimeout(function () {
                            layui.$('.call-mask').hide();
                            layui.$('.call-dialog').hide();
                        }, 1000);
                        console.log("[" + tagaccount.m_from + "]软拨号去电结束:" + eventObj.from.slice(0, 3) + '****' + eventObj.from.slice(7, 11));
                    } break;

                case 1102:
                    {
                        layui.$('.call-text').text('正在呼叫');
                        layui.$('.call-mask').show();
                        layui.$('.call-dialog').show();
                        console.log("[" + tagaccount.m_from + "]软拨号[被叫]正在呼叫:" + eventObj.from.slice(0, 3) + '****' + eventObj.from.slice(7, 11));
                    } break;
                case 1104:
                    {
                        layui.$('.call-text').text('响铃中...');
                        console.log("[" + tagaccount.m_from + "]软拨号[被叫]去电响铃弹屏:" + eventObj.from.slice(0, 3) + '****' + eventObj.from.slice(7, 11));
                    } break;
                case 1108:
                    {
                        layui.$('.call-text').text('通话中...');
                        layui.$('.time-info').show();
                        console.log("[" + tagaccount.m_from + "]软拨号[被叫]分机接通:" + eventObj.from.slice(0, 3) + '****' + eventObj.from.slice(7, 11));
                    } break;
                case 1121:
                    {
                        console.log("[" + tagaccount.m_from + "]软拨号[被叫]去电结束:" + eventObj.from.slice(0, 3) + '****' + eventObj.from.slice(7, 11));
                        layui.$('.call-text').text('通话结束');
                        layui.index.endCall();
                        setTimeout(function () {
                            layui.$('.call-mask').hide();
                            layui.$('.call-dialog').hide();
                        }, 1000);
                    } break;

                default: break;
            }
        },
        /**
         * 电话抢接
         * @param {any} tagaccount
         * @param {any} eventObj
         */
        ippbxEventCalloutAct: function (tagaccount, eventObj) {
            switch (eventObj.result) {
                case 202:
                    {
                        console.log("[" + tagaccount.m_from + "]电话抢接拨号:" + eventObj.to);
                    } break;
                case 208:
                    {
                        console.log("[" + tagaccount.m_from + "]电话抢接接通弹屏:" + eventObj.to);
                    } break;
                case 221:
                    {
                        console.log("[" + tagaccount.m_from + "]电话抢接结束:" + eventObj.to);
                    } break;
                default: break;
            }
        },
        /**
         * 分机拨号
         * @param {any} name
         * @param {any} mobile
         * @param {any} encMobile
         * @param {any} orderId
         */
        call: function (name, mobile, encMobile, orderId) {
            layui.$('.mobile-info').text(encMobile);
            layui.$('.name-info').text(name);
            layui.$('.answer').hide();
            layui.$('#orderId').val(orderId);
            if (url_StartDial(0, mobile) <= 0) {
                alert("分机拨号错误");
            }
        },
        /**
         * 接听
         * */
        answer: function () {
            if (url_DoExtAnswer(0) < 0) {
                alert("usb设备应答错误");
            }
        },
        /**
         * 挂机
         * */
        hangup: function () {
            if (url_DoHang(0) < 0) {
                alert("挂机失败，请尝试手工挂机");
            }
        },
        /**
         * 通话开始计时
         * */
        startCall: function () {
            time = 0;
            layui.$('.time-info').text('00:00:00');
            layui.$('.time-info').show();
            timeInterval = setInterval(function () {
                time++;
                var hours = Math.floor(time / 3600);
                var minutes = Math.floor((time % 3600) / 60);
                var seconds = Math.floor(time % 60);
                var formatHours = ('0' + hours).slice(-2);
                var formatMinutes = ('0' + minutes).slice(-2);
                var formatSeconds = ('0' + seconds).slice(-2);
                layui.$('.time-info').text(formatHours + ':' + formatMinutes + ':' + formatSeconds);
            }, 1000);
        },
        /**
         * 通话结束计时
         * */
        endCall: function () {
            layui.$('.time-info').hide();
            try {
                clearInterval(timeInterval);
            } catch (e) {
            }
        }
    }

    exports('index', func);
});