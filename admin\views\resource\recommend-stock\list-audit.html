﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 180px; }
    </style>
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input"
                                   placeholder="标题/股票代码/名称" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">类型</label>
                        <div class="layui-input-inline">
                            <select name="type" id="type">
                                <option value="">请选择类型</option>
                                <option value="1">产品策略</option>
                                <option value="2">福利资料</option>
                                <option value="3">服务资料</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="hgAuditStatus" id="hgAuditStatus">
                                <option value="">请选择状态</option>
                                <option value="2">待审核</option>
                                <option value="3">完成</option>
                                <option value="4">驳回</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">投资顾问</label>
                        <div class="layui-input-inline">
                            <select name="investmentAdvisorId" id="investmentadvisor-view" lay-search>
                                <option value="">请选择投资顾问</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">发布日期</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="recommend-stock-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                        <button class="layui-btn layuiadmin-btn-list layui-btn-primary" lay-submit
                                lay-filter="recommend-stock-export">
                            <i class="layui-icon layui-icon-export layuiadmin-button-btn"></i>导出
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="recommend-stock-list" lay-filter="list"></table>
                <script type="text/html" id="recommend-stock-bar">
                    <a class="layui-btn layui-btn-xs layui-btn-warm" href="read.html?id={{d.id}}" target="_blank">查看</a>
                    <a class="layui-btn layui-btn-xs" lay-event="audit">审核</a>
                    <!-- {{# if(d.hgAuditStatus!=3&&d.hgAuditStatus!=4){}}
                    {{#}}} -->
                </script>
            </div>
        </div>
    </div>
    <script id="investmentadvisor-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">
            {{item.name}}
            {{# if(item.certNo!=''){}}
            （{{item.certNo}}）
            {{# }}}
        </option>
        {{#  }); }}
    </script>
    <script id="tableStatus" type="text/html">

        {{#   var result = '-'; var color = "";
        if (d.hgAuditStatus == 1) {
            result = '待送审';
        }
        else if (d.hgAuditStatus == 2) {
            result = '待审核';
        }
        else if (d.hgAuditStatus == 3) {
            result = '完成';
            color = "green";
        }
        else if (d.hgAuditStatus == 4) {
            result = '驳回';
            color= "red";
        }
        if (d.hgAuditStatus != 1 && d.hgAuditStatus != 2 && d.hgAuditRemark != null && d.hgAuditRemark != '') {
            result += '（' + d.hgAuditRemark + '）';
        }
        }}
        <span style="color:{{=color}}">{{result}}</span>
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.42"></script>
    <script type="text/javascript">
        layui.use(['form', 'laydate', 'common', 'recommendStock'], function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            layui.recommendStock.query();
            layui.recommendStock.initAllInvestmentadvisor();
            //监听查询按钮
            layui.form.on('submit(recommend-stock-search)', function (data) {
                var field = data.field;
                if (field.hgAuditStatus == '') {
                    field.hgAuditStatus = -1;
                }
                if (field.type == '') {
                    field.type = -1;
                }
                //执行重载
                layui.tableRequest.reload("recommend-stock-list", {
                    where: field
                });
            });
            //监听导出按钮
            layui.form.on('submit(recommend-stock-export)', function (data) {
                if (data.field.hgAuditStatus == '') {
                    data.field.hgAuditStatus = -1;
                }
                if (data.field.type == '') {
                    data.field.type = -1;
                }
                data.field.companyId = layui.setter.companyId;
                layui.recommendStock.export(data);
            });
        });
    </script>
</body>
</html>