﻿layui.define(['laytpl', 'request', 'tableRequest', 'setter', 'common', 'company', 'role', 'department', 'customer'], function (exports) {
    var func = {
        /**
         * 渲染客户列表
         * @param {any} selectedId
         */
        initCustomer: function (checkedIds) {
            layui.customer.getAll(function (res) {
                if (res.result != null && res.result.length > 0 && checkedIds.length > 0) {
                    for (var i = 0; i < res.result.length; i++) {
                        var isChecked = checkedIds.includes(res.result[i].id);
                        res.result[i].isChecked = isChecked;
                    }
                }
                var getTpl = document.getElementById("customer-tpl").innerHTML
                    , view = document.getElementById('customer-list-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('checkbox');
            });
        },
        /**
        * 渲染客户列表
        * @param {any} companyId
        * @param {any} selectedId
        */
        initCustomerForSelect: function (selectedId) {
            layui.customer.getAll(function (res) {
                res.result.unshift({ id: '', alias: '请选择客户' });
                var getTpl = document.getElementById("customer-tpl").innerHTML
                    , view = document.getElementById('customer-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (selectedId != undefined) {
                    view.value = selectedId;
                }
                layui.form.render('select');
            });
        },
        /**
         * 渲染部门下拉选框
         * @param {any} companyId
         * @param {any} selectId
         */
        initDepartment: function (companyId, selectId) {
            layui.department.queryByCompanyId(companyId, function (res) {
                var json = [{ id: '', name: '请选择所属部门' }];
                for (var i = 0; i < res.result.length; i++) {
                    var level1 = { id: res.result[i].id, name: res.result[i].name };
                    json.push(level1);
                    var child2 = res.result[i].childs;
                    if (child2 != null && child2.length > 0) {
                        for (var j = 0; j < child2.length; j++) {
                            var level2 = { id: child2[j].id, name: res.result[i].name + ' > ' + child2[j].name };
                            json.push(level2);
                            if (child2[j].childs != null && child2[j].childs.length > 0) {
                                for (var k = 0; k < child2[j].childs.length; k++) {
                                    var level3 = { id: child2[j].childs[k].id, name: res.result[i].name + ' > ' + child2[j].name + ' > ' + child2[j].childs[k].name };
                                    json.push(level3);
                                }
                            }
                        }
                    }
                }
                var getTpl = document.getElementById("department-tpl").innerHTML
                    , view = document.getElementById('department-view');
                layui.laytpl(getTpl).render(json, function (html) {
                    view.innerHTML = html;
                });
                if (selectId != '') {
                    view.value = selectId;
                }
                layui.form.render('select');
            });
        },
        /**
         * 渲染角色列表
         * @param {any} companyId
         * @param {any} checkedIds
         */
        initRole: function (companyId, selectId) {
            layui.role.queryByCompanyId(companyId, true, function (res) {
                res.result.unshift({ id: '', name: '请选择角色' });
                var getTpl = document.getElementById("role-tpl").innerHTML
                    , view = document.getElementById('role-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (selectId != '') {
                    view.value = selectId;
                }
                layui.form.render('select');
            });
        },
        /**
         * 渲染客户销售账号列表
         * */
        initSaleUser: function (callbackFunc) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/admin-user/brief/by-userid/query',
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },

        /**
         * 通过id获取用户信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id') || '';
            layui.request({
                requestBase: 'sso',
                method: 'post',
                url: '/admin/basis/admin-user/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=account]').val(res.result.account);
                    layui.$('input[name=companyId]').val(res.result.companyId);
                    layui.$('#status').val(res.result.status);

                    layui.formAdminUser.initDepartment(res.result.companyId, res.result.departmentId);
                    layui.formAdminUser.initRole(res.result.companyId, res.result.roleIds);
                    layui.formAdminUser.initCustomer(res.result.customerIds);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取用户配置的客户权限
         * */
        getCustomerPermission: function () {
            var userId = layui.common.getUrlParam('userId') || '';
            if (userId == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/customer-permission/get?userId=' + userId,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.formAdminUser.initCustomer(res.result);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 通过id获取用户信息
        * */
        getByCustomer: function () {
            var id = layui.common.getUrlParam('id') || '';
            layui.request({
                requestBase: 'sso',
                method: 'post',
                url: '/admin/basis/admin-user/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=account]').val(res.result.account);
                    layui.$('#status').val(res.result.status);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取后台用户列表
         * */
        query: function () {
            layui.tableRequest.request('form', true, 'admin-user-list', '/admin/basis/admin-user/query', 'application/json', [
                { type: 'checkbox', fixed: 'left' },
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '姓名' },
                { field: 'account', title: '登录账号' },
                {
                    field: 'customerNames', title: '绑定客户', templet: function (e) {
                        var customerNames = '-';
                        if (e.customerNames != null && e.customerNames.length > 0) {
                            customerNames = '';
                            for (var i = 0; i < e.customerNames.length; i++) {
                                if (customerNames != '')
                                    customerNames += '，';
                                customerNames += e.customerNames[i];
                            }
                        }
                        return customerNames;
                    }
                },
                {
                    field: 'roleNames', title: '角色', templet: function (e) {
                        var roleNames = '-';
                        if (e.roleNames != null && e.roleNames.length > 0) {
                            roleNames = '';
                            for (var i = 0; i < e.roleNames.length; i++) {
                                if (roleNames != '')
                                    roleNames += '，';
                                roleNames += e.roleNames[i];
                            }
                        }
                        return roleNames;
                    }
                },
                {
                    field: 'status', title: '状态', width: 80, templet: function (e) {
                        var statusDesc = '';
                        switch (e.status) {
                            case 1:
                                statusDesc = '正常';
                                break;
                            case 2:
                                statusDesc = '冻结';
                                break;
                            case 3:
                                statusDesc = '离职';
                                break;
                            default:
                        }
                        return statusDesc;
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 120, align: 'center', toolbar: '#admin-user-bar' }
            ], { companyId: layui.setter.companyId }, '#topToolBar');
            //监听表格事件
            layui.formAdminUser.tableEvent();
        },
        /**
         * 获取后台用户列表[客户端]
         * */
        queryByCustomer: function () {
            layui.tableRequest.request('form', true, 'admin-user-list', '/admin/basis/admin-user/by-userid/query', 'application/json', [
                { type: 'checkbox', fixed: 'left' },
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '姓名' },
                { field: 'account', title: '登录账号' },
                {
                    field: 'status', title: '状态', templet: function (e) {
                        var statusDesc = '';
                        switch (e.status) {
                            case 1:
                                statusDesc = '正常';
                                break;
                            case 2:
                                statusDesc = '冻结';
                                break;
                            case 3:
                                statusDesc = '离职';
                                break;
                            default:
                        }
                        return statusDesc;
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 80, align: 'center', toolbar: '#admin-user-bar' }
            ], { companyId: layui.setter.companyId }, '#topToolBar');
            //监听表格事件
            layui.formAdminUser.tableEvent();
        },
        /**
         * 创建后台用户
         * @param {any} data
         */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/admin-user/create',
                data: JSON.stringify({
                    companyId: layui.setter.companyId,
                    departmentId: data.field.departmentId,
                    roleIds: data.field.roleId != null && data.field.roleId != "" ? [data.field.roleId] : [],
                    name: data.field.name,
                    account: data.field.account,
                    password: data.field.password,
                    customerIds: data.field.customerIds
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("admin-user-list");
                    parent.layui.common.alertAutoClose("用户创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 创建后台用户[客户端]
        * @param {any} data
        */
        createByCustomer: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/admin-user/customer/create',
                data: JSON.stringify({
                    roleId: layui.setter.roleId,
                    name: data.field.name,
                    account: data.field.account,
                    password: data.field.password,
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("admin-user-list");
                    parent.layui.common.alertAutoClose("用户创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过id修改单个用户信息
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/admin-user/update',
                data: JSON.stringify({
                    id: data.field.id,
                    companyId: data.field.companyId,
                    departmentId: data.field.departmentId,
                    roleIds: data.field.roleId != null && data.field.roleId != "" ? [data.field.roleId] : [],
                    name: data.field.name,
                    account: data.field.account,
                    status: data.field.status,
                    customerIds: data.field.customerIds
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("admin-user-list");
                    parent.layui.common.alertAutoClose("用户修改成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过id修改单个用户信息[客户端]
         * @param {any} data
         */
        updateByCustomer: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/admin-user/customer/update',
                data: JSON.stringify({
                    id: data.field.id,
                    name: data.field.name,
                    status: data.field.status,
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("admin-user-list");
                    parent.layui.common.alertAutoClose("用户修改成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 配置用户客户权限
         * @param {any} data
         */
        setPermission: function (data) {
            var userId = layui.common.getUrlParam('userId') || '';
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/customer-permission/save',
                data: JSON.stringify({
                    userId: userId,
                    customerIds: data.field.customerIds
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.common.alertAutoClose("权限设置成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 批量修改用户登录密码
         * @param {any} ids
         * @param {any} password
         */
        batchUpdatePwd: function (ids, password) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/admin-user/password/batch-update',
                data: JSON.stringify({
                    ids: ids,
                    password: password
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("admin-user-list");
                    layui.common.alertAutoClose("用户密码重置成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 批量修改用户状态
         * @param {any} ids
         * @param {any} status
         * @param {any} eventName
         */
        batchUpdateStatus: function (ids, status, eventName) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/admin-user/status/batch-update',
                data: JSON.stringify({
                    ids: ids,
                    status: status
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("admin-user-list");
                    layui.common.alertAutoClose("用户" + eventName + "成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 批量删除用户
         * @param {any} ids
         */
        batchDelete: function (ids) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/admin-user/batch-delete',
                data: JSON.stringify({
                    ids: ids
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("admin-user-list");
                    layui.common.alertAutoClose("用户删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    var updateUrl = 'update.html?id=' + data.id + '&companyId=' + layui.setter.companyId;
                    layui.common.openIframe('编辑用户', 650, 500, updateUrl);
                }
                else if (obj.event === 'permission') {
                    layui.common.openIframe('设置权限', 650, 500, 'set-permisson.html?userId=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该用户吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.adminUser.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'edit-customer') {
                    var updateUrl = 'update-customer.html?id=' + data.id + '&companyId=' + layui.setter.companyId;
                    layui.common.openIframe('编辑用户', 550, 300, updateUrl);
                }
            });
            layui.table.on('toolbar(list)', function (obj) {
                var checkedList = layui.table.checkStatus(obj.config.id);
                if (checkedList.data.length == 0) {
                    layui.common.alertAutoClose("请选择需要操作的用户");
                    return;
                }
                var names = '';
                var ids = [];
                for (var i = 0; i < checkedList.data.length; i++) {
                    if (names != '')
                        names += '，';
                    names += checkedList.data[i].name;
                    ids.push(checkedList.data[i].id);
                }
                if (obj.event == 'frozen' || obj.event == 'quit' || obj.event == 'recovery') {
                    var typeName = obj.event == 'frozen' ? '冻结' : obj.event == 'quit' ? '离职' : '恢复';
                    var typeId = obj.event == 'frozen' ? 2 : obj.event == 'quit' ? 3 : 1;
                    var confirmIndex = layui.layer.confirm('确定' + typeName + '【' + names + '】用户吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.layer.close(confirmIndex);
                        layui.formAdminUser.batchUpdateStatus(ids, typeId, typeName);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event == 'reset') {
                    var confirmIndex = layui.layer.confirm('确定重置【' + names + '】用户密码吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.layer.close(confirmIndex);
                        layer.prompt({ title: '请输入新密码', formType: 1 }, function (pass, index) {
                            layer.close(index);
                            layui.formAdminUser.batchUpdatePwd(ids, pass);
                        });
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除【' + names + '】用户信息吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.formAdminUser.batchDelete(ids);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('formAdminUser', func);
});