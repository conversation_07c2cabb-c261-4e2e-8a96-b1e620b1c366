﻿layui.define(['laytpl', 'form', 'request', 'uploadFile', 'tableRequest', 'common', 'xmSelect', 'dropdown', 'dic', 'adminUser', 'company', 'productChannel', 'landingPage', 'subsidiary', 'investmentAdvisor'], function (exports) {
    var func = {
        /**
         * 获取投顾列表
         * */
        initInvestmentAdvisor: function (callbackFunc) {
            layui.investmentAdvisor.queryBySubsidiaryId('', function (investmentAdvisorRes) {
                if (investmentAdvisorRes.result != null) {
                    investmentAdvisor = investmentAdvisorRes.result;
                }
                if (callbackFunc != undefined) {
                    callbackFunc(investmentAdvisorRes.result);
                }
            });
        },
        /**
       * 渲染所属组织下拉选框
       * */
        initSubsidiary: function (id) {
            layui.subsidiary.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择所属组织' });
                var getTpl = document.getElementById("subsidiary-tpl").innerHTML
                    , view = document.getElementById('subsidiary-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('subsidiary-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
        * 渲染所属组织列表(增删审)
        * @param {any} checkedIds
        */
        initProductForOp: function (selectIds) {
            layui.subsidiary.getAll(function (res) {
                var data = [];
                var selectItems = [];
                if (res.result.length > 0) {
                    for (var i = 0; i < res.result.length; i++) {
                        var item = { name: res.result[i].name, value: res.result[i].id };
                        data.push(item);
                        if (selectIds.length > 0 && selectIds.indexOf(res.result[i].id) > -1) {
                            selectItems.push(item);
                        }
                    }
                }
                xmSel = layui.xmSelect.render({
                    el: '#subsidiary-item',
                    language: 'zn',
                    filterable: true,
                    tips: '请选择使用团队',
                    theme: { color: '#0081ff ' },
                    data: data,
                    toolbar: { show: true },
                    autoRow: true,
                    disabled: location.href.indexOf('audit.html') > -1 ? true : false
                });
                if (selectItems.length > 0) {
                    xmSel.setValue(selectItems);
                }
            });
        },
        /**
        * 渲染企业下拉选框
        * */
        initCompany: function (id) {
            layui.company.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企业' });
                var getTpl = document.getElementById("company-tpl").innerHTML
                    , view = document.getElementById('company-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('company-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 渲染渠道下拉选框
         * */
        initProductChannel: function () {
            layui.productChannel.getAll(function (res) {
                res.result.unshift({ key: '', name: '请选择渠道' });
                // 查找要移除对象的索引
                let index = res.result.findIndex(obj => obj.key === 's1_order');
                if (index !== -1) {
                    res.result.splice(index, 1);
                }
                var getTpl = document.getElementById("product-channel-tpl").innerHTML
                    , view = document.getElementById('product-channel-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('select');
            });
        },
        /**
         * 初始化活动
         * @param {any} productId
         */
        initActivity: function (productId, productAmonnt) {
            layui.request({
                method: 'post',
                url: '/admin/product/activity/by-productid/query?productId=' + productId,
            }).then(function (res) {
                if (res.isSuccess) {
                    res.result.unshift({ id: '', name: '不使用优惠', amount: 0 });
                    for (var i = 0; i < res.result.length; i++) {
                        res.result[i].payableAmount = (productAmonnt - res.result[i].amount).toFixed(2);
                    }
                    var getTpl = document.getElementById("activity-tpl").innerHTML
                        , view = document.getElementById('activity-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.form.render('select');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 渲染产品落地页列表
         * @param {any} companyId
         */
        initLandingPage: function (companyId) {
            layui.landingPage.getByCompanyId(companyId, function (res) {
                res.result.unshift({ url: '', title: '使用通用落地页' });
                var getTpl = document.getElementById("landing-page-tpl").innerHTML
                    , view = document.getElementById('landing-page-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('select');
            });
        },
        /**
         * 获取所有产品列表
         * @param {any} callbackFunc
         */
        getAll: function (callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/product/info/get-all',
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res)
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 渲染功能权限列表
         * @param {any} checkedIds
         */
        initDic: function (checkedIds) {
            layui.dic.query('product_module', function (res) {
                if (res.result != null && res.result.length > 0 && checkedIds.length > 0) {
                    for (var i = 0; i < res.result.length; i++) {
                        var isChecked = checkedIds.includes(res.result[i].key);
                        res.result[i].isChecked = isChecked;
                    }
                }
                var getTpl = document.getElementById("dic-tpl").innerHTML
                    , view = document.getElementById('dic-list-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('checkbox');
            });
        },
        /**
         * 渲染所属用户列表
         * @param {any} id
         */
        initAdminUser: function (id) {
            layui.adminUser.getUserList(layui.setter.productCompanyId, function (res) {
                res.result.unshift({ id: '', name: '请选择所属用户', account: '' });
                var getTpl = document.getElementById("admin-user-tpl").innerHTML
                    , view = document.getElementById('admin-user-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                var aUserStr = localStorage.getItem('ly-admin-user');
                if (aUserStr != null) {
                    var aUserObj = JSON.parse(aUserStr);
                    document.getElementById('admin-user-view').value = aUserObj.id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 初始化富文本编辑器
         * */
        initTinymce: function () {
            tinymce.init({
                selector: '#detail',
                language: 'zh_CN',
                menubar: false,
                branding: false,
                plugins: 'preview searchreplace autolink directionality visualblocks visualchars fullscreen image link media template code codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists wordcount imagetools help emoticons autosave autoresize axupimgs',
                toolbar: 'code cut copy paste pastetext | forecolor backcolor link image media axupimgs | alignleft aligncenter alignright alignjustify outdent indent styleselect formatselect fontselect fontsizeselect bullist numlist bold italic underline strikethrough blockquote subscript superscript removeformat table charmap emoticons pagebreak insertdatetime preview fullscreen bdmap indent2em lineheight formatpainter',
                min_height: 400,
                max_height: 500,
                fontsize_formats: '12px 14px 16px 18px 24px 36px 48px 56px 72px',
                font_formats: '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;',
                importcss_append: true,
                images_upload_handler(blobInfo, progress) {
                    return new Promise((resolve, reject) => {
                        var upurl = layui.setter.request.resourceBaseUrl + '/common/file/public/tinymce/put?folder=files/product/detail';
                        var file = blobInfo.blob();
                        var xhr, formData;
                        xhr = new XMLHttpRequest();
                        xhr.withCredentials = false;
                        xhr.open('POST', upurl);
                        xhr.onload = function () {
                            if (xhr.status != 200) {
                                reject('HTTP Error: ' + xhr.status);
                                return;
                            }
                            var json = JSON.parse(xhr.responseText);
                            if (!json || json.location == '') {
                                reject(xhr.responseText);
                                return;
                            }
                            resolve(json.location);
                        };
                        formData = new FormData();
                        formData.append('file', file, file.name);
                        xhr.send(formData);
                    })
                },
                //自定义文件选择器的回调内容
                file_picker_callback: function (callback, value, meta) {
                    var filetype = '.pdf, .txt, .zip, .rar, .7z, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .mp3, .mp4';
                    var upurl = layui.setter.request.resourceBaseUrl + '/common/file/public/tinymce/put?folder=files/product/detail';
                    switch (meta.filetype) {
                        case 'image':
                            filetype = '.jpg, .jpeg, .png, .gif';
                            break;
                        case 'media':
                            filetype = '.mp3, .mp4';
                            break;
                        default:
                    }
                    var input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    input.setAttribute('accept', filetype);
                    input.click();
                    input.onchange = function () {
                        var file = this.files[0];
                        var xhr, formData;
                        xhr = new XMLHttpRequest();
                        xhr.withCredentials = false;
                        xhr.open('POST', upurl);
                        xhr.onload = function () {
                            var json;
                            if (xhr.status != 200) {
                                failure('HTTP Error: ' + xhr.status);
                                return;
                            }
                            json = JSON.parse(xhr.responseText);
                            if (!json || typeof json.location != 'string') {
                                failure('Invalid JSON: ' + xhr.responseText);
                                return;
                            }
                            callback(json.location);
                        };
                        formData = new FormData();
                        formData.append('file', file, file.name);
                        xhr.send(formData);
                    };
                },
                autosave_ask_before_unload: false
            });
        },
        /**
         * 上传封面
         * */
        initFile: function () {
            layui.uploadFile('Resource', 'uploadImg', '/common/file/public/put?folder=files/product/cover', function (res) {
                if (res.isSuccess) {
                    layui.$("#hid_coverUrl").val(res.result);
                    layui.common.alertAutoClose("上传成功");
                    layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.result);
                }
                else {
                    layui.common.alert(res.message, 2);
                }
            });
            layui.uploadFile('Resource', 'uploadImg2', '/common/file/public/put?folder=files/product/share', function (res) {
                if (res.isSuccess) {
                    layui.$("#hid_coverUrl2").val(res.result);
                    layui.common.alertAutoClose("上传成功");
                    layui.$('#uploadDemoView2').removeClass('layui-hide').find('img').attr('src', res.result);
                }
                else {
                    layui.common.alert(res.message, 2);
                }
            });
        },
        /**
         * 通过id获取单个产品信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/product/info/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=coverUrl]').val(res.result.coverUrl);
                    layui.$('input[name=shareIconUrl]').val(res.result.shareIconUrl);
                    layui.$('input[name=status]').val(res.result.status);
                    layui.$('#riskLevel').val(res.result.riskLevel);
                    layui.$('input[name=amount]').val(res.result.amount);
                    if (res.result.startTime != null && res.result.startTime != '') {
                        layui.$('input[name=startTime]').val(layui.common.timeFormat(res.result.startTime).split(' ')[0]);
                    }
                    layui.$('input[name=teacherName]').val(res.result.teacherName);
                    layui.$('input[name=kfCaseId]').val(res.result.kfCaseId);
                    layui.$('input[name=thirdPartyProductId]').val(res.result.thirdPartyProductId);
                    layui.$('input[name=contractTemelateId]').val(res.result.contractTemelateId);
                    layui.$('input[name=payUrl]').val(res.result.payUrl);
                    layui.$('input[name=duration]').val(res.result.duration);
                    layui.$('input[name=shareDesc]').val(res.result.shareDesc);
                    layui.$('#unit').val(res.result.unit);
                    layui.$('#status').val(res.result.status);
                    layui.$('#scene').val(res.result.scene);
                    layui.$('#desc').val(res.result.desc);
                    layui.$('#type').val(res.result.type);
                    layui.$('#detail').val(res.result.detail);
                    layui.form.render('select');
                    layui.product.initFile();
                    if (res.result.coverUrl != '') {
                        layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.result.coverUrl);
                    }
                    if (res.result.shareIconUrl != '') {
                        layui.$('#uploadDemoView2').removeClass('layui-hide').find('img').attr('src', res.result.shareIconUrl);
                    }
                    layui.product.initDic(res.result.moduleCodes);
                    layui.product.initProductForOp(res.result.useSubsidiaryIds != null ? res.result.useSubsidiaryIds : []);
                    layui.product.initTinymce();
                    if (location.href.indexOf('audit.html') > -1) {
                        var investmentAdvisorItem = '';
                        for (var i = 0; i < res.result.investmentAdvisorList.length; i++) {
                            investmentAdvisorItem += ' <tr><td> <input type="text" disabled="disabled" value="' + (res.result.investmentAdvisorList[i].name + '（' + res.result.investmentAdvisorList[i].certNo + '）') + '" class="layui-input">';
                            investmentAdvisorItem += '</td></tr>';
                        }
                        layui.$('.tb tbody').html(investmentAdvisorItem);
                        layui.form.render('select');
                    }
                    else {
                        layui.product.initInvestmentAdvisor(function () {
                            var investmentAdvisorItem = '';
                            for (var i = 0; i < res.result.investmentAdvisorList.length; i++) {
                                investmentAdvisorItem += ' <tr><td> <select class="item investmentAdvisor" lay-verify="required" lay-search>';
                                investmentAdvisorItem += '<option value="">请选择投资顾问</option>';
                                if (investmentAdvisor.length > 0) {
                                    for (var j = 0; j < investmentAdvisor.length; j++) {
                                        investmentAdvisorItem += '<option ' + (investmentAdvisor[j].id == res.result.investmentAdvisorList[i].id ? "selected" : "") + ' value="' + investmentAdvisor[j].id + '" data-name="' + investmentAdvisor[j].name + '" data-cert="' + investmentAdvisor[j].certNo + '">' + investmentAdvisor[j].name + '（' + investmentAdvisor[j].certNo + '）' + '</option>'
                                    }
                                }
                                investmentAdvisorItem += '</select></td>';
                                investmentAdvisorItem += '<td><a href="javascript:;" class="remove-item"><i class="layui-icon">&#xe640;</i></a></td></tr>';
                            }
                            layui.$('.tb tbody').html(investmentAdvisorItem);
                            layui.form.render('select');
                        });
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过id获取产品投顾信息
         * */
        getForInvestmentAdvisor: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/product/info/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    //获取投顾列表
                    layui.investmentAdvisor.queryBySubsidiaryId('', function (investmentAdvisorRes) {
                        var data = [];
                        var selectItems = [];
                        if (investmentAdvisorRes.result.length > 0) {
                            for (var i = 0; i < investmentAdvisorRes.result.length; i++) {
                                var item = {
                                    name: investmentAdvisorRes.result[i].name + '（' + investmentAdvisorRes.result[i].certNo + '）',
                                    value: investmentAdvisorRes.result[i].id,
                                    certNo: investmentAdvisorRes.result[i].certNo,
                                    oldName: investmentAdvisorRes.result[i].name
                                };
                                data.push(item);
                                if (res.result.investmentAdvisorList != null && res.result.investmentAdvisorList.length > 0) {
                                    for (var j = 0; j < res.result.investmentAdvisorList.length; j++) {
                                        if (investmentAdvisorRes.result[i].id == res.result.investmentAdvisorList[j].id) {
                                            selectItems.push(item);
                                        }
                                    }
                                }
                            }
                        }
                        xmSel = layui.xmSelect.render({
                            el: '#investment-advisor-item',
                            language: 'zn',
                            filterable: true,
                            tips: '请选择投顾',
                            theme: { color: '#0081ff ' },
                            data: data,
                            toolbar: { show: true },
                            autoRow: true,
                        });
                        if (selectItems.length > 0) {
                            xmSel.setValue(selectItems);
                        }
                    });
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过id获取单个产品信息
         * */
        getBrief: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.$('#productId').val(id);
            layui.request({
                method: 'post',
                url: '/admin/product/info/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.product.initLandingPage(res.result.companyId);
                    layui.product.initActivity(res.result.id, res.result.amount);
                    layui.$('#lab_product').text(res.result.name + ' / ¥' + res.result.amount + ' / ' + res.result.duration + (res.result.unit == '月' ? '个' : '') + res.result.unit);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取产品列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'product-list', '/admin/product/info/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                {
                    title: '使用团队', templet: function (e) {
                        return e.useSubsidiaryNames != null && e.useSubsidiaryNames.length > 0 ? e.useSubsidiaryNames.toString('，') : '-';
                    }
                },
                { field: 'name', title: '产品名称' },
                { field: 'amount', title: '产品金额' },
                {
                    title: '类型', templet: function (e) {
                        return e.type == 1 ? '证券投资培训课程' : '证券投资顾问产品';
                    }
                },
                {
                    title: '状态', templet: function (e) {
                        return e.status == 1 ? '正常销售' : '已下架';
                    }
                },
                {
                    title: '审核状态', templet: function (e) {
                        var auditStatus = '-';
                        if (e.auditStatus == 1)
                            auditStatus = '待审核';
                        else if (e.auditStatus == 2)
                            auditStatus = '通过';
                        else if (e.auditStatus == 3) {
                            auditStatus = '驳回';
                            if (e.auditRemark != null && e.auditRemark != '') {
                                auditStatus += '【' + e.auditRemark + '】'
                            }
                        }
                        return auditStatus;
                    }
                },
                {
                    title: '风险等级', templet: function (e) {
                        var riskLevel = '';
                        if (e.riskLevel == 1)
                            riskLevel = '低风险';
                        else if (e.riskLevel == 2)
                            riskLevel = '中低风险';
                        else if (e.riskLevel == 3)
                            riskLevel = '中风险';
                        else if (e.riskLevel == 4)
                            riskLevel = '中高风险';
                        else if (e.riskLevel == 5)
                            riskLevel = '高风险';
                        return riskLevel;
                    }
                },
                {
                    title: '服务周期', templet: function (e) {
                        return e.duration + (e.unit == '月' ? '个' : '') + e.unit;
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 160, align: 'left', toolbar: '#product-bar' }
            ], { companyId: layui.setter.productCompanyId }, '', '', null, function (res, curr, count) {
                layui.dropdown.suite();
            });
            //监听表格事件
            layui.product.tableEvent();
        },
        /**
        * 创建产品
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/info/create',
                data: JSON.stringify({
                    name: data.field.name,
                    coverUrl: data.field.coverUrl,
                    type: data.field.type,
                    riskLevel: data.field.riskLevel,
                    amount: data.field.amount,
                    startTime: data.field.startTime,
                    duration: data.field.duration,
                    unit: data.field.unit,
                    teacherName: data.field.teacherName,
                    kfCaseId: data.field.kfCaseId,
                    desc: data.field.desc,
                    moduleCodes: data.field.moduleCodes,
                    shareDesc: data.field.shareDesc,
                    shareIconUrl: data.field.shareIconUrl,
                    thirdPartyProductId: data.field.thirdPartyProductId,
                    detail: tinyMCE.activeEditor.getContent(),
                    useSubsidiaryIds: data.field.useSubsidiaryIds,
                    payUrl: data.field.payUrl,
                    contractTemelateId: data.field.contractTemelateId,
                    scene: data.field.scene,
                    investmentAdvisorList: data.field.investmentAdvisorList
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("产品创建成功");
                    setTimeout(function () { location.href = 'list.html'; }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑单个产品
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/info/update',
                data: JSON.stringify({
                    id: data.field.id,
                    name: data.field.name,
                    coverUrl: data.field.coverUrl,
                    type: data.field.type,
                    riskLevel: data.field.riskLevel,
                    amount: data.field.amount,
                    duration: data.field.duration,
                    unit: data.field.unit,
                    status: data.field.status,
                    startTime: data.field.startTime,
                    teacherName: data.field.teacherName,
                    kfCaseId: data.field.kfCaseId,
                    desc: data.field.desc,
                    moduleCodes: data.field.moduleCodes,
                    shareDesc: data.field.shareDesc,
                    shareIconUrl: data.field.shareIconUrl,
                    thirdPartyProductId: data.field.thirdPartyProductId,
                    detail: tinyMCE.activeEditor.getContent(),
                    useSubsidiaryIds: data.field.useSubsidiaryIds,
                    payUrl: data.field.payUrl,
                    contractTemelateId: data.field.contractTemelateId,
                    scene: data.field.scene,
                    investmentAdvisorList: data.field.investmentAdvisorList
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("产品编辑成功");
                    setTimeout(function () { location.href = 'list.html'; }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 编辑产品产品落地页审核状态
        * @param {any} data
        */
        updateAuditStatus: function (data) {
            var id = layui.common.getUrlParam('id');
            data.field.id = id;
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/info/audit-status/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("产品审核成功");
                    setTimeout(function () { location.href = 'list.html'; }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 修改产品投顾
         * */
        updateInvestmentAdvisor: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/info/investment-advisor/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("product-list");
                    parent.layui.common.alertAutoClose("投顾修改成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除产品
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/product/info/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("product-list");
                    layui.common.alertAutoClose("产品删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 生成购买链接
         * @param {any} data
         */
        genUrl: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            var reqData = {
                productId: data.field.productId,
                channelKey: data.field.channelKey,
                saleUserId: data.field.saleUserId,
                isShowKf: data.field.isShowKf,
                h5Url: data.field.h5Url,
                activityId: data.field.activityId,
                serviceStartTime: data.field.serviceStartTime,
                serviceEndTime: data.field.serviceEndTime,
                linkType: data.field.linkType
            };
            layui.request({
                method: 'post',
                url: '/admin/product/link/create',
                data: JSON.stringify(reqData),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('#lab_url').text(res.result.url);
                    layui.$('#erwm').attr('src', res.result.qrCodeUrl);
                    layui.$('#buyUrl').show();
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    location.href = 'update.html?id=' + data.id;
                }
                else if (obj.event === 'audit') {
                    location.href = 'audit.html?id=' + data.id;
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该产品吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.product.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'gen') {
                    layui.common.openIframe('生成购买链接', 780, 550, 'gen-url.html?id=' + data.id);
                }
                else if (obj.event === 'gen-give') {
                    layui.common.openIframe('生成赠送链接', 780, 550, 'gen-give-url.html?id=' + data.id);
                }
                else if (obj.event === 'tg') {
                    layui.common.openIframe('绑定投顾', 500, 350, 'set-investment-advisor.html?id=' + data.id);
                }
            });
        },
        /**
         * 绑定事件
         * @param {any} opType
         */
        bindEvent: function (opType) {
            if (layui.setter.companyId == '6340ecbf4d7e5a21ed46e996') {
                //点牛
                layui.$('.zqt-item').hide();
            }
            else if (layui.setter.companyId == '6336a5e0e23662d2a9cafec5') {
                //证券通
                layui.$('.dn-item').hide();
            }
            //添加行
            layui.$('.add-item').click(function () {
                var tr = ' <tr><td> <select class="item investmentAdvisor" lay-verify="required" lay-search>';
                tr += '<option value="">请选择投资顾问</option>';
                if (investmentAdvisor.length > 0) {
                    for (var j = 0; j < investmentAdvisor.length; j++) {
                        tr += '<option value="' + investmentAdvisor[j].id + '" data-name="' + investmentAdvisor[j].name + '" data-cert="' + investmentAdvisor[j].certNo + '">' + investmentAdvisor[j].name + '（' + investmentAdvisor[j].certNo + '）' + '</option>'
                    }
                }
                tr += '</select></td>';
                tr += '<td><a href="javascript:;" class="remove-item"><i class="layui-icon">&#xe640;</i></a></td></tr>';
                layui.$('.tb tbody').append(tr);
                layui.form.render('select');
            });
            //删除行
            layui.$(document).on("click", '.remove-item', function () {
                layui.$(this).parent().parent('tr').remove();
            });
            if (opType == 'create') {
                layui.form.on('submit(product-submit)', function (data) {
                    var moduleCodes = [];
                    layui.$(":checkbox[name=dic]:checked").each(function () {
                        moduleCodes.push(layui.$(this).val());
                    });
                    data.field.moduleCodes = moduleCodes;
                    data.field.useSubsidiaryIds = [];
                    var useSubsidiaryIds = xmSel.getValue('value');
                    if (useSubsidiaryIds.length > 0) {
                        data.field.useSubsidiaryIds = useSubsidiaryIds;
                    }
                    if (useSubsidiaryIds.length == 0) {
                        layui.common.alertAutoClose("请选择使用团队");
                        return false;
                    }
                    //投资顾问
                    var investmentAdvisorList = [];
                    var investmentAdvisorTrList = layui.$('.tb tbody tr');
                    if (investmentAdvisorTrList.length > 0) {
                        for (var i = 0; i < investmentAdvisorTrList.length; i++) {
                            var thisSel = investmentAdvisorTrList.eq(i).find('.investmentAdvisor');
                            investmentAdvisorList.push({
                                id: thisSel.val(), name: thisSel.find('option:selected').data('name'), certNo: thisSel.find('option:selected').data('cert')
                            })
                        }
                    };
                    data.field.investmentAdvisorList = investmentAdvisorList;
                    layui.product.create(data);
                });
            }
            else if (opType == 'update') {
                layui.form.on('submit(product-submit)', function (data) {
                    var moduleCodes = [];
                    layui.$(":checkbox[name=dic]:checked").each(function () {
                        moduleCodes.push(layui.$(this).val());
                    });
                    data.field.moduleCodes = moduleCodes;
                    data.field.useSubsidiaryIds = [];
                    var useSubsidiaryIds = xmSel.getValue('value');
                    if (useSubsidiaryIds.length > 0) {
                        data.field.useSubsidiaryIds = useSubsidiaryIds;
                    }
                    if (useSubsidiaryIds.length == 0) {
                        layui.common.alertAutoClose("请选择使用团队");
                        return false;
                    }
                    //投资顾问
                    var investmentAdvisorList = [];
                    var investmentAdvisorTrList = layui.$('.tb tbody tr');
                    if (investmentAdvisorTrList.length > 0) {
                        for (var i = 0; i < investmentAdvisorTrList.length; i++) {
                            var thisSel = investmentAdvisorTrList.eq(i).find('.investmentAdvisor');
                            investmentAdvisorList.push({
                                id: thisSel.val(), name: thisSel.find('option:selected').data('name'), certNo: thisSel.find('option:selected').data('cert')
                            })
                        }
                    };
                    data.field.investmentAdvisorList = investmentAdvisorList;
                    layui.product.update(data);
                });
            }
            else if (opType == 'update-status') {
                layui.form.on('submit(product-audit-submit)', function (data) {
                    layui.product.updateAuditStatus(data);
                });
            }
            else if (opType == 'gen') {
                if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.select.pageurl')) {
                    layui.$('.auth-item').show();
                }
                layui.form.on('submit(gen-url-submit)', function (data) {
                    layui.product.genUrl(data);
                    return false; //阻止表单跳转
                });
                layui.form.on('submit(copy-url-submit)', function (data) {
                    var content = layui.$("#lab_url").text();
                    if (content == "") {
                        layui.common.alertAutoClose("请先点击生成按钮生成购买地址");
                        return false;
                    }
                    let copy = (e) => {
                        e.preventDefault()
                        e.clipboardData.setData('text/plain', content)
                        layui.common.alertAutoClose("复制成功");
                        document.removeEventListener('copy', copy)
                    }
                    document.addEventListener('copy', copy)
                    document.execCommand("Copy");
                    return false; //阻止表单跳转
                });
                layui.form.on('select(product-channel)', function (data) {
                    //if (data.value == 'offline') {
                    //    layui.product.initAdminUser();
                    //    layui.$('#admin-user-item').show();
                    //}
                    //else {
                    //    layui.$('#admin-user-item').hide();
                    //    layui.$('#admin-user-view').val('');
                    //}
                });
            }
            else if (opType == 'gen-give') {
                layui.form.on('submit(gen-url-submit)', function (data) {
                    if (!layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.gen.giveurl')) {
                        layui.common.alertAutoClose("暂无权限");
                        return false;
                    }
                    var timeArr = data.field.giveDate.split(' 到 ');
                    data.field.serviceStartTime = timeArr[0];
                    data.field.serviceEndTime = timeArr[1];
                    data.field.linkType = 'sign';
                    console.log(data.field);
                    layui.product.genUrl(data);
                    return false; //阻止表单跳转
                });
                layui.form.on('submit(copy-url-submit)', function (data) {
                    var content = layui.$("#lab_url").text();
                    if (content == "") {
                        layui.common.alertAutoClose("请先点击生成按钮生成链接");
                        return false;
                    }
                    let copy = (e) => {
                        e.preventDefault()
                        e.clipboardData.setData('text/plain', content)
                        layui.common.alertAutoClose("复制成功");
                        document.removeEventListener('copy', copy)
                    }
                    document.addEventListener('copy', copy)
                    document.execCommand("Copy");
                    return false; //阻止表单跳转
                });
            }
        }
    }

    exports('product', func);
});