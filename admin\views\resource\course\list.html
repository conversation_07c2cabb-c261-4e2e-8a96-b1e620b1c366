﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <!--<style>
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>-->
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="请输入课程名称" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">类型</label>
                        <div class="layui-input-inline">
                            <select name="type" id="type">
                                <option value="">请选择课程类型</option>
                                <option value="课程">课程</option>
                                <option value="投顾">投顾</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="status" id="status">
                                <option value="">请选择课程状态</option>
                                <option value="1">正常销售</option>
                                <option value="2">暂停销售</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="course-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list course-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="course-list" lay-filter="list"></table>
                <script type="text/html" id="course-bar">
                    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="gen">购买链接</a>
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'course'], function () {
            layui.course.query();
            //监听查询按钮
            layui.form.on('submit(course-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("course-list", {
                    where: field
                });
            });
            //打开添加弹框
            layui.$('.course-create').click(function () {
                location.href = 'create.html';
            });
        });
    </script>
</body>
</html>