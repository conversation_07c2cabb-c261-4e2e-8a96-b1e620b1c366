﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 80px; }

        /* .layui-form {
            display: flex;
            flex-wrap: wrap;
        }

        .layui-form-item {
            width: 50%;
        } */

        .layui-form-item .layui-input-inline { width: 600px; }

        .file-list { width: 100%; margin-top: 5px; }

        .file-list li { width: 100%; line-height: 24px; padding: 3px 10px; box-sizing: border-box; float: left; display: flex; }

        .file-list li:hover { background: #eee; }

        .file-list li a { font-size: 14px; color: #0094ff; display: inline-block; width: 300px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden; }

        .file-list li i.layui-icon-link { font-size: 14px; }

        .file-list li i.remove { float: right; cursor: pointer; display: none; }

        .layui-form-mid .layui-table-view,
        .layui-table-body { max-height: 200px !important; }
        .log-item { display: none; }
    </style>
</head>

<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list"
         style="margin: 20px; padding: 20px; background-color: #fff;padding-bottom: 120px; overflow: auto;">
        <div class="layui-form-item" style="display: flex;">
            <div style="width: 50%;height: 38px;">
                <label class="layui-form-label">所属组织</label>
                <div class="layui-form-mid" id="subsidiaryId">-</div>
            </div>
            <div style="width: 50%; height: 38px;">
                <label class="layui-form-label">标题</label>
                <div class="layui-form-mid" id="title">-</div>
            </div>
        </div>
        <div class="layui-form-item" style="display: flex;">
            <div style="width: 50%; height: 38px;">
                <label class="layui-form-label">渠道名称</label>
                <div class="layui-form-mid" id="channelName">-</div>
            </div>
            <div style="width: 50%; height: 38px;">
                <label class="layui-form-label" style="width:100px;">对外内容路径</label>
                <div class="layui-form-mid" id="contentPath">-</div>
            </div>
        </div>
        <div class="layui-form-item log-item">
            <label class="layui-form-label">驳回记录</label>
            <div class="layui-form-mid" style="max-width:80%">
                <table id="logTable" lay-filter="list"></table>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">附件</label>
            <div class="layui-input-inline">
                <div style="float: left; width: 480px;">
                    <ul class="file-list">
                    </ul>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">内容</label>
            <div class="layui-form-mid" id="content" style="max-width:80%">-</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-form-mid" id="remark">-</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-inline">
                <input type="radio" name="status" value="3" title="通过">
                <input type="radio" name="status" value="4" title="驳回">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">审核意见</label>
            <div class="layui-input-inline">
                <input type="text" name="auditRemark" id="auditRemark" placeholder="请输入审核意见" autocomplete="off"
                       class="layui-input">
            </div>
        </div>
        <div class="layui-form-item" style="text-align: center; position: fixed;background-color: #fff;bottom: 0; left: 0;right: 0; margin-bottom: 0;   border-top: 1px solid #eee;
            padding: 30px 0;
            text-align: center;
            z-index: 999;
            box-shadow: 2px 1px 8px 1px rgba(0,0,0,.1);">
            <div class="layui-input-inline" style="float: none;">
                <input type="button" lay-submit="" id="btn" class="layui-btn layui-btn-normal"
                       style="margin-right: 30px;" lay-filter="audit-status-submit" value="确认审核">
                <input type="button" class="layui-btn layui-btn-primary" onclick="history.go(-1);" value="返回上一页">
            </div>
        </div>
    </div>
    <script id="subsidiary-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="annexTpl" type="text/html">
        {{#  layui.each(d.annex, function(index, item){ }}
        <li>
            <a href="{{item.url }}" target="_blank">
                {{item.name}}
            </a>
            <span>
                {{(item.size / 1024).toFixed(1)}}KB
            </span>
        </li>
        {{#  }); }}
    </script>
    <script id="queryContentTpl" type="text/html">
        <div>
            <span style="color: #0094ff;" onclick="goInfo()">
                查看内容
            </span>
        </div>
    </script>
    <script src="/lib/tinymce/tinymce.min.js"></script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.3"></script>
    <script type="text/javascript">
        layui.use(['media', "table"], function () {
            layui.media.get();
            var isRead = layui.common.getUrlParam('isRead');
            if (isRead == 1) {
                layui.$('#btn').hide();
                layui.$('#auditRemark').attr('readonly', 'readonly');
                layui.$('#auditRemark').addClass('layui-disabled');
                layui.$('input[name=status]').attr('disabled', 'disabled');
            }
            //监听提交事件
            layui.form.on('submit(audit-status-submit)', function (data) {
                var checked = layui.$('input[name=status]:checked').val();
                if (checked == undefined || checked == '') {
                    layui.common.alertAutoClose('请选择审核状态');
                    return;
                }
                layui.media.updateStatusForHg(data);
                return false; //阻止表单跳转
            });
        })
        /**打开富文本弹窗 */
        function goInfo() {
            layui.common.openIframe('审核素材/研报', 1200, 700, 'content-info.html?id=' + layui.common.getUrlParam('id'));
        }
    </script>
</body>
</html>