﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 200px; }

        .red { font-size: 18px; }

        .layui-fluid { padding: 0; }

        .layui-card-header { padding: 20px 0 10px 0 !important; }

        .layui-card-body { padding: 0 }
    </style>
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <input id="sort" name="sort" type="hidden" value="0" />
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input"
                                   placeholder="订单号/产品/手机号/身份证号" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">支付方式</label>
                        <div class="layui-input-inline">
                            <select name="payType" id="payType">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">购买产品</label>
                        <div class="layui-input-inline">
                            <select name="productId" id="product-view" lay-search>
                                <option value="">请选择购买产品</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">所属用户</label>
                        <div class="layui-input-inline">
                            <select name="saleUserId" id="admin-user-view" lay-search>
                                <option value="">请选择订单所属用户</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">退款状态</label>
                        <div class="layui-input-inline">
                            <select name="refundStatus" id="refundStatus">
                                <option value="">请选择退款状态</option>
                                <option value="1">未退款</option>
                                <option value="2">已提交申请</option>
                                <option value="3">退款成功</option>
                                <option value="4">退款失败</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">支付时间</label>
                        <div class="layui-input-inline" style="width:150px">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间"
                                   autocomplete="off" />
                        </div>
                        <div class="layui-input-inline" style="width:150px">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间"
                                   autocomplete="off" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list " lay-submit lay-filter="product-order-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="product-order-list" lay-filter="product-order-list"></table>
                <script type="text/html" id="product-order-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
                    {{# if(layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.financial.audit')&&d.financialAuditStatus==1){}}
                    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="financial-audit">审核</a>
                    {{#}}}
                </script>
            </div>
        </div>
    </div>

    <div id="preview" class="preview" style="display:none;">
    </div>
    <script id="admin-user-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="paytype-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="product-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script type="text/html" id="topToolBar">
        <div class="layui-btn-container layui-input-inline">
            <button class="layui-btn layui-btn-sm layuiadmin-btn-list layui-btn-primary" lay-event="product-order-financial">
                <i class="layui-icon layui-icon-edit layuiadmin-button-btn"></i>批量审核
            </button>
        </div>
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=3.1"></script>
    <script type="text/javascript">
        var salerUserList = [];
        layui.use(['productOrder'], function () {
            layui.productOrder.initProduct();
            layui.productOrder.initPayTypeForSerch();
            layui.productOrder.initAdminUser('list');
            layui.productOrder.queryForFinancial();
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            layui.form.on('submit(product-order-search)', function (data) {
                if (data.field.payType == '') {
                    data.field.payType = -1;
                }
                if (data.field.refundStatus == '') {
                    data.field.refundStatus = -1;
                }
                if (data.field.scene == '') {
                    data.field.scene = -1;
                }
                layui.tableRequest.reload("product-order-list", {
                    where: data.field,
                    page: {
                        curr: 1
                    }
                });
            });
            layui.table.on('tool(product-order-list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    location.href = 'detail.html?id=' + data.id;
                }
                else if (obj.event == 'financial-audit') {
                    layui.common.openIframe('审核订单', 740, 580, 'audit-financial.html?id=' + data.id);
                }
            });
            if (!layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.copy')) {
                layui.$(document).bind("contextmenu", function () { return false; });
                layui.$(document).bind("selectstart", function () { return false; });
            }
        });
    </script>
</body>
</html>