﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 60px; }

        .layui-form-item .layui-input-inline { width: 600px; }

        .file-list { width: 100%; margin-top: 5px; }

        .file-list li { width: 100%; line-height: 24px; padding: 3px 10px; box-sizing: border-box; float: left; display: flex; }

        .file-list li:hover { background: #eee; }

        .file-list li a { font-size: 14px; color: #0094ff; display: inline-block; width: 300px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden; }

        .file-list li i.layui-icon-link { font-size: 14px; }

        .file-list li i.remove { float: right; cursor: pointer; }
    </style>
</head>

<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list"
         style="padding: 20px 30px 0 0;">
        <input id="classify" name="classify" type="hidden" value="" />
        <div class="layui-form-item">
            <label class="layui-form-label">所属组织</label>
            <div class="layui-input-inline">
                <select name="subsidiaryId" lay-verify="required" id="subsidiary-view" lay-search></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">标题</label>
            <div class="layui-input-inline">
                <input type="text" name="title" lay-verify="required" placeholder="请输入标题" autocomplete="off"
                       class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">渠道名称</label>
            <div class="layui-input-inline" style="width:230px">
                <input type="text" name="channelName" lay-verify="required" placeholder="请输入渠道名称" autocomplete="off"
                       class="layui-input">
            </div>
            <label class="layui-form-label" style="width:100px;">对外内容路径</label>
            <div class="layui-input-inline" style="width:230px">
                <select name="contentPath" lay-verify="required">
                    <option value="">请选择对外内容路径</option>
                    <option value="企业微信">企业微信</option>
                    <option value="新媒体">新媒体</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">附件</label>
            <div class="layui-input-inline">
                <div style="float:left;">
                    <button type="button" class="layui-btn layui-btn-primary" id="uploadAnnex">
                        <i class="layui-icon layui-icon-upload"></i>上传附件
                    </button>
                </div>

                <div style="float: left; width: 480px;">
                    <ul class="file-list">
                    </ul>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">内容</label>
            <div class="layui-input-inline">
                <textarea type="text" name="content" id="content" placeholder="请输入送审内容" autocomplete="off"
                          class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-inline">
                <input type="text" name="remark" placeholder="请输入备注" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-primary" lay-filter="media-create-submit"
                       value="保存">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="media-save-submit"
                       value="送审">
            </div>
        </div>
    </div>
    <script id="company-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="subsidiary-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/aliyun/aliyun-oss-sdk-6.18.0.min.js"></script>
    <script src="/lib/tinymce/tinymce.min.js"></script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.1"></script>
    <script type="text/javascript">
        layui.use(['form', 'media'], function () {
            var type = layui.common.getUrlParam('type');
            layui.$('#classify').val(type);
            layui.media.initFile();
            layui.media.initTinymce('');
            layui.media.initSubsidiary();
            //监听提交事件
            layui.form.on('submit(media-create-submit)', function (data) {
                data.field.companyId = layui.setter.productCompanyId;
                data.field.content = tinyMCE.activeEditor.getContent();
                data.field.status = 1;
                data.field.annex = [];
                var annex = layui.$('.file-list li');
                if (annex.length > 0) {
                    for (var i = 0; i < annex.length; i++) {
                        var that = layui.$(annex).eq(i);
                        data.field.annex.push({ name: that.find('a').text(), url: that.find('a').attr('href'), size: that.find('span').data('size') });
                    }
                }
                layui.media.create(data);
                return false; //阻止表单跳转
            });
            layui.form.on('submit(media-save-submit)', function (data) {
                data.field.companyId = layui.setter.productCompanyId;
                data.field.content = tinyMCE.activeEditor.getContent();
                data.field.status = 2;
                data.field.annex = [];
                var annex = layui.$('.file-list li');
                if (annex.length > 0) {
                    for (var i = 0; i < annex.length; i++) {
                        var that = layui.$(annex).eq(i);
                        data.field.annex.push({ name: that.find('a').text(), url: that.find('a').attr('href'), size: that.find('span').data('size') });
                    }
                }
                layui.media.create(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>