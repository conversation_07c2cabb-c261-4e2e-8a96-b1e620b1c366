﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        html { background: #fff }
        .file-item { border: 1px solid #eee; width: 250px; line-height: 50px; background: #f2f2f2; padding: 0 20px; margin-left: 20px; margin-bottom: 10px; }
        .file-item i { font-size: 20px; font-weight: bold; }
        .file-item span { margin-left: 2px; position: relative; top: -2px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="doc-view" style="padding: 20px 30px 0 0;">
    </div>

    <script id="doc-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <div class="file-item">
            <a href="{{item.fileUrl}}" target="_blank">
                <span>{{item.fileName}}</span>
            </a>
        </div>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['productOrder'], function () {
            layui.productOrder.viewContract();
        });
    </script>
</body>
</html>