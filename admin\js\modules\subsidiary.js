﻿layui.define(['laytpl', 'request', 'tableRequest', 'common', 'department', 'wechatPayConfg', 'alipayConfg'], function (exports) {
    var func = {
        /**
         * 渲染部门下拉选框
         * */
        initDepartment: function (id) {
            layui.department.queryByCompanyId(layui.setter.productCompanyId, function (res) {
                var json = [{ id: '', name: '请选择所属部门' }];
                for (var i = 0; i < res.result.length; i++) {
                    var level1 = { id: res.result[i].id, name: res.result[i].name };
                    json.push(level1);
                    var child2 = res.result[i].childs;
                    if (child2 != null && child2.length > 0) {
                        for (var j = 0; j < child2.length; j++) {
                            var level2 = { id: child2[j].id, name: res.result[i].name + ' > ' + child2[j].name };
                            json.push(level2);
                            var child3 = child2[j].childs;
                            if (child3 != null && child3.length > 0) {
                                for (var k = 0; k < child3.length; k++) {
                                    var level3 = { id: child3[k].id, name: res.result[i].name + ' > ' + child2[j].name + ' > ' + child3[k].name };
                                    json.push(level3);
                                    var child4 = child3[k].childs;
                                    if (child4 != null && child4.length > 0) {
                                        for (var m = 0; m < child4.length; m++) {
                                            var level4 = { id: child4[m].id, name: res.result[i].name + ' > ' + child2[j].name + ' > ' + child3[k].name + ' > ' + child4[m].name };
                                            json.push(level4);
                                            var child5 = child4[m].childs;
                                            if (child5 != null && child5.length > 0) {
                                                for (var n = 0; n < child5.length; n++) {
                                                    var level5 = { id: child5[n].id, name: res.result[i].name + ' > ' + child2[j].name + ' > ' + child3[k].name + ' > ' + child4[l].name + ' > ' + child5[n].name };
                                                    json.push(level5);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                var getTpl = document.getElementById("department-tpl").innerHTML
                    , view = document.getElementById('department-view');
                layui.laytpl(getTpl).render(json, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('department-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 渲染微信支付配置下拉选框
         * */
        initWechatPayConfig: function (id) {
            layui.wechatPayConfg.queryByCompanyId(layui.setter.productCompanyId, function (res) {
                res.result.unshift({ id: '', name: '请选择微信支付配置' });
                if (res.result.length > 0) {
                    for (var i = 0; i < res.result.length; i++) {
                        if (res.result[i].id != '') {
                            res.result[i].name = res.result[i].name + ' [商户号:' + res.result[i].mchId + ']'
                        }
                    }
                }
                var getTpl = document.getElementById("wechatpay-tpl").innerHTML
                    , view = document.getElementById('wechatpay-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('wechatpay-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
        * 渲染支付宝配置下拉选框
        * */
        initAlipayConfig: function (id) {
            layui.alipayConfg.queryByCompanyId(layui.setter.productCompanyId, function (res) {
                res.result.unshift({ id: '', name: '请选择支付宝配置' });
                if (res.result.length > 0) {
                    for (var i = 0; i < res.result.length; i++) {
                        if (res.result[i].id != '') {
                            res.result[i].name = res.result[i].name + ' [应用ID:' + res.result[i].appId + ']'
                        }
                    }
                }
                var getTpl = document.getElementById("alipay-tpl").innerHTML
                    , view = document.getElementById('alipay-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('alipay-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
        * 获取所有组织列表
        * */
        getAll: function (callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/basis/subsidiary/get-all',
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 通过id获取组织信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/basis/subsidiary/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=payDomain]').val(res.result.payDomain);
                    layui.subsidiary.initDepartment(res.result.departmentId);
                    layui.subsidiary.initWechatPayConfig(res.result.wechatPayConfigId);
                    layui.subsidiary.initAlipayConfig(res.result.aliPayConfigId);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取所有组织列表
         * @param {any} callbackFunc
         */
        getAll: function (callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/basis/subsidiary/get-all',
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                callbackFunc(res);
            })
        },
        /**
         * 获取组织列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'subsidiary-list', '/admin/basis/subsidiary/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '名称' },
                { field: 'departmentName', title: '绑定部门' },
                { field: 'payDomain', title: '支付域名' },
                { field: 'wechatPayConfigName', title: '微信支付配置' },
                { field: 'aliPayConfigName', title: '支付宝配置' },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 130, align: 'left', toolbar: '#subsidiary-bar' }
            ]);
            //监听表格事件
            layui.subsidiary.tableEvent();
        },
        /**
        * 创建组织
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/basis/subsidiary/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("subsidiary-list");
                    parent.layui.common.alertAutoClose("组织创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑组织
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/basis/subsidiary/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("subsidiary-list");
                    parent.layui.common.alertAutoClose("组织编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除组织
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/basis/subsidiary/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("subsidiary-list");
                    layui.common.alertAutoClose("组织删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑组织', 620, 450, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除当前组织吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.subsidiary.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('subsidiary', func);
});