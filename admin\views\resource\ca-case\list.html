﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 250px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属企微</label>
                        <div class="layui-input-inline">
                            <select name="workWxAppId" id="workwx-app-view">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="txb_kwd" name="keywords" type="text" class="layui-input" placeholder="方案名称" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="ca-case-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list ca-case-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list layui-btn-primary ca-case-quota" lay-submit lay-filter="ca-case-quota">
                            查看配额
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="ca-case-list" lay-filter="list"></table>
                <script type="text/html" id="ca-case-bar">
                    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="qrcode">二维码</a>
                    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="url">链接</a>
                    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="tag">标签</a>
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>

    <script type="text/html" id="topToolBar">
        <div class="layui-btn-container layui-input-inline">
            <button class="layui-btn layui-btn-sm layuiadmin-btn-list layui-btn-primary" lay-event="set-services">
                <i class="layui-icon layui-icon-edit layuiadmin-button-btn"></i>批量设置员工
            </button>
        </div>
    </script>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'caCase'], function () {
            layui.caCase.initApp();
            layui.caCase.query();
            //监听查询按钮
            layui.form.on('submit(ca-case-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("ca-case-list", {
                    where: field
                });
            });
            //打开添加弹框
            layui.$('.ca-case-create').click(function () {
                layui.common.openIframe('创建加粉方案', 740, 600, 'create.html');
            });
            layui.$('.ca-case-quota').click(function () {
                layui.common.openIframe('查看获客助手配额', 800, 500, 'list-quota.html');
            });
            layui.table.on('toolbar(list)', function (obj) {
                if (obj.event === 'set-services') {
                    var checkedList = layui.table.checkStatus(obj.config.id);
                    if (checkedList.data.length == 0) {
                        layui.common.alertAutoClose("请选择需要操作的记录");
                        return;
                    }
                    var ids = [];
                    for (var i = 0; i < checkedList.data.length; i++) {
                        ids.push(checkedList.data[i].id);
                    }
                    layui.common.openIframe('批量分配员工', 600, 500, 'set-services.html?ids=' + ids.toString());
                }
            });
        });
    </script>
</body>
</html>