﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 200px; }
        .red { font-size: 18px; }
        .layui-fluid { padding: 0; }
        .layui-card-header { padding: 20px 0 10px 0 !important; }
        .layui-card-body { padding: 0 }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <input id="sort" name="sort" type="hidden" value="0" />
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属组织</label>
                        <div class="layui-input-inline">
                            <select name="subsidiaryId" id="subsidiary-view" lay-search></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="订单号/产品/手机号/身份证号" />
                        </div>
                    </div>
                    <div class="layui-inline" id="fkAuditStatusItem">
                        <label class="layui-form-label">订单质检</label>
                        <div class="layui-input-inline" style="width:150px;">
                            <select name="fkAuditStatus" id="fkAuditStatus">
                                <option value="">请选择质检状态</option>
                                <option value="10">风险可控</option>
                                <option value="11">有风险</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline handled">
                        <label class="layui-form-label">拨打状态</label>
                        <div class="layui-input-inline">
                            <select name="callStatus" id="callStatus">
                                <option value="">请选择拨打状态</option>
                                <option value="0">未拨打</option>
                                <option value="1">拨打中</option>
                                <option value="2">已接通</option>
                                <option value="3">无人接听</option>
                                <option value="4">关机</option>
                                <option value="5">停号</option>
                                <option value="6">空号</option>
                                <option value="7">拒接</option>
                                <option value="9">用户正忙</option>
                                <option value="10">系统拦截</option>
                                <option value="11">线路拦截</option>
                                <option value="12">超时失效</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline handled">
                        <label class="layui-form-label">拨打结果</label>
                        <div class="layui-input-inline">
                            <select name="intentionCategoryId" id="intentionCategoryId">
                                <option value="">请选择拨打结果</option>
                                <option value="99">无触达</option>
                                <option value="0">未接通</option>
                                <option value="1">通过</option>
                                <option value="2">不通过</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">购买产品</label>
                        <div class="layui-input-inline">
                            <select name="productId" id="product-view" lay-search>
                                <option value="">请选择购买产品</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">所属用户</label>
                        <div class="layui-input-inline">
                            <select name="saleUserId" id="admin-user-view" lay-search>
                                <option value="">请选择订单所属用户</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">支付时间</label>
                        <div class="layui-input-inline" style="width:150px">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" autocomplete="off" />
                        </div>
                        <div class="layui-input-inline" style="width:150px">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" autocomplete="off" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list " lay-submit lay-filter="product-order-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="product-order-list" lay-filter="product-order-list"></table>
                <script type="text/html" id="product-order-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
                    {{# if(layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.fk.audit')&&d.fkAuditStatus==1){}}
                    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="fk-audit">质检</a>
                    {{#}}}
                </script>
            </div>
        </div>
    </div>

    <div id="preview" class="preview" style="display:none;">
    </div>
    <script id="subsidiary-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="admin-user-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="product-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=2.3"></script>
    <script type="text/javascript">
        var salerUserList = [];
        layui.use(['productOrder'], function () {
            layui.productOrder.initSubsidiary();
            layui.productOrder.initProduct();
            layui.productOrder.initAdminUser('list');
            layui.productOrder.queryForFK();
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            layui.form.on('submit(product-order-search)', function (data) {
                if (data.field.scene == '') {
                    data.field.scene = -1;
                }
                if (data.field.fkAuditStatus == '') {
                    data.field.fkAuditStatus = -1;
                }
                if (data.field.callStatus == '') {
                    data.field.callStatus = -1;
                }
                if (data.field.intentionCategoryId == '') {
                    data.field.intentionCategoryId = -1;
                }
                layui.tableRequest.reload("product-order-list", {
                    where: data.field,
                    page: {
                        curr: 1
                    }
                });
            });
            layui.table.on('tool(product-order-list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    location.href = 'detail.html?id=' + data.id;
                }
                else if (obj.event == 'fk-audit') {
                    layui.common.openIframe('订单质检', 570, 500, 'audit-fengkong.html?id=' + data.id);
                }
            });
            if (!layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.copy')) {
                layui.$(document).bind("contextmenu", function () { return false; });
                layui.$(document).bind("selectstart", function () { return false; });
            }
        });
    </script>
</body>
</html>