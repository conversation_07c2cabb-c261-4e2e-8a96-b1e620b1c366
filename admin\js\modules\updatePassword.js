﻿layui.define(['common', 'setter', 'request'], function (exports) {
    var func = {
        /**
         * 修改密码
         * */
        update: function (data) {
            var patrn = /^(\w){6,20}$/;
            if (!patrn.exec(data.field.newPassword)) {
                layui.common.alertAutoClose('密码格式有误，只能输入6-20个字母、数字、下划线 ');
                return;
            }
            if (data.field.newPassword != data.field.confirmPassword) {
                layui.common.alertAutoClose('两次密码输入不一致');
                return;
            }
            var encryptor = new JSEncrypt();
            encryptor.setPublicKey(layui.setter.rsaPublicKey);
            data.field.oriPassword = encryptor.encrypt(data.field.oriPassword);
            data.field.newPassword = encryptor.encrypt(data.field.newPassword);
            var data = JSON.stringify({ oriPassword: data.field.oriPassword, newPassword: data.field.newPassword });
            layui.request({
                requestBase: 'sso',
                method: 'post',
                url: '/admin/sso/user/password/update',
                data: data,
                headers: { 'Content-Type': 'application/json' }
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.delCookie(layui.setter.request.tokenName);
                    layui.common.alertAutoClose('修改成功，请重新登录');
                    setTimeout(function () {
                        parent.location.reload();
                    }, 2000)
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        }
    };

    exports('updatePassword', func);
});