﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 120px; }
        .layui-form-item .layui-input-inline { width: 400px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <input id="id" name="id" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">名称</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入产品包名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">投资顾问</label>
            <div class="layui-input-inline">
                <input type="text" name="teacherName" lay-verify="required" placeholder="请输入投资顾问名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">包含产品</label>
            <div class="layui-input-inline" id="product-item">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="product-package-create-submit" value="确认修改">
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        var xmSel;
        layui.use(['form', 'productPackage'], function () {
            layui.productPackage.get();
            //监听提交事件
            layui.form.on('submit(product-package-create-submit)', function (data) {
                data.field.productIds = [];
                var productIds = xmSel.getValue('value');
                if (productIds.length > 0) {
                    data.field.productIds = productIds;
                }
                layui.productPackage.update(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>