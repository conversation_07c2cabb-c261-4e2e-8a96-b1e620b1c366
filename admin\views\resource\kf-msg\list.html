﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        /* .layui-form-item .layui-input-inline { width: 300px; }*/
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属企微</label>
                        <div class="layui-input-inline">
                            <select name="workWxAppId" id="workwx-app-view" lay-filter="workwx-app-id">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">加粉方案</label>
                        <div class="layui-input-inline">
                            <select name="kfCaseId" id="kf-case-view" lay-search>
                                <option value="">请选择加粉方案</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="客服ID/外部联系人ID" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">

                    <label class="layui-form-label">创建日期</label>
                    <div class="layui-input-inline">
                        <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" />
                    </div>
                    <div class="layui-input-inline">
                        <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" />
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="kf-msg-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="kf-msg-list" lay-filter="list"></table>
                <script type="text/html" id="kf-msg-bar">
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="send">重发</a>
                </script>
            </div>
        </div>
    </div>

    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="kf-case-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['kfMsg'], function () {
            layui.kfMsg.initApp();
            layui.kfMsg.query();
            layui.kfMsg.bindEvent();
        });
    </script>
</body>
</html>