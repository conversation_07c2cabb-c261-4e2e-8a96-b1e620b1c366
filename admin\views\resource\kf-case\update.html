﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 550px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
        .xiaoetong { display: none; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <input id="id" name="id" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">方案名称</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入企微加粉方案名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">所属企微</label>
            <div class="layui-input-inline">
                <select name="workWxAppId" id="workwx-app-view" lay-filter="workwx-app" lay-verify="required">
                    <option value="">请选择所属企微</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">微信客服</label>
            <div class="layui-input-inline">
                <select name="openKfId" id="openkf-id" lay-search>
                    <option value="">请选择微信客服</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">使用场景</label>
            <div class="layui-input-inline">
                <select name="scene" id="scene" lay-filter="scene" lay-verify="required">
                    <option value="">请选择使用场景</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item tencent-ad-account" style="display:none;">
            <label class="layui-form-label">推广账户</label>
            <div class="layui-input-inline">
                <select name="tencentAdAccountId" id="tencentAdAccountId">
                    <option value="">请选择推广账户</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">欢迎语开始文案</label>
            <div class="layui-input-inline">
                <textarea type="text" id="welcomeStartMessage" name="welcomeStartMessage" placeholder="请输入欢迎语开始文案" autocomplete="off" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">菜单项配置</label>
            <div style="width: 560px; float: left; ">
                <div class="menu-input-list" style="float:left;">
                </div>
                <button type="button" class="layui-btn layui-btn-primary add-menu">
                    <i class="layui-icon">&#xe654;</i> 添加菜单
                </button>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">欢迎语结束文案</label>
            <div class="layui-input-inline">
                <textarea type="text" id="welcomeEndMessage" name="welcomeEndMessage" placeholder="请输入欢迎语结束文案" autocomplete="off" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">回传配置</label>
            <div class="layui-input-inline">
                <input type="text" name="baiDuToken" placeholder="百度Token/回传配置" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">广告账户ID</label>
            <div class="layui-input-inline">
                <input type="text" name="tencentAdPageId" placeholder="请输入腾讯广告账户ID" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">渠道标识</label>
            <div class="layui-input-inline">
                <input type="text" name="state" placeholder="请输入活码渠道标识" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">无效资源比例</label>
            <div class="layui-input-inline">
                <input type="text" name="invalidRate" lay-verify="required" placeholder="请输入无效资源比例（例:扣除5%填写0.05）" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item xiaoetong">
            <label class="layui-form-label">小鹅通规则ID</label>
            <div class="layui-input-inline">
                <input type="text" name="xiaoetongRuleId" placeholder="请输入小鹅通规则ID" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item xiaoetong">
            <label class="layui-form-label">小鹅通客服链接</label>
            <div class="layui-input-inline">
                <input type="text" name="xiaoetongKfUrl" placeholder="请输入小鹅通客服链接" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-block">
                <label class="layui-form-label">二维码</label>
                <div class="layui-input-block">
                    <button type="button" class="layui-btn layui-btn-primary" id="uploadImg">
                        <i class="layui-icon">&#xe67c;</i>请上传一张二维码图片
                    </button>
                    <div class="layui-inline  uploadimg" id="uploadDemoView"><img height="50px" /></div>
                    <input id="qrCodeUrl" name="qrCodeUrl" type="hidden" />
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="kf-case-submit" value="确认修改">
            </div>
        </div>
    </div>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['kfCase'], function () {
            layui.kfCase.get();
            layui.kfCase.bindEvent('update');
            layui.uploadFile('resource', 'uploadImg', '/common/file/public/put?folder=workwx/kfcase/qrcode', function (res) {
                if (res.isSuccess) {
                    layui.$("#qrCodeUrl").val(res.result);
                    layui.common.alertAutoClose("上传成功");
                    layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.result);
                }
                else {
                    layui.common.alert(res.message, 2);
                }
            });
            //神光显示小鹅通配置
            if (layui.setter.companyId == '6340ec834d7e5a21ed46e995') {
                layui.$('.xiaoetong').show();
            }
        });
    </script>
</body>
</html>