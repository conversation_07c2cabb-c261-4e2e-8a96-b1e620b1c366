﻿layui.define(['laytpl', 'request', 'tableRequest', 'common'], function (exports) {
    var func = {
        /**
         * 是否已配置坐席
         * @param {any} callbackFund
         */
        isExist: function (callbackFund) {
            layui.request({
                method: 'post',
                url: '/admin/callcenter/seats/exist',
            }).then(function (res) {
                callbackFund(res);
            })
        },
        /**
         * 通过用户id获取坐席信息
         * @param {any} id
         * @param {any} callbackFund
         */
        getByUserId: function (id, callbackFund) {
            layui.request({
                method: 'post',
                url: '/admin/callcenter/seats/get?userId=' + id,
            }).then(function (res) {
                callbackFund(res);
            })
        },
        /**
         * 通过id获取员工坐席信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/callcenter/seats/get?userId=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=userId]').val(id);
                    if (res.result != null) {
                        layui.$('input[name=name]').val(res.result.name);
                        layui.$('input[name=account]').val(res.result.account);
                        layui.$('input[name=password]').val(res.result.password);
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },

        /**
         * 编辑员工坐席
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/callcenter/seats/save',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("admin-user-list");
                    parent.layui.common.alertAutoClose("坐席编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
    }
    exports('seats', func);
});