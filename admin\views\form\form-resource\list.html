﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-btn-container .layui-unselect { margin-right: 10px !important; margin-bottom: 8px !important; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属客户</label>
                        <div class="layui-input-inline">
                            <select name="customerId" id="customer-view">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">域名</label>
                        <div class="layui-input-inline">
                            <select name="domainName" id="domain-view" lay-search>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">落地页</label>
                        <div class="layui-input-inline">
                            <select name="pagePath" id="page-view" lay-search>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="status" id="status-view">
                                <option value="">请选择状态</option>
                                <option value="1">正常</option>
                                <option value="2">已删除</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" autocomplete="off" placeholder="关键词/姓名/手机号/代码" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">渠道</label>
                        <div class="layui-input-inline">
                            <input id="channel" name="channel" type="text" class="layui-input" autocomplete="off" placeholder="来源渠道" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">创建日期</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" class="layui-input" autocomplete="off" placeholder="开始时间" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" class="layui-input" autocomplete="off" placeholder="结束时间" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="form-resource-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <input type="checkbox" id="auto-refresh" name="auto-refresh" title="自动刷新" lay-skin="primary" lay-filter="auto-refresh">
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="form-resource-list" lay-filter="list"></table>
                <script type="text/html" id="form-resource-bar">
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>
    <script type="text/html" id="topToolBar">
        <div class="layui-btn-container">
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="batch-del">批量删除资源</button>
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="batch-distribution">批量分配资源</button>
            <button class="layui-btn layui-btn-sm " lay-event="export-form">导出表单资源</button>
            <button class="layui-btn layui-btn-sm " lay-event="export-form-clue">导出表单线索</button>
            <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="clear">软删除表单资源</button>
            <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="physical-clear">物理删除表单资源</button>
        </div>
    </script>
    <div class="layui-form" id="distribution-dialog" style="padding: 20px 0 0 0;display:none;">
        <input id="ids" name="ids" value="" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">所属客户</label>
            <div class="layui-input-inline">
                <select name="customerId2" id="customer-view-2" lay-verify="required"></select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="form-distribution-submit" value="确认分配">
            </div>
        </div>
    </div>
    <div class="layui-form" id="distribution-dialog2" style="padding: 20px 0 0 0;display:none;">
        <input id="id" name="id" value="" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">所属客户</label>
            <div class="layui-input-inline">
                <select name="customerId3" id="customer-view-3" lay-verify="required"></select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="form-distribution-submit-3" value="确认分配">
            </div>
        </div>
    </div>
    <script id="customer-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.alias}}</option>
        {{#  }); }}
    </script>
    <script id="domain-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.domainName}}">
            {{# if(item.domainName!=''){}}
            {{item.domainName}}
            {{# }else{}}
            请选择域名
            {{# }}}
        </option>
        {{#  }); }}
    </script>
    <script id="page-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.path}}">
            {{item.title}}
            {{# if(item.path!=''){}}
            【{{item.path}}】
            {{# }}}
        </option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.1"></script>
    <script type="text/javascript">
        layui.use(['common', 'formResource'], function () {
            layui.formResource.initCustomer();
            layui.formResource.initDomain();
            layui.formResource.initPage();
            layui.formResource.query();
            layui.formResource.bindEvent();
        })
    </script>
</body>
</html>