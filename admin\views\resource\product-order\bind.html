﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 420px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
        .img-item { display: inline-block; position: relative; margin-right: 5px; }
        .img-item span.remove { position: absolute; cursor: pointer; right: 0; top: 0; display: inline-block; width: 15px; height: 15px; text-align: center; line-height: 15px; background: #000; opacity: 0.5; color: #fff }
        .pay-info { margin-bottom: 10px; margin-bottom: 10px; float: left; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">客户</label>
            <div class="layui-input-inline">
                <input type="text" name="mobile" lay-verify="required" placeholder="客户手机号" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">购买产品</label>
            <div class="layui-input-inline">
                <select name="productId" id="product-view" lay-verify="required" lay-filter="product-view" lay-search>
                    <option value="">请选择购买产品</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">所属企微</label>
            <div class="layui-input-inline">
                <select name="workWxAppId" id="workwx-app-view" lay-filter="workwx-app" lay-verify="required">
                    <option value="">请选择所属企微</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item" style="display:none;">
            <label class="layui-form-label">客户来源</label>
            <div class="layui-input-inline">
                <select name="customerChannel" id="kf-case-view" lay-search>
                    <option value="">请选择客户来源渠道</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">企微联系人</label>
            <div class="layui-input-inline" id="external-contact">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="product-order-create-submit" value="确认绑定">
            </div>
        </div>
    </div>

    <script id="product-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        {{# if(item.id==''){}}
        <option value="" data-names="{{item.teacherNames}}">{{item.name}}</option>
        {{#}else{}}
        <option value="{{item.id}}" data-names="{{item.teacherNames}}">{{item.name}} - ¥{{item.amount}}</option>
        {{#}}}
        {{#  }); }}
    </script>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="kf-case-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=2.3"></script>
    <script type="text/javascript">
        var xmSel;
        var payTypeXmSel;
        layui.use(['form', 'common', 'productOrder'], function () {
            layui.productOrder.initProduct();
            layui.productOrder.initApp();
            layui.productOrder.searchExternalContact();

            layui.form.on('submit(product-order-create-submit)', function (data) {
                data.field.externalContactId = '';
                data.field.customerChannel = layui.$('#kf-case-view').find('option:selected').text();
                var externalContactIds = xmSel.getValue('value');
                if (externalContactIds.length > 0) {
                    data.field.externalContactId = externalContactIds[0];
                }
                if (data.field.externalContactId == '') {
                    layui.common.alertAutoClose("请选择对应的企微联系人");
                    return;
                }
                layui.productOrder.bind(data);
                return false; //阻止表单跳转
            });
        });
    </script>
</body>
</html>