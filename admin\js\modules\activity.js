﻿layui.define(['laytpl', 'form', 'xmSelect', 'request', 'laydate', 'tableRequest', 'common', 'product'], function (exports) {
    var func = {
        ///**
        // * 渲染产品列表
        // * @param {any} checkedIds
        // */
        //initProduct: function (checkedIds) {
        //    layui.product.getAll(function (res) {
        //        if (res.result != null && res.result.length > 0 && checkedIds.length > 0) {
        //            for (var i = 0; i < res.result.length; i++) {
        //                var isChecked = checkedIds.includes(res.result[i].id);
        //                res.result[i].isChecked = isChecked;
        //            }
        //        }
        //        var getTpl = document.getElementById("product-tpl").innerHTML
        //            , view = document.getElementById('product-list-view');
        //        layui.laytpl(getTpl).render(res.result, function (html) {
        //            view.innerHTML = html;
        //        });
        //        layui.form.render('checkbox');
        //    });
        //},
        /**
         * 渲染产品列表
         * @param {any} checkedIds
         */
        initProduct: function (selectIds) {
            layui.product.getAll(function (res) {
                var data = [];
                var selectItems = [];
                if (res.result.length > 0) {
                    for (var i = 0; i < res.result.length; i++) {
                        var item = { name: res.result[i].name + ' ¥' + res.result[i].amount + '【' + (res.result[i].duration + (res.result[i].unit == '月' ? '个' : '') + res.result[i].unit) + '】', value: res.result[i].id };
                        data.push(item);
                        if (selectIds.length > 0 && selectIds.indexOf(res.result[i].id) > -1) {
                            selectItems.push(item);
                        }
                    }
                }
                xmSel = layui.xmSelect.render({
                    el: '#product-item',
                    language: 'zn',
                    filterable: true,
                    tips: '请选择产品',
                    theme: { color: '#0081ff ' },
                    data: data,
                    toolbar: { show: true },
                    autoRow: true,
                });
                if (selectItems.length > 0) {
                    xmSel.setValue(selectItems);
                }
            });
        },
        /**
        * 上传封面
        * */
        initFile: function () {
            layui.uploadFile('resource', 'uploadImg', '/common/file/public/put?folder=files/product/activity/cover', function (res) {
                if (res.isSuccess) {
                    layui.$("#hid_coverUrl").val(res.result);
                    layui.common.alertAutoClose("上传成功");
                    layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.result);
                }
                else {
                    layui.common.alert(res.message, 2);
                }
            });
        },
        /**
        * 初始化富文本编辑器
        * */
        initTinymce: function () {
            tinymce.init({
                selector: '#detail',
                language: 'zh_CN',
                menubar: false,
                branding: false,
                plugins: 'preview searchreplace autolink directionality visualblocks visualchars fullscreen image link media template code codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists wordcount imagetools help emoticons autosave autoresize axupimgs',
                toolbar: 'code cut copy paste pastetext | forecolor backcolor link image media axupimgs | alignleft aligncenter alignright alignjustify outdent indent styleselect formatselect fontselect fontsizeselect bullist numlist bold italic underline strikethrough blockquote subscript superscript removeformat table charmap emoticons pagebreak insertdatetime preview fullscreen bdmap indent2em lineheight formatpainter',
                min_height: 400,
                max_height: 500,
                fontsize_formats: '12px 14px 16px 18px 24px 36px 48px 56px 72px',
                font_formats: '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;',
                importcss_append: true,
                images_upload_handler(blobInfo, progress) {
                    return new Promise((resolve, reject) => {
                        var upurl = layui.setter.request.resourceBaseUrl + '/common/file/public/tinymce/put?folder=files/product/detail';
                        var file = blobInfo.blob();
                        var xhr, formData;
                        xhr = new XMLHttpRequest();
                        xhr.withCredentials = false;
                        xhr.open('POST', upurl);
                        xhr.onload = function () {
                            if (xhr.status != 200) {
                                reject('HTTP Error: ' + xhr.status);
                                return;
                            }
                            var json = JSON.parse(xhr.responseText);
                            if (!json || json.location == '') {
                                reject(xhr.responseText);
                                return;
                            }
                            resolve(json.location);
                        };
                        formData = new FormData();
                        formData.append('file', file, file.name);
                        xhr.send(formData);
                    })
                },
                //自定义文件选择器的回调内容
                file_picker_callback: function (callback, value, meta) {
                    var filetype = '.pdf, .txt, .zip, .rar, .7z, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .mp3, .mp4';
                    var upurl = layui.setter.request.resourceBaseUrl + '/common/file/public/tinymce/put?folder=files/product/activity/detail';
                    switch (meta.filetype) {
                        case 'image':
                            filetype = '.jpg, .jpeg, .png, .gif';
                            break;
                        case 'media':
                            filetype = '.mp3, .mp4';
                            break;
                        default:
                    }
                    var input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    input.setAttribute('accept', filetype);
                    input.click();
                    input.onchange = function () {
                        var file = this.files[0];
                        var xhr, formData;
                        xhr = new XMLHttpRequest();
                        xhr.withCredentials = false;
                        xhr.open('POST', upurl);
                        xhr.onload = function () {
                            var json;
                            if (xhr.status != 200) {
                                failure('HTTP Error: ' + xhr.status);
                                return;
                            }
                            json = JSON.parse(xhr.responseText);
                            if (!json || typeof json.location != 'string') {
                                failure('Invalid JSON: ' + xhr.responseText);
                                return;
                            }
                            callback(json.location);
                        };
                        formData = new FormData();
                        formData.append('file', file, file.name);
                        xhr.send(formData);
                    };
                },
                autosave_ask_before_unload: false
            });
        },
        /**
         * 通过id获取单个活动信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/product/activity/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('#type').val(res.result.type);
                    layui.$('#status').val(res.result.status);
                    layui.$('input[name=startTime]').val(layui.common.timeFormat(res.result.startTime));
                    layui.$('input[name=endTime]').val(layui.common.timeFormat(res.result.endTime));
                    layui.$('#remark').val(res.result.remark);
                    layui.$('#desc').val(res.result.desc);
                    if (res.result.type == 1) {
                        //优惠券
                        layui.$('.coupon,.amount').show();
                        layui.$('input[name=amount]').val(res.result.amount);
                        layui.$('input[name=issueNumber]').val(res.result.issueNumber);
                        layui.$('input[name=timeLimit]').val(res.result.timeLimit);
                    }
                    else if (res.result.type == 2) {
                        //折扣
                        layui.$('.discount').show();
                        layui.$('input[name=discount]').val(res.result.discount);
                    }
                    else if (res.result.type == 3) {
                        //赠送时长
                        layui.$('.give').show();
                        layui.$('.amount').show();
                        layui.$('input[name=amount]').val(res.result.amount);
                        layui.$('input[name=duration]').val(res.result.duration);
                        layui.$('#unit').val(res.result.unit);
                    }
                    else if (res.result.type == 4) {
                        //换购时长
                        layui.$('.give,.amount').show();
                        layui.$('input[name=amount]').val(res.result.amount);
                        layui.$('input[name=duration]').val(res.result.duration);
                        layui.$('#unit').val(res.result.unit);
                    }
                    else if (res.result.type == 5) {
                        //优惠金额
                        layui.$('.amount').show();
                        layui.$('input[name=amount]').val(res.result.amount);
                    }
                    layui.form.render('select');
                    if (res.result.coverUrl != null && res.result.coverUrl != '') {
                        layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.result.coverUrl);
                        layui.$('#hid_coverUrl').val(res.result.coverUrl);
                    }
                    layui.$('#detail').val(res.result.detail);
                    layui.activity.initProduct(res.result.productIds);
                    layui.activity.initTinymce();
                    layui.activity.initFile();
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取活动列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'activity-list', '/admin/product/activity/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '活动名称' },
                {
                    title: '类型', width: 100, templet: function (e) {
                        var type = '-';
                        if (e.type == 1) {
                            type = '优惠券';
                        }
                        else if (e.type == 2) {
                            type = '产品折扣';
                        }
                        else if (e.type == 3) {
                            type = '赠送服务期';
                        }
                        else if (e.type == 4) {
                            type = '换购服务期';
                        }
                        else if (e.type == 5) {
                            type = '优惠金额';
                        }
                        return type;
                    }
                },
                {
                    title: '状态', width: 80, templet: function (e) {
                        return e.status == 1 ? '正常' : '下线';
                    }
                },
                {
                    field: 'auditStatus', title: '审核状态', width: 90, templet: function (e) {
                        var statusDesc = '-';
                        if (e.auditStatus == 1) {
                            statusDesc = '审核中';
                        }
                        else if (e.auditStatus == 2) {
                            statusDesc = '通过';
                        }
                        else if (e.auditStatus == 3) {
                            statusDesc = '驳回';
                        }
                        if (e.auditRemark != null && e.auditRemark != '') {
                            statusDesc += '（' + e.auditRemark + '）'
                        }
                        return statusDesc;
                    }
                },
                {
                    title: '活动信息', templet: function (e) {
                        var info = '-';
                        if (e.type == 1) {
                            info = '金额：' + e.amount + '，有效天数：' + e.timeLimit + '，发放数量：' + e.issueNumber + '，剩余数量：' + e.remainNumber;
                        }
                        else if (e.type == 2) {
                            info = '折扣：' + e.discount + '折';
                        }
                        else if (e.type == 3) {
                            info = '赠送时长：' + e.duration + (e.unit == '月' ? '个' : '') + e.unit;
                            if (e.amount > 0) {
                                info += '，优惠金额：' + e.amount;
                            }
                        }
                        else if (e.type == 4) {
                            info = '加' + e.amount + '元换购时长：' + e.duration + (e.unit == '月' ? '个' : '') + e.unit;
                        }
                        else if (e.type == 5) {
                            info = '金额：' + e.amount;
                        }
                        return info;
                    }
                },
                {
                    title: '活动时间', templet: function (e) {
                        return layui.common.timeFormat(e.startTime) + ' 至 ' + layui.common.timeFormat(e.endTime);
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 170, align: 'left', toolbar: '#activity-bar' }
            ], {});
            //监听表格事件
            layui.activity.tableEvent();
        },
        /**
         * 获取活动优惠券列表
         * */
        queryCoupon: function () {
            var activityId = layui.common.getUrlParam('id') || '';
            layui.tableRequest.request('resource', true, 'coupon-list', '/admin/product/coupon/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'mobile', title: '领取人' },
                {
                    title: '使用状态', templet: function (e) {
                        return e.useStatus == 1 ? '未使用' : '已使用-' + layui.common.timeFormat(e.useTime);
                    }
                },
                { field: 'amount', title: '优惠券金额' },
                {
                    title: '过期时间', templet: function (e) {
                        return layui.common.timeFormat(e.expiredTime);
                    }
                },
                {
                    field: 'createdAt', title: '领取时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
            ], { activityId: activityId });
        },
        /**
        * 创建单个活动
        * */
        create: function (data) {
            if (data.field.type == 1) {
                if (data.field.amount == '') {
                    layui.common.alertAutoClose("请输入优惠金额");
                    return;
                }
                else if (data.field.issueNumber == '') {
                    layui.common.alertAutoClose("请输入发行数量");
                    return;
                }
                else if (data.field.timeLimit == '') {
                    layui.common.alertAutoClose("请输入优惠券有效期天数");
                    return;
                }
                data.field.discount = 0;
                data.field.duration = 0;
                data.field.unit = '';
            }
            else if (data.field.type == 2) {
                if (data.field.discount == '') {
                    layui.common.alertAutoClose("请输入产品折扣");
                    return;
                }
                data.field.amount = 0;
                data.field.issueNumber = 0;
                data.field.timeLimit = 0;
                data.field.duration = 0;
                data.field.unit = '';
            }
            else if (data.field.type == 3) {
                if (data.field.duration == '') {
                    layui.common.alertAutoClose("请输入赠送周期");
                    return;
                }
                if (data.field.unit == '') {
                    layui.common.alertAutoClose("请选择赠送周期单位");
                    return;
                }
                if (data.field.amount == '') {
                    data.field.amount = 0;
                }
                data.field.issueNumber = 0;
                data.field.timeLimit = 0;
                data.field.discount = 0;
            }
            else if (data.field.type == 4) {
                if (data.field.amount == '') {
                    layui.common.alertAutoClose("请输入换购金额");
                    return;
                }
                if (data.field.duration == '') {
                    layui.common.alertAutoClose("请输入换购周期");
                    return;
                }
                if (data.field.unit == '') {
                    layui.common.alertAutoClose("请选择换购周期单位");
                    return;
                }
                data.field.issueNumber = 0;
                data.field.timeLimit = 0;
                data.field.discount = 0;
            }
            else if (data.field.type == 5) {
                if (data.field.amount == '') {
                    layui.common.alertAutoClose("请输入优惠金额");
                    return;
                }
                data.field.issueNumber = 0;
                data.field.timeLimit = 0;
                data.field.discount = 0;
                data.field.duration = 0;
                data.field.unit = '';
            }
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/activity/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("活动创建成功");
                    setTimeout(function () { location.href = 'list.html'; }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑单个活动
         * @param {any} data
         */
        update: function (data) {
            if (data.field.type == 1) {
                if (data.field.amount == '') {
                    layui.common.alertAutoClose("请输入优惠金额");
                    return;
                }
                else if (data.field.issueNumber == '') {
                    layui.common.alertAutoClose("请输入发行数量");
                    return;
                }
                else if (data.field.timeLimit == '') {
                    layui.common.alertAutoClose("请输入优惠券有效期天数");
                    return;
                }
                data.field.discount = 0;
                data.field.duration = 0;
                data.field.unit = '';
            }
            else if (data.field.type == 2) {
                if (data.field.discount == '') {
                    layui.common.alertAutoClose("请输入产品折扣");
                    return;
                }
                data.field.amount = 0;
                data.field.issueNumber = 0;
                data.field.timeLimit = 0;
                data.field.duration = 0;
                data.field.unit = '';
            }
            else if (data.field.type == 3) {
                if (data.field.duration == '') {
                    layui.common.alertAutoClose("请输入赠送周期");
                    return;
                }
                if (data.field.unit == '') {
                    layui.common.alertAutoClose("请选择赠送周期单位");
                    return;
                }
                if (data.field.amount == '') {
                    data.field.amount = 0;
                }
                data.field.issueNumber = 0;
                data.field.timeLimit = 0;
                data.field.discount = 0;
            } else if (data.field.type == 4) {
                if (data.field.amount == '') {
                    layui.common.alertAutoClose("请输入换购金额");
                    return;
                }
                if (data.field.duration == '') {
                    layui.common.alertAutoClose("请输入换购周期");
                    return;
                }
                if (data.field.unit == '') {
                    layui.common.alertAutoClose("请选择换购周期单位");
                    return;
                }
                data.field.issueNumber = 0;
                data.field.timeLimit = 0;
                data.field.discount = 0;
            }
            else if (data.field.type == 5) {
                if (data.field.amount == '') {
                    layui.common.alertAutoClose("请输入优惠金额");
                    return;
                }
                data.field.issueNumber = 0;
                data.field.timeLimit = 0;
                data.field.discount = 0;
                data.field.duration = 0;
                data.field.unit = '';
            }
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/activity/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("活动编辑成功");
                    setTimeout(function () { location.href = 'list.html'; }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 编辑产品活动审核状态
        * @param {any} data
        */
        updateAuditStatus: function (data) {
            var id = layui.common.getUrlParam('id');
            data.field.id = id;
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/activity/audit-status/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("activity-list");
                    parent.layui.common.alertAutoClose("活动审核成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除活动
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/product/activity/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("activity-list");
                    layui.common.alertAutoClose("活动删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    location.href = 'update.html?id=' + data.id;
                }
                else if (obj.event === 'audit') {
                    layui.common.openIframe('审核活动', 500, 300, 'audit.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该活动吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.activity.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        },
        /**
         * 绑定事件
         * @param {any} opType
         */
        bindEvent: function (opType) {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            if (opType == 'create') {
                layui.form.on('select(activity-type)', function (data) {
                    if (data.value == 1) {
                        layui.$('.coupon,.amount').show();
                        layui.$('.give,.discount').hide();
                    }
                    else if (data.value == 2) {
                        layui.$('.discount').show();
                        layui.$('.give,.coupon,.amount').hide();
                    }
                    else if (data.value == 3) {
                        layui.$('.give,.amount').show();
                        layui.$('.discount,.coupon').hide();
                    }
                    else if (data.value == 4) {
                        layui.$('.amount,.give').show();
                        layui.$('.discount,.coupon').hide();
                    }
                    else if (data.value == 5) {
                        layui.$('.amount').show();
                        layui.$('.discount,.coupon,.give').hide();
                    }
                    else {
                        layui.$('.give,.discount,.coupon').hide();
                    }
                });
                layui.form.on('submit(activity-submit)', function (data) {
                    data.field.productIds = [];
                    var productIds = xmSel.getValue('value');
                    if (productIds.length > 0) {
                        data.field.productIds = productIds;
                    }
                    data.field.detail = tinyMCE.activeEditor.getContent();
                    layui.activity.create(data);
                });
            }
            else if (opType == 'update') {
                layui.form.on('submit(activity-submit)', function (data) {
                    data.field.productIds = [];
                    var productIds = xmSel.getValue('value');
                    if (productIds.length > 0) {
                        data.field.productIds = productIds;
                    }
                    data.field.detail = tinyMCE.activeEditor.getContent();
                    layui.activity.update(data);
                });
            }
        }
    }

    exports('activity', func);
});