﻿layui.define(['common', 'setter', 'request'], function (exports) {
    var $ = layui.jquery;
    var setter = layui.setter;
    document.title = setter.name;
    $("#siteName").text(setter.name);
    if (setter.logo != '') {
        $("#siteLogo").attr('src', setter.logo);
    }
    else {
        $("#siteLogo").hide();
    }
    var func = {
        /**
         * 用户登录
         * */
        exec: function () {
            var username = layui.$("#txb_userName").val();
            var password = layui.$("#txb_password").val();
            var encryptor = new JSEncrypt();
            encryptor.setPublicKey(layui.setter.rsaPublicKey);
            password = encryptor.encrypt(password);
            var data = JSON.stringify({ account: username, password: password });
            layui.request({
                requestBase: 'sso',
                method: 'post',
                url: '/admin/sso/user/login',
                data: data,
                headers: { 'Content-Type': 'application/json' }
            }).then(function (res) {
                if (res.isSuccess) {
                    var exp = layui.common.timeFormat(res.result.expiredTime);
                    layui.common.setCookieExpires(layui.setter.request.tokenName, res.result.token, exp);
                    var appId = layui.common.getUrlParam('appId') || '';
                    var goUrl = 'index.html';
                    if (goUrl != '') {
                        goUrl += '?appId=' + appId
                    }
                    location.href = goUrl;
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        }
    };

    exports('login', func);
});