﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 350px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
        .img-item { display: inline-block; position: relative; margin-right: 5px; }
        .img-item span.remove { position: absolute; cursor: pointer; right: 0; top: 0; display: inline-block; width: 15px; height: 15px; text-align: center; line-height: 15px; background: #000; opacity: 0.5; color: #fff }
        .pay-info { margin-bottom: 10px; margin-bottom: 10px; float: left; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">微信公众号</label>
            <div class="layui-input-inline">
                <select name="appId" id="account-view" lay-verify="required" lay-search>
                    <option value="">请选择微信公众号</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">员工编号</label>
            <div class="layui-input-inline">
                <input id="staffId" name="staffId" lay-verify="required" type="text" class="layui-input" placeholder="请输入员工编号，不可重复" />
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">员工姓名</label>
            <div class="layui-input-inline">
                <input id="staffId" name="staffName" lay-verify="required" type="text" class="layui-input" placeholder="请输入员工姓名" />
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="staff-submit" value="确认添加">
            </div>
        </div>
    </div>
    <script id="account-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.appId}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=2.2"></script>
    <script type="text/javascript">
        var payTypeXmSel;
        layui.use([ 'staffQrCode'], function () {
            layui.staffQrCode.initAccount();
            //监听提交事件
            layui.form.on('submit(staff-submit)', function (data) {
                layui.staffQrCode.create(data);
                return false; //阻止表单跳转
            });
        });
    </script>
</body>
</html>