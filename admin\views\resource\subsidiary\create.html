﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 400px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">

        <div class="layui-form-item">
            <label class="layui-form-label">名称</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入组织名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">绑定部门</label>
            <div class="layui-input-inline">
                <select name="departmentId" id="department-view" lay-search></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">支付域名</label>
            <div class="layui-input-inline">
                <input type="text" name="payDomain" placeholder="请输入支付域名" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">微信支付配置</label>
            <div class="layui-input-inline">
                <select name="wechatPayConfigId" id="wechatpay-view" lay-search></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">支付宝配置</label>
            <div class="layui-input-inline">
                <select name="aliPayConfigId" id="alipay-view" lay-search></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="submit" value="确认添加">
            </div>
        </div>
    </div>
    <script id="department-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="wechatpay-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="alipay-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'subsidiary'], function () {
            layui.subsidiary.initDepartment();
            layui.subsidiary.initWechatPayConfig();
            layui.subsidiary.initAlipayConfig();

            //监听提交事件
            layui.form.on('submit(submit)', function (data) {
                layui.subsidiary.create(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>