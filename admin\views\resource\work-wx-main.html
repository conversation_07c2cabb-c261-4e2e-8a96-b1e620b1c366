﻿<!DOCTYPE html>
<html style="background-color: #f0f2f5;">
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <style>
        body { -webkit-font-smoothing: antialiased; text-rendering: optimizeLegibility; font-family: Helvetica Neue,Helvetica,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Arial,sans-serif; }
        .con { float: left; width: 100%; }
        .card { width: 320px; padding-left: 20px; padding-bottom: 20px; float: left; box-sizing: border-box; }
        .card-panel { height: 108px; cursor: pointer; font-size: 12px; position: relative; overflow: hidden; color: #666; background: #fff; -webkit-box-shadow: 4px 4px 40px rgb(0 0 0 / 5%); box-shadow: 4px 4px 40px rgb(0 0 0 / 5%); border-color: rgba(0,0,0,.05); }
        .card-panel-icon-wrapper { float: left; margin: 14px 0 0 14px; padding: 16px; -webkit-transition: all .38s ease-out; transition: all .38s ease-out; border-radius: 6px; }
        .card-panel-description { text-align: right; float: right; font-weight: 700; margin: 26px; margin-left: 0; }
        .card-panel-text { line-height: 18px; color: rgba(0,0,0,.45); font-size: 16px; margin-bottom: 12px; }
        .card-panel-num { font-size: 20px; color: #666; font-weight: 700; }
    </style>
</head>
<body>
    <div class="layui-form" style="padding:20px 20px 0 20px;box-sizing:border-box">
        <blockquote class="layui-elem-quote" style="background:#fff;padding:20px;box-sizing:border-box;">
            <div class="layui-inline">
                <label class="layui-form-label">所属企微</label>
                <div class="layui-input-inline">
                    <select name="workWxAppId" id="workwx-app-view" lay-filter="workwx-app-search" lay-search>
                    </select>
                </div>
            </div>
            <div class="layui-inline" style="margin-left:10px;">
                <div class="layui-input-inline">
                    <input type="checkbox" name="autoRefreshData" lay-skin="primary" lay-filter="auto-refresh-data" title="自动更新数据">
                </div>
                <div class="layui-input-inline">
                    <div class="layui-form-mid layui-word-aux tips"></div>
                </div>
            </div>
        </blockquote>
    </div>

    <div class="con">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>企微用户数统计（客服链路）</legend>
        </fieldset>
        <div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-group" style="font-size: 48px; color: #40c9c6"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">总用户数</div>
                        <span class="card-panel-num" id="userTotalCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-friends" style="font-size: 48px; color: #36a3f7"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">昨日新增人数</div>
                        <span class="card-panel-num" id="userYesterdayCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-add-circle" style="font-size: 48px; color: #f4516c"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text"> 今日新增人数</div>
                        <span class="card-panel-num" id="userTodayCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-group" style="font-size: 48px; color: #c8b6eb"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">总转化人数</div>
                        <span class="card-panel-num" id="userTotalConversionCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-user" style="font-size: 48px; color: #8093c2"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">昨日新增转化人数</div>
                        <span class="card-panel-num" id="userYesterdayConversionCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-add-1" style="font-size: 48px; color: #40c9c6"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">今日新增转化人数</div>
                        <span class="card-panel-num" id="userTodayConversionCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="con">
        <div id="main" style="width:100%;height:400px;padding:20px;box-sizing:border-box;"></div>
    </div>
    <div class="con" style="margin-top:20px;">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>企微用户数统计（小程序链路）</legend>
        </fieldset>
        <div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-group" style="font-size: 48px; color: #40c9c6"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">总用户数</div>
                        <span class="card-panel-num" id="userMiniTotalCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-friends" style="font-size: 48px; color: #36a3f7"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">昨日新增人数</div>
                        <span class="card-panel-num" id="userMiniYesterdayCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-add-circle" style="font-size: 48px; color: #f4516c"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text"> 今日新增人数</div>
                        <span class="card-panel-num" id="userMiniTodayCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-group" style="font-size: 48px; color: #c8b6eb"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">总转化人数</div>
                        <span class="card-panel-num" id="userMiniTotalConversionCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-user" style="font-size: 48px; color: #8093c2"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">昨日新增转化人数</div>
                        <span class="card-panel-num" id="userMiniYesterdayConversionCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-add-1" style="font-size: 48px; color: #40c9c6"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">今日新增转化人数</div>
                        <span class="card-panel-num" id="userMiniTodayConversionCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="con">
        <div id="main-mini" style="width:100%;height:400px;padding:20px;box-sizing:border-box;"></div>
    </div>
    <div class="con">
        <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
            <legend>客服消息数统计</legend>
        </fieldset>
        <div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-dialogue" style="font-size: 48px; color: #40c9c6"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">客服消息总数</div>
                        <span class="card-panel-num" id="kfMsgTotalCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-ok" style="font-size: 48px; color: #36a3f7"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">打开微信客服会话数</div>
                        <span class="card-panel-num" id="kfMsgOpenSessionTotalCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-release" style="font-size: 48px; color: #f4516c"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text"> 用户发送文本消息数</div>
                        <span class="card-panel-num" id="kfMsgSendTextMsgTotalCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <!--<div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-group" style="font-size: 48px; color: #c8b6eb"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">[图片/事件/文件]客服消息数</div>
                        <span class="card-panel-num" id="kfMsgSendOtherTotalCount">
                            --
                        </span>
                    </div>
                </div>
            </div>-->
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-chat" style="font-size: 48px; color: #8093c2"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">昨日客服消息总数</div>
                        <span class="card-panel-num" id="kfMsgYesterdayCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-ok-circle" style="font-size: 48px; color: #40c9c6"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">昨日打开微信客服会话数</div>
                        <span class="card-panel-num" id="kfMsgOpenSessionYesterdayCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-file-b" style="font-size: 48px; color: #36a3f7"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">昨日用户发送文本消息数</div>
                        <span class="card-panel-num" id="kfMsgSendTextMsgYesterdayCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <!--<div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-add-1" style="font-size: 48px; color: #40c9c6"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">昨日[图片/事件/文件]客服消息数</div>
                        <span class="card-panel-num" id="kfMsgSendOtherYesterdayCount">
                            --
                        </span>
                    </div>
                </div>
            </div>-->
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-notice" style="font-size: 48px; color: #40c9c6"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">今日客服消息总数</div>
                        <span class="card-panel-num" id="kfMsgTodayCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-layer" style="font-size: 48px; color: #8093c2  "></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">今日打开微信客服会话数</div>
                        <span class="card-panel-num" id="kfMsgOpenSessionTodayCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-list" style="font-size: 48px; color: #f4516c "></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">今日用户发送文本消息数</div>
                        <span class="card-panel-num" id="kfMsgSendTextMsgTodayCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <!--<div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-add-1" style="font-size: 48px; color: #40c9c6"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">今日[图片/事件/文件]客服消息数</div>
                        <span class="card-panel-num" id="kfMsgSendOtherTodayCount">
                            --
                        </span>
                    </div>
                </div>
            </div>-->
        </div>
    </div>

    <div class="con">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>落地页按钮点击数</legend>
        </fieldset>
        <div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-edit" style="font-size: 48px; color: #40c9c6"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">落地页按钮总点击次数</div>
                        <span class="card-panel-num" id="clickRecordTotalCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-edit" style="font-size: 48px; color: #36a3f7"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">昨日落地页按钮点击次数</div>
                        <span class="card-panel-num" id="clickRecordYesterdayCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-panel">
                    <div class="card-panel-icon-wrapper"><i class="layui-icon layui-icon-edit" style="font-size: 48px; color: #f4516c"></i></div>
                    <div class="card-panel-description">
                        <div class="card-panel-text"> 今日落地页按钮点击次数</div>
                        <span class="card-panel-num" id="clickRecordTodayCount">
                            --
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/lib/echarts/echarts.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['workwxMain'], function () {
            layui.workwxMain.init();
        });
    </script>
</body>
</html>