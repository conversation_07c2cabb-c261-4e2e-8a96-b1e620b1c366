﻿layui.define(['laytpl', 'form', 'request', 'tableRequest', 'common', 'customer'], function (exports) {
    var func = {
        /**
         * 渲染客户列表
         * @param {any} companyId
         * @param {any} selectedId
         */
        initCustomer: function (selectedId) {
            layui.customer.getAll(function (res) {
                res.result.unshift({ id: '', alias: '请选择客户' });
                var getTpl = document.getElementById("customer-tpl").innerHTML
                    , view = document.getElementById('customer-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (selectedId != undefined) {
                    view.value = selectedId;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过id获取配置信息
         * */
        get: function (type) {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                setTimeout(function () {
                    parent.location.reload();
                }, 3000);
                return;
            }
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/page-qrcode/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    if (type == 'customer') {
                        layui.$('input[name=id]').val(res.result.id);
                        layui.$('input[name=title]').val(res.result.title);
                        layui.$('input[name=pageUrl]').val(res.result.pageUrl);
                        layui.$('input[name=showText]').val(res.result.showText);
                        layui.$('input[name=qrCodeUrl]').val(res.result.qrCodeUrl);
                        layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.result.qrCodeUrl);
                    }
                    else {
                        layui.$('input[name=id]').val(res.result.id);
                        layui.$('input[name=configName]').val(res.result.configName);
                        layui.$('input[name=title]').val(res.result.title);
                        layui.$('input[name=pageUrl]').val(res.result.pageUrl);
                        layui.$('input[name=showText]').val(res.result.showText);
                        layui.$('input[name=qrCodeUrl]').val(res.result.qrCodeUrl);
                        layui.$('#desc').val(res.result.desc);
                        layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.result.qrCodeUrl);
                        layui.pageQrCode.initCustomer(res.result.customerId);
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },

        /**
        * 获取落地页二维码配置列表
        * */
        query: function () {
            layui.tableRequest.request('form', true, 'page-qrcode-list', '/admin/basis/page-qrcode/query', 'application/json', [
                { field: 'id', title: '标识' },
                { field: 'customerName', title: '所属客户' },
                { field: 'title', title: '落地页标题' },
                { field: 'pageUrl', title: '落地页地址' },
                {
                    field: 'qrCodeUrl', title: '二维码', width: 80, align: 'center', templet: function (e) {
                        if (e.qrCodeUrl != '') {
                            return '<a target="_blank" href="' + e.qrCodeUrl + '"><img src="' + e.qrCodeUrl + '" height="30px"/></a>';
                        }
                        return '-';
                    }
                },
                { field: 'showText', title: '显示文案' },
                {
                    field: 'createdAt', title: '创建时间', templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', align: 'left', toolbar: '#page-qrcode-bar' }
            ]);
            //监听表格事件
            layui.pageQrCode.tableEvent();
        },
        /**
        * 获取落地页二维码配置列表【客户端】
        * */
        queryByCustomer: function () {
            layui.tableRequest.request('form', false, 'page-qrcode-list', '/admin/basis/page-qrcode/by-userid/query', 'application/json', [
                { field: 'id', title: '标识' },
                { field: 'title', title: '落地页标题' },
                {
                    field: 'qrCodeUrl', title: '二维码', width: 120, align: 'center', templet: function (e) {
                        if (e.qrCodeUrl != '') {
                            return '<a target="_blank" href="' + e.qrCodeUrl + '"><img src="' + e.qrCodeUrl + '" height="30px"/></a>';
                        }
                        return '-';
                    }
                },
                { field: 'showText', title: '显示文案' },
                { fixed: 'right', title: '操作', width: 80, align: 'left', toolbar: '#page-qrcode-bar' }
            ]);
            //监听表格事件
            layui.pageQrCode.tableEvent();
        },
        /**
        * 创建单个落地页二维码配置
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/page-qrcode/create',
                data: JSON.stringify({
                    customerId: data.field.customerId,
                    configName: data.field.configName,
                    desc: data.field.desc,
                    title: data.field.title,
                    pageUrl: data.field.pageUrl,
                    qrCodeUrl: data.field.qrCodeUrl,
                    showText: data.field.showText
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("落地页二维码创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑配置信息
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/page-qrcode/update',
                data: JSON.stringify({
                    id: data.field.id,
                    customerId: data.field.customerId,
                    configName: data.field.configName,
                    desc: data.field.desc,
                    title: data.field.title,
                    pageUrl: data.field.pageUrl,
                    qrCodeUrl: data.field.qrCodeUrl,
                    showText: data.field.showText
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("落地页二维码编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 编辑配置信息【客户端】
        * @param {any} data
        */
        updateByCustomer: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/page-qrcode/by-customer/update',
                data: JSON.stringify({
                    id: data.field.id,
                    title: data.field.title,
                    pageUrl: data.field.pageUrl,
                    qrCodeUrl: data.field.qrCodeUrl,
                    showText: data.field.showText
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("落地页二维码编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除配置信息
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/page-qrcode/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("page-qrcode-list");
                    layui.common.alertAutoClose("落地页二维码删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑落地页二维码配置信息', 600, 610, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'edit-qrcode') {
                    layui.common.openIframe('编辑落地页二维码配置信息', 500, 350, 'update-customer.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该落地页二维码配置吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.pageQrCode.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('pageQrCode', func);
});