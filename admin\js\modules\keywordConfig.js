﻿layui.define(['laytpl', 'form', 'request', 'tableRequest', 'common', 'customer', 'domainConfig', 'pageConfig'], function (exports) {
    var func = {
        /**
         * 渲染客户列表
         * @param {any} companyId
         * @param {any} selectedId
         */
        initCustomer: function (selectedId) {
            layui.customer.getAll(function (res) {
                res.result.unshift({ id: '', alias: '请选择客户' });
                var getTpl = document.getElementById("customer-tpl").innerHTML
                    , view = document.getElementById('customer-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (selectedId != undefined) {
                    view.value = selectedId;
                }
                layui.form.render('select');
            });
        },
        /**
        * 渲染落地页列表
        */
        initPage: function () {
            layui.pageConfig.getAll(function (res) {
                res.result.unshift({ id: '', title: '请选择落地页', path: '' });
                var getTpl = document.getElementById("page-tpl").innerHTML
                    , view = document.getElementById('page-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('select');
            });
        },
        /**
         * 渲染域名列表
         * @param {any} customerId
         */
        initDomain: function (customerId) {
            layui.domainConfig.queryByCustomerId(customerId, function (res) {
                if (res.result == null)
                    res.result = [{ id: '', domainName: '请选择域名' }]
                else
                    res.result.unshift({ id: '', domainName: '请选择域名' });

                var getTpl = document.getElementById("domain-tpl").innerHTML
                    , view = document.getElementById('domain-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('select');
            });
        },
        /**
         * 获取关键词列表
         * */
        query: function () {
            layui.tableRequest.request('form', true, 'keyword-config-list', '/admin/basis/keyword-config/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left', width: 80 },
                { field: 'customerName', title: '所属客户' },
                { field: 'identification', title: '关键词标识' },
                { field: 'name', title: '关键词名称', minWidth: 300 },
                {
                    field: 'createdAt', title: '创建时间', templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                }
            ], {}, '#topToolBar');
            layui.keywordConfig.tableEvent();
        },
        /**
        * 导入关键词
        * */
        import: function (data) {
            if (data.field.customerId == '') {
                layui.common.alertAutoClose("请选择所属客户");
                return;
            }
            if (data.field.file == '') {
                layui.common.alertAutoClose("请选择关键词文件");
                return;
            }
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            var formData = new FormData(layui.$('#uploadForm')[0]);
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/keyword-config/import',
                data: formData
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose(res.result);
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 导出关键词
         * @param {any} data
         */
        export: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/keyword-config/export',
                data: JSON.stringify({
                    customerId: data.field.customerId,
                    domainId: data.field.domainId,
                    pageId: data.field.pageId
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.alertAutoClose("导出成功");
                    parent.location.href = res.result;
                    parent.layui.common.closeType('iframe');
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        clear: function () {
            var customer = layui.$('#customer-view');
            var customerId = customer.val();
            if (customerId == '') {
                layui.common.alertAutoClose("请选择客户");
                return false;
            }
            var confirmIndex = layui.layer.confirm('确定清空【' + customer.find('option:selected').text() + '】关键词吗？', {
                icon: 3,
                title: '提示',
                btn: ['确定', '取消']
            }, function () {
                layui.request({
                    requestBase: 'form',
                    method: 'post',
                    url: '/admin/basis/keyword-config/clear?customerId=' + customerId,
                }).then(function (res) {
                    if (res.isSuccess) {
                        layui.tableRequest.reload("keyword-config-list");
                        layui.common.alertAutoClose("关键词清除成功");
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            }, function () {
                layui.layer.close(confirmIndex);
            });
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            //监听查询按钮
            layui.form.on('submit(keyword-config-search)', function (data) {
                var field = data.field;
                layui.tableRequest.reload("keyword-config-list", {
                    where: field
                });
            });
            layui.table.on('toolbar(list)', function (obj) {
                if (obj.event == 'import') {
                    layui.common.openIframe('导入搜索关键词', 600, 350, 'import.html');
                }
                else if (obj.event == 'export') {
                    layui.common.openIframe('导出搜索关键词', 650, 350, 'export.html');
                }
                else if (obj.event === 'clear') {
                    layui.keywordConfig.clear();
                }
            });
        },
    }

    exports('keywordConfig', func);
});