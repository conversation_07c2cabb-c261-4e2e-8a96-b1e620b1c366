﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>后台管理系统</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Cache" content="no-cache">
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/lib/font-awesome/font-awesome.min.css" rel="stylesheet" />
    <link href="/lib/alicallcenter/ui.min.css" rel="stylesheet" />
    <link href="css/index.css" rel="stylesheet" />
    <style>
        .layui-this { background-color: #f6f6f6 }
        .seats { color: #808080; }
        .seats i { font-style: normal; }
        .call-mask { background: #000000; opacity: 0.5; width: 100%; height: 100%; z-index: 9998; position: fixed; left: 0; top: 0; display: none; }
        .call-dialog { width: 300px; height: 400px; background: url(images/call_bg.png) repeat-x; background-size: auto 100%; z-index: 9999; position: fixed; top: 50%; left: 50%; border-radius: 10px; transform: translate(-50%, -50%); display: none; }
        .call-dialog .call-text { color: #ccc; box-sizing: border-box; padding: 10px; }
        .call-dialog .mobile-info { width: 100%; text-align: center; color: #fff; font-size: 25px; margin-top: 50px; }
        .call-dialog .name-info { color: #ccc; text-align: center; margin-top: 5px; }
        .call-dialog .time-info { width: 100%; text-align: center; color: #ccc; font-size: 12px; margin-top: 30px; display: none; }
        .call-dialog .btn-info { text-align: center; margin-top: 100px; position: absolute; bottom: 40px; width: 100%; }
        .call-dialog .btn-info a img { width: 65px; }
        .call-dialog .btn-info a:first-child { margin-right: 60px; display: none; }
    </style>
</head>
<body>
    <!--初始化lodding-->
    <div class="init-lodding">
        <div style="width:100%;height:100%;background:#fff;z-index:9999;position:fixed;top:0;left:0;text-align:center;vertical-align:central;display:table;">
            <p style="display: table-cell; vertical-align: middle;"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 35px; color: #50b1fb"></i><span style="position: relative; display: inline-block; top: -7px; left: 10px; font-size: 16px; color: #50b1fb ">正在加载中...</span></p>
        </div>
    </div>
    <div class="layui-layout-body">
        <div id="LAY_app">
            <div class="layui-layout layui-layout-admin">
                <div class="layui-header">
                    <!-- 头部区域 -->
                    <ul class="layui-nav layui-layout-left" id="nav-menu">
                        <li class="layui-nav-item layadmin-flexible" lay-unselect>
                            <a href="javascript:;" class="padding" layadmin-event="flexible" title="侧边伸缩">
                                <i class="layui-icon layui-icon-shrink-right" id="LAY_app_flexible"></i>
                            </a>
                        </li>

                        <li class="layui-nav-item" lay-unselect>
                            <a href="javascript:;" class="padding" layadmin-event="refresh" title="刷新">
                                <i class="layui-icon layui-icon-refresh-3"></i>
                            </a>
                        </li>
                    </ul>
                    <!--<ul class="layui-nav ">
                        <li class="layui-nav-item">
                            <a href="javascript:;">切换应用</a>
                            <dl class="layui-nav-child" id="LAY-system-top-menu">
                            </dl>
                        </li>
                        <li class="layui-nav-item layui-this">
                            <a href="javascript:;">日志中心</a>
                        </li>
                    </ul>-->
                    <ul class="layui-nav layui-layout-right" lay-filter="layadmin-layout-right">
                        <li class="layui-nav-item" style="margin-right: 20px;display:none;" id="seat-item">
                            <span class="seats" href="javascript:;">坐席状态：<i id="seats-status">-</i></span>
                        </li>
                        <li class="layui-nav-item layui-hide-xs" lay-unselect>
                            <a href="javascript:;" lay-href="notice.html" lay-text="站内信" title="站内信">
                                <i class="layui-icon layui-icon-notice" style="font-size:18px;"></i>
                            </a>
                        </li>
                        <li class="layui-nav-item layui-hide-xs" lay-unselect>
                            <a href="javascript:;" layadmin-event="fullscreen">
                                <i class="layui-icon layui-icon-screen-full"></i>
                            </a>
                        </li>
                        <li class="layui-nav-item" lay-unselect style="margin-right:20px">
                            <a href="javascript:;">
                                <cite id="admin-username"></cite>
                            </a>
                            <dl class="layui-nav-child">
                                <dd><a lay-href="update-password.html" style="text-align: center;cursor:pointer">修改密码</a></dd>
                                <hr>
                                <dd class="loginout" id="login-out" style="text-align: center;cursor:pointer"><a>退出</a></dd>
                            </dl>
                        </li>
                    </ul>
                </div>

                <!-- 侧边菜单 -->
                <div class="layui-side layui-side-menu">
                    <div class="layui-side-scroll">
                        <div class="layui-logo" id="header-logo" lay-href="main.html">
                            <img id="siteLogo" style="border-radius:6px;" />
                            <span id="siteName"></span>
                        </div>
                        <ul class="layui-nav layui-nav-tree layui-nav-left" lay-shrink="all" id="LAY-system-side-menu" lay-filter="layadmin-system-side-menu">
                        </ul>
                    </div>
                </div>

                <!-- 页面标签 -->
                <div class="layadmin-pagetabs" id="LAY_app_tabs">
                    <div class="layui-icon layadmin-tabs-control layui-icon-prev" layadmin-event="leftPage"></div>
                    <div class="layui-icon layadmin-tabs-control layui-icon-next" layadmin-event="rightPage"></div>
                    <div class="layui-icon layadmin-tabs-control layui-icon-down">
                        <ul class="layui-nav layadmin-tabs-select" lay-filter="layadmin-pagetabs-nav">
                            <li class="layui-nav-item" lay-unselect>
                                <a href="javascript:;"></a>
                                <dl class="layui-nav-child layui-anim-fadein">
                                    <dd layadmin-event="closeThisTabs"><a href="javascript:;">关闭当前标签页</a></dd>
                                    <dd layadmin-event="closeOtherTabs"><a href="javascript:;">关闭其它标签页</a></dd>
                                    <dd layadmin-event="closeAllTabs"><a href="javascript:;">关闭全部标签页</a></dd>
                                </dl>
                            </li>
                        </ul>
                    </div>
                    <div class="layui-tab" lay-unauto lay-allowClose="true" lay-filter="layadmin-layout-tabs">
                        <ul class="layui-tab-title" id="LAY_app_tabsheader">
                            <li lay-id="main.html" lay-attr="main.html" class="layui-this"><i class="layui-icon layui-icon-home"></i></li>
                        </ul>
                    </div>
                </div>

                <!-- 主体内容 -->
                <div class="layui-body" id="LAY_app_body">
                    <div id="LAY_app_body_canvas"></div>
                    <div class="layadmin-tabsbody-item layui-show">
                        <iframe src="main.html" frameborder="0" class="layadmin-iframe"></iframe>
                    </div>
                </div>

                <!-- 辅助元素，一般用于移动设备下遮罩 -->
                <div class="layadmin-body-shade" layadmin-event="shade"></div>
            </div>
        </div>
    </div>
    <!--消息按钮跳转-->
    <a id="notice_link_btn" style="display:none;" href="javascript:;">站内信</a>
    <!--呼叫弹框-->
    <div class="call-mask"></div>
    <div class="call-dialog">
        <input id="orderId" value="" type="hidden" />
        <p class="call-text">正在呼叫...</p>
        <p class="mobile-info">-</p>
        <p class="name-info">-</p>
        <p class="time-info">00:00:00</p>
        <p class="btn-info">
            <a href="javascript:;" class="answer"><img src="images/answer.png" /></a>
            <a href="javascript:;" class="hangup"><img src="images/hangup.png" /></a>
        </p>
    </div>
    <!--阿里云外呼-->
    <div id="workbench-container" style="position:fixed;right:0;bottom:0;z-index:1000"></div>
    <!--顶部切换应用模板-->
    <script id="top-menu-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <dd><a href="index.html?appId={{item.id}}">{{item.name}}</a></dd>
        {{#  }); }}
    </script>
    <!-- 左侧菜单模板 -->
    <script id="left-menu-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <li data-name="{{item.name}}" class="layui-nav-item">
            <a href="{{# if(item.url==''){}}javascript:;{{# }else{}}{{item.url}}{{# }}}javascript:;" lay-tips="{{item.name}}" lay-direction="{{item.id}}">
                {{item.icon}}
                <cite>{{item.name}}</cite>
                <span class="layui-nav-more"></span>
            </a>
            <dl class="layui-nav-child layui-nav-child-s">
                {{#  layui.each(item.childs, function(indexChild, itemChild){ }}
                <dd data-name="{{itemChild.name}}">
                    <a lay-href="{{itemChild.url}}">
                        {{itemChild.icon}}
                        {{itemChild.name}}
                    </a>
                </dd>
                {{#  }); }}
            </dl>
        </li>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="js/global.js?v=2.9"></script>
    <script src="/lib/alicallcenter/ui.min.js"></script>
    <script>
        var time = 0;
        var timeInterval;
        var noticeDialog = null;
        var currentNotification = null;
        layui.use(['index', 'formAuth'], function () {
            layui.formAuth.initPage();
            layui.index.getUserInfo();
            layui.$('#login-out').click(function () {
                layui.index.loginOut();
            });

            //接听
            layui.$('.answer').click(function () {
                layui.index.answer();
            })
            //挂机
            layui.$('.hangup').click(function () {
                layui.index.hangup();
            });

        });
    </script>

    <script>
        (function () {
            function canvasWM(content) {
                var canvas = document.createElement('canvas');
                const container = document.getElementById('LAY_app_body_canvas');
                const fontSize = 14; // 字体大小
                const degree = -30; // 旋转角度，顺时针为正，逆时针为负
                const areaWidth = window.innerWidth; // 水印区域的宽度
                const areaHeight = window.innerHeight; // 水印区域的高度
                const nameStr = content;
                const timeStr = (new Date()).toLocaleString();
                const lineSpace = 150; // 每行文字的垂直间隔（包含文字高度），px
                const colSpace = 3; // 显示文字的区域的宽度与文字宽度的比例
                const zIndex = 1000;

                canvas.setAttribute('width', areaWidth)
                canvas.setAttribute('height', areaHeight)

                const ctx = canvas.getContext('2d');

                ctx.rotate(degree * Math.PI / 180);
                ctx.font = `${fontSize}px Microsoft Yahei`; // 设置字体及大小
                const textWidth = ctx.measureText(nameStr).width  // 获取文字宽度
                const textUnitWidth = textWidth * colSpace + 70; // 一个文字单元的宽度（文字宽度+左右空白）

                ctx.fillStyle = 'rgba(0, 0, 0, 0.2)'; // 字体的颜色
                ctx.textAlign = "center"; // 文本的对齐方式
                ctx.textBaseline = "middle"; // 文本的基线属性
                ctx.globalAlpha = 0.5; // 透明度

                ctx.translate(100, 100); // 给画面一个基础偏移量，也可以省略该值

                let xNum = Math.ceil(areaWidth / textUnitWidth) //不旋转时横向可以展示的最多文字单元数
                let yNum = Math.ceil(areaHeight / lineSpace)//（不旋转时）纵向可以展示的最多文字单元数

                // 当文字旋转时，有一部分文字会被转走，需要扩展写字的范围，使用正弦函数确定扩展的最大范围
                let xStart = 0, yStart = 0, sin = Math.sin(Math.abs(degree) * Math.PI / 180);
                if (degree > 0) {
                    // 顺时针旋转时，右侧和上侧可能会有空余
                    xNum += Math.ceil(sin * areaHeight / textUnitWidth);
                    yStart = Math.ceil(sin * areaWidth / lineSpace) + -1;
                } else {
                    // 逆时针旋转时，左侧和下侧可能会有空余
                    xStart = Math.ceil(sin * areaHeight / textUnitWidth) * -1;
                    yNum += Math.ceil(sin * areaWidth / lineSpace);
                }

                for (let x = xStart; x < xNum; x++) {
                    for (let y = yStart; y < yNum; y++) {
                        const offsetY = y % 2 == 0 ? 0 : textUnitWidth / 2; // 隔行横向偏移一半的距离
                        const startX = textUnitWidth * x + offsetY; // 文字的X轴起始点
                        const startY = lineSpace * y; // 文字的Y轴起始点
                        ctx.fillText(nameStr, startX, startY);
                        ctx.fillText(timeStr, startX, startY + fontSize * 1.5);
                    }
                }

                var base64Url = canvas.toDataURL();
                const watermarkDiv = document.createElement("div");
                watermarkDiv.setAttribute('style', 'position:fixed;top:0;left:0;width:100%;height:100%;z-index:' + zIndex + ';pointer-events:none;background-repeat:repeat;background-image:url(\'' + base64Url + '\')');

                container.style.position = 'relative';
                container.insertBefore(watermarkDiv, container.firstChild);

            }
            window.canvasWM = canvasWM;
        })();
    </script>
</body>
</html>