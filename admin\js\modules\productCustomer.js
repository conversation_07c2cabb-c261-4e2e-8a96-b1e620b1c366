layui.define(['laytpl', 'form', 'laydate', 'request', 'tableRequest', 'common', 'dropdown'], function (exports) {
    var func = {
        /**
        * 通过id获取单个客户信息
        * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                setTimeout(function () { history.back(-1) }, 2000);
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/product/customer/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('#id').text(res.result.id);
                    layui.$('#name').text(res.result.name);
                    //layui.$('#mobile').text(res.result.mobile);
                    //layui.$('#certNo').text(res.result.certNo);
                    if (res.result.saleUserNames != null) {
                        var saleUserNames = '';
                        for (var i = 0; i < res.result.saleUserNames.length; i++) {
                            saleUserNames += '<button type="button" class="layui-btn layui-btn-primary layui-btn-xs">' + res.result.saleUserNames[i] + '</button>';
                        }
                        layui.$('#saleUserNames').html(saleUserNames);
                    }
                    layui.$('#sex').text(res.result.sex);
                    layui.$('#nickname').text(res.result.nickname);
                    layui.$('#username').text(res.result.username);
                    layui.$('#status').text(res.result.status);
                    layui.$('#stockAge').text(res.result.stockAge);
                    layui.$('#investmentVarieties').text(res.result.investmentVarieties.toString('，'));
                    layui.$('#weChatAccount').text(res.result.weChatAccount);
                    layui.$('#weChatNickName').text(res.result.weChatNickName);
                    if (res.result.tags != null) {
                        var tags = '';
                        for (var i = 0; i < res.result.tags.length; i++) {
                            tags += '<button type="button" class="layui-btn layui-btn-primary layui-btn-xs">' + res.result.tags[i] + '</button>';
                        }
                        layui.$('#tags').html(tags);
                    }
                    layui.$('#isRealname').text(res.result.isRealname ? '是' : '否');

                    layui.$('#intention').text(res.result.intention);
                    layui.$('#riskLevel').text(res.result.riskLevel);
                    layui.$('#from').text(res.result.from);
                    layui.$('#description').text(res.result.description);

                    layui.$('#certValidity').text(res.result.certValidity);
                    layui.$('#address').text(res.result.address);
                    layui.$('#profession').text(res.result.profession);
                    layui.$('#education').text(res.result.education);
                    layui.$('#badIntegrityRecord').text(res.result.badIntegrityRecord);
                    layui.$('#company').text(res.result.company);
                    layui.$('#workplace').text(res.result.workplace);

                    if (res.result.role == 2) {
                        var companyInfo = "企业认证";
                        if (res.result.managerType == 1) {
                            companyInfo += '(法人)';
                        }
                        if (res.result.managerType == 2) {
                            companyInfo += '(代理人)';
                        }
                        if (res.result.companyName != null && res.result.companyName != '') {
                            companyInfo += '，企业名称：' + res.result.companyName;
                        }
                        if (res.result.organization != null && res.result.organization != '') {
                            companyInfo += '，统信码：' + res.result.organization;
                        }
                        if (res.result.legalName != null && res.result.legalName != '') {
                            companyInfo += '，法人：' + res.result.legalName;
                        }
                        layui.$('#authType').text(companyInfo);
                    }
                    else {
                        layui.$('#authType').text('个人认证');
                    }
                    layui.$('.read-customer-btn').bind('click', function () {
                        var type = layui.$(this).data('type');
                        layui.productCustomer.readMobile(res.result.id, type);
                    })
                    //订单列表
                    layui.productCustomer.queryOrder(res.result.id);
                    if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.call.read')) {
                        //通话记录
                        layui.productCustomer.queryCallRecord(res.result.id);
                        //通话记录（阿里云）
                        layui.productCustomer.queryAliCallRecord(res.result.id);

                        //沟通记录
                        layui.productCustomer.queryFollowUpRecord(res.result.id);
                        layui.$('.call-info').show();
                        layui.$('.create-followup').click(function () {
                            layui.common.openIframe('新增沟通记录', 400, 220, 'create-followup.html?customerId=' + res.result.id);
                        });
                    }
                }
                else {
                    layui.common.alertAutoClose(res.message);
                }
            })
        },
        /**
         * 通过id获取单个客户信息
         * */
        getForUpdate: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.alertAutoClose("参数有误");
                setTimeout(function () { history.back(-1) }, 2000);
                return;
            }
            layui.$('#orderId').val(id);
            layui.request({
                method: 'post',
                url: '/admin/product/customer/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    //layui.$('input[name=certNo]').val(res.result.certNo);
                    layui.$("input[name='sexInfo'][value=" + res.result.sex + "]").prop("checked", true);
                    layui.$('input[name=weChatAccount]').val(res.result.weChatAccount);
                    layui.$('input[name=weChatNickName]').val(res.result.weChatNickName);
                    layui.$('#intention').val(res.result.intention);
                    layui.$('#riskLevel').val(res.result.riskLevel);
                    layui.$('input[name=certValidity]').val(res.result.certValidity);
                    layui.$('input[name=address]').val(res.result.address);
                    layui.$('#profession').val(res.result.profession);
                    layui.$('#education').val(res.result.education);
                    layui.$('#badIntegrityRecord').val(res.result.badIntegrityRecord);
                    layui.$('input[name=company]').val(res.result.company);
                    layui.$('input[name=workplace]').val(res.result.workplace);
                    //if (res.result.spareMobileList != null && res.result.spareMobileList != '') {
                    //    layui.$('input[name=mobiles]').val(res.result.spareMobileList.toString(','));
                    //}
                    layui.form.render('select');
                    layui.form.render('radio');
                }
                else {
                    parent.layui.common.closeType('iframe');
                    parent.layui.common.alertAutoClose(res.message);
                }
            })
        },
        /**
         * 获取实名认证文件图片
         * */
        getAuthFile: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
            }
            layui.request({
                method: 'post',
                url: '/admin/product/customer/auth-file/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    if (res.result.urls.length == 0) {
                        parent.layui.common.closeType('iframe');
                        parent.layui.common.alertAutoClose("暂无证照信息");
                    }
                    var imgs = '';
                    for (var i = 0; i < res.result.urls.length; i++) {
                        imgs += '<a href="' + res.result.urls[i] + '" target="_blank"><img src="' + res.result.urls[i] + '" /></a>'
                    }
                    layui.$('#preview').html(imgs);
                }
                else {
                    parent.layui.common.closeType('iframe');
                    parent.layui.common.alertAutoClose(res.message);
                }
            })
        },
        /**
        * 获取客户列表
        * */
        query: function () {
            layui.tableRequest.request('resource', true, 'customer-list', '/admin/product/customer/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '姓名', width: 80 },
                //{ field: 'mobile', title: '手机号', width: 120 },
                //{ field: 'certNo', title: '身份证号', width: 170 },
                {
                    title: '所属用户', width: 100, templet: function (e) {
                        var saleUserName = '';
                        if (e.saleUserNames != null && e.saleUserNames.length > 0) {
                            for (var i = 0; i < e.saleUserNames.length; i++) {
                                if (saleUserName != '')
                                    saleUserName += '，';
                                saleUserName += e.saleUserNames[i];
                            }
                        }
                        return saleUserName;
                    }
                },
                {
                    field: 'isRealname', title: '是否已实名', templet: function (e) {
                        return e.isRealname ? '是' : '否'
                    }
                },
                { field: 'riskLevel', title: '风险等级' },
                { field: 'intention', title: '意向分类' },
                { field: 'statusDesc', title: '状态' },
                { field: 'from', title: '来源' },
                {
                    fixed: 'right', title: '最后更新时间', width: 160, templet: function (e) {
                        return layui.common.timeFormat(e.updatedAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 100, align: 'left', toolbar: '#customer-bar' }
            ], { companyId: layui.setter.productCompanyId }, '#topToolBar', '', null, function (res, curr, count) {
                layui.dropdown.suite();
            });
        },
        /**
         * 获取订单列表
         * @param {any} customerId
         */
        queryOrder: function (customerId) {
            layui.tableRequest.request('resource', false, 'order-list', '/admin/product/order/by-customerid/query?customerId=' + customerId, 'application/json', [
                { field: 'orderNo', title: '订单编号', width: 210 },
                { field: 'saleUserName', title: '所属用户', width: 100 },
                { field: 'productName', title: '购买产品', minWidth: 190 },
                {
                    field: 'payTime', title: '支付状态', width: 110, sort: true, templet: function (e) {
                        var res = e.payStatusDesc;
                        if (e.payStatusDesc == '支付成功' && e.refundStatus != '未退款') {
                            res = e.refundStatus;
                            if (e.refundTime != null) {
                                res += '-' + layui.common.timeFormat(e.refundTime);
                            }
                            else {
                                if (e.payTime != null) {
                                    res += '-' + layui.common.timeFormat(e.payTime);
                                }
                            }
                        }
                        if (e.payStatusDesc == "驳回" && e.financialRemark != null && e.financialRemark != '') {
                            res += '（' + e.financialRemark + '）'
                        }
                        return res;
                    }
                },
                { field: 'signFlowStatusDesc', title: '签署状态', width: 100 },
                {
                    title: '分配用户', width: 100, templet: function (e) {
                        return e.saleUserName != "" ? e.saleUserName : '-';
                    }
                },
                { field: 'payableAmount', title: '应付', width: 100 },
                { field: 'actuallyPaidAmount', title: '实付', width: 100 },
                {
                    field: 'payTypeDesc', title: '支付方式', width: 180, templet: function (e) {
                        var payTypeDesc = e.payTypeDesc;
                        if (e.transferVoucher != null && e.transferVoucher != '') {
                            payTypeDesc += ' <a style="color:#3c92d5" onclick="layui.productOrder.openPreview(\'' + e.transferVoucher + '\')" href="javascript:;">[转账凭证]</a>'
                        }
                        return payTypeDesc;
                    }
                },
                {
                    field: 'refundTime', title: '退款状态', width: 110, sort: true, templet: function (e) {
                        var res = e.refundStatusDesc;
                        if (e.refundTime != null) {
                            res += '-' + layui.common.timeFormat(e.refundTime)
                        }
                        return res;
                    }
                },
                { field: 'financialAuditStatusDesc', title: '财务审核', width: 120 },
                { field: 'auditStatusDesc', title: '合规审核', width: 120 },
                { field: 'callStatusDesc', title: '拨打状态', width: 120 },
                { field: 'followUpStatusDesc', title: '回访状态', width: 120 },
                { field: 'intentionName', title: '客户意向', width: 100 },
                {
                    field: 'serviceTime', title: '服务期限', width: 200, sort: true, templet: function (e) {
                        return layui.common.timeFormat(e.serviceStartTime).split(' ')[0] + ' 至 ' + layui.common.timeFormat(e.serviceEndTime).split(' ')[0];
                    }
                },
                {
                    field: 'surplusValue', title: '剩余价值', width: 100, templet: function (e) {
                        return e.surplusValue == 0 ? '-' : e.surplusValue;
                    }
                },
                {
                    fixed: 'right', field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 150, align: 'left', toolbar: '#order-bar' }
            ], {}, null, 'auto');
            layui.table.on('tool(order-list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    location.href = '../product-order/detail.html?id=' + data.id;
                }
                else if (obj.event === 'followup') {
                    layui.$('#orderId').val(data.id);
                    if (data.followUpStatus > 0) {
                        layui.$('#followUpStatus').val(data.followUpStatus);
                    }
                    else {
                        layui.$('#followUpStatus').val('');
                    }
                    layui.form.render('select');
                    layui.common.openPage('编辑回访状态', 370, 320, '#update-followup-dialog');
                }
            });
        },
        /**
         * 获取通话记录列表
         * @param {any} customerId
         */
        queryCallRecord: function (customerId) {
            layui.tableRequest.request('resource', false, 'call-list', '/admin/callcenter/callrecord/by-customerid/query?customerId=' + customerId, 'application/json', [
                { field: 'userName', title: '通话人' },
                {
                    field: 'beginTime', title: '通话开始时间', templet: function (e) {
                        return layui.common.timeFormat(e.beginTime);
                    }
                },
                {
                    field: 'finishedTime', title: '通话结束时间', templet: function (e) {
                        return e.finishedTime != null ? layui.common.timeFormat(e.finishedTime) : '-';
                    }
                },
                {
                    field: 'establishedLength', title: '接通时长', templet: function (e) {
                        if (e.establishedLength === 0) return '-';
                        var hours = Math.floor((e.establishedLength % 86400000) / 3600000); //时
                        var minutes = Math.floor((e.establishedLength % 3600000) / 60000); //分
                        var seconds = Math.floor((e.establishedLength % 60000) / 1000); //秒

                        var text = '';
                        if (hours > 0) {
                            text += hours + '小时';
                        }
                        if (minutes > 0) {
                            text += minutes + '分';
                        }
                        if (seconds > 0) {
                            text += seconds + '秒';
                        }
                        return text;
                    }
                },
                {
                    field: 'userCallType', title: '呼叫类型', templet: function (e) {
                        if (e.userCallType == 11) {
                            return '来电';
                        }
                        else if (e.userCallType == 12) {
                            return '去电';
                        }
                        else if (e.userCallType == 13) {
                            return '绑定分机对呼去电';
                        }
                        else if (e.userCallType == 14) {
                            return '监听';
                        }
                        else if (e.userCallType == 15) {
                            return '强插';
                        }
                        else if (e.userCallType == 16) {
                            return '会议';
                        }
                        else if (e.userCallType == 17) {
                            return '软转接';
                        }
                        else if (e.userCallType == 20) {
                            return '硬转接C';
                        }
                        else if (e.userCallType == 21) {
                            return '硬转接B';
                        }
                        else if (e.userCallType == 30) {
                            return '来电抢接者';
                        }
                        else if (e.userCallType == 31) {
                            return '来电被抢接';
                        }
                        else if (e.userCallType == 32) {
                            return '呼叫转移过来';
                        }
                        else {
                            return '-';
                        }
                    }
                },
                {
                    field: 'finishedReason', title: '结束原因', templet: function (e) {
                        if (e.finishedReason == 2) {
                            return '请求超时';
                        }
                        else if (e.finishedReason == 3) {
                            return '发送命令超时';
                        }
                        else if (e.finishedReason == 4) {
                            return '音频长时间静音,无语音包';
                        }
                        else if (e.finishedReason == 5) {
                            return '回铃超时';
                        }
                        else if (e.finishedReason == 6) {
                            return '未回铃';
                        }
                        else if (e.finishedReason == 7) {
                            return '通话超时';
                        }
                        else if (e.finishedReason == 16) {
                            return '呼叫失败';
                        }
                        else if (e.finishedReason == 17) {
                            return '呼叫拒接';
                        }
                        else if (e.finishedReason == 18) {
                            return '本地挂机';
                        }
                        else if (e.finishedReason == 19) {
                            return '本地取消';
                        }
                        else if (e.finishedReason == 20) {
                            return '对方挂机';
                        }
                        else if (e.finishedReason == 21) {
                            return '抢接服务器挂机';
                        }
                        else {
                            return '-';
                        }
                    }
                },
                { fixed: 'right', title: '操作', width: 120, align: 'left', toolbar: '#call-record-bar' }
            ], {}, null, 'auto');

            layui.table.on('tool(call-list)', function (obj) {
                var data = obj.data;
                var fileUrl = '//dn-sound-record.oss-cn-shanghai.aliyuncs.com/' + data.recordFileUrl;
                if (obj.event === 'download') {
                    if (data.establishedLength == 0) {
                        layui.common.alertAutoClose("当前无可下载的录音文件或录音暂未同步");
                        return;
                    }
                    window.open(fileUrl);
                }
                else if (obj.event == 'player') {
                    if (data.establishedLength == 0) {
                        layui.common.alertAutoClose("当前无可播放的录音文件或录音暂未同步");
                        return;
                    }
                    document.getElementById('audio_dom').src = fileUrl;
                    layui.common.openPage('播放录音', 500, 100, '#audio-dialog', function () {
                        document.getElementById('audio_dom').src = '';
                    });
                }
            });
        },
        /**
         * 获取通话录音列表（阿里云）
         * @param {any} customerId
         */
        queryAliCallRecord: function (customerId) {
            layui.tableRequest.request('resource', true, 'ali-call-list', '/admin/callcenter/ali/callrecord/query', 'application/json', [
                { field: 'userName', title: '通话人' },
                { field: 'contactType', title: '通话类型' },
                { field: 'startTime', title: '开始时间' },
                { field: 'releaseTime', title: '结束时间' },
                { field: 'contactDisposition', title: '结束原因' },
                { field: 'releaseInitiator', title: '挂断方' },
                {
                    field: 'callDuration', title: '通话时长', templet: function (e) {
                        if (e.callDuration == '') return '-';

                        var hours = Math.floor(parseInt(e.callDuration) / 3600);
                        var minutes = Math.floor((parseInt(e.callDuration) - (hours * 3600)) / 60);
                        var seconds = parseInt(e.callDuration) % 60;

                        return [hours, minutes, seconds]
                            .map(num => num < 10 ? '0' + num : num.toString())
                            .filter(num => num)
                            .join(':');
                    }
                },
                { fixed: 'right', title: '操作', width: 120, align: 'left', toolbar: '#ali-call-record-bar' }
            ], { customerId: customerId }, null, 'auto');

            layui.table.on('tool(ali-call-list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'download') {
                    if (!data.recordingReady) {
                        layui.common.alertAutoClose("当前无可下载的录音文件或录音暂未同步");
                        return;
                    }
                    layui.productCustomer.getAliCallRecordFileUrl(data.id, 1);
                }
                else if (obj.event == 'player') {
                    if (!data.recordingReady) {
                        layui.common.alertAutoClose("当前无可播放的录音文件或录音暂未同步");
                        return;
                    }
                    layui.productCustomer.getAliCallRecordFileUrl(data.id, 2);
                }
            });
        },
        /**
         * 获取通话记录录音文件地址
         * @param {any} id
         * @param {any} type
         */
        getAliCallRecordFileUrl: function (id, type) {
            layui.request({
                method: 'post',
                url: '/admin/callcenter/ali/callrecord/file/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    if (type == 1) {
                        window.open(res.result.fileUrl);
                    }
                    else {
                        document.getElementById('audio_dom').src = res.result.fileUrl;
                        layui.common.openPage('播放录音', 500, 100, '#audio-dialog', function () {
                            document.getElementById('audio_dom').src = '';
                        });
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
       * 获取沟通记录列表
       * @param {any} customerId
       */
        queryFollowUpRecord: function (customerId) {
            layui.tableRequest.request('resource', false, 'followup-list', '/admin/callcenter/followuprecord/query', 'application/json', [
                { field: 'content', title: '沟通内容' },
                { field: 'uesrName', title: '用户', width: 100 },
                {
                    field: 'createdAt', title: '沟通时间', width: 160, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                }

            ], { customerId: customerId }, null, 'auto');
        },
        /**
        * 创建沟通记录
        * @param {any} data
        */
        createFollowUpRecord: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/callcenter/followuprecord/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("followup-list");
                    parent.layui.common.alertAutoClose("新增记录成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 修改订单回访状态
         * @param {any} id
         * @param {any} status
         */
        updateFollowUpStatus: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/order/followup-status/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                if (res.isSuccess) {
                    layui.common.closeType('page');
                    layui.tableRequest.reload("order-list");
                    layui.common.alertAutoClose("编辑回访状态成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑客户信息
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/customer/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 修改用户状态
         * @param {any} id
         * @param {any} status
         */
        updateStatus: function (id, status) {
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/product/customer/status/update',
                data: JSON.stringify({ id: id, status: status == 1 ? 2 : 1 }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("customer-list");
                    layui.common.alertAutoClose("修改成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 修改用户登录密码
        * @param {any} id
        * @param {any} password
        */
        updatePassword: function (id, password) {
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/product/customer/password/update',
                data: JSON.stringify({ id: id, password: password }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("customer-list");
                    layui.common.alertAutoClose("修改成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
          * 删除客户
          * @param {any} data
          */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/product/customer/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("customer-list");
                    layui.common.alertAutoClose("客户删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 呼叫客户
        * */
        callCustomer: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
            }
            layui.request({
                method: 'post',
                url: '/admin/product/customer/ali/call?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    if (res.result != null && res.result.length > 0) {
                        layui.$('.file-item-z').find('.z-mobile').text(res.result[0]);
                        var getTpl = document.getElementById("b-mobile-tpl").innerHTML
                            , view = document.getElementById('b-mobile-view');
                        layui.laytpl(getTpl).render(res.result, function (html) {
                            view.innerHTML = html;
                        });
                        layui.$('.call-item-btn').click(function () {
                            layui.common.closeType('page');
                            var $this = layui.$(this);
                            //显示弹框
                            window.workbenchChild.changeUIConfig({ mainContentVisible: true });
                            //执行呼叫
                            var callId = 'c_' + id;
                            if ($this.data('type') != 1) {
                                callId = 'c_b_' + id;
                            }
                            window.workbenchChild.call({ callee: callId });
                        });
                        layui.common.openPage('选择手机号', 370, 320, '#mobile-dialog');
                    }
                    else {
                        //显示弹框
                        window.workbenchChild.changeUIConfig({ mainContentVisible: true });
                        //执行呼叫
                        window.workbenchChild.call({ callee: 'c_' + id });
                    }
                }
                else {
                    layui.common.alertAutoClose(res.message);
                }
            })
        },
        /**
        * 导出客户
        * */
        export: function (data) {
            var isRealname = layui.$('#isRealname').val();
            var from = layui.$('#from').val();
            var startTime = layui.$('#startTime').val();
            var endTime = layui.$('#endTime').val();
            var field = {
                companyId: layui.setter.productCompanyId,
                keywords: layui.$('#keywords').val(),
                isRealname: isRealname == '' ? -1 : isRealname,
                from: from == '' ? -1 : from,
                startTime: startTime,
                endTime: endTime
            };
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/customer/export',
                data: JSON.stringify(field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                if (res.isSuccess) {
                    location.href = res.result;
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
             * 查看手机号/身份证号
             * @param {any} customerId
             * @param {any} type
             */
        readMobile: function (customerId, type) {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/product/customer/mobile/read?id=' + customerId + '&type=' + type,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    if (type == 2) {
                        layui.$('#certNo').html(res.result);
                    }
                    else {
                        layui.$('#mobile').html(res.result);
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 生成说明书
         * @param {Object} data 表单数据
         */
        generatePetitionForm: function (data) {
            return layui.request({
                method: 'post',
                url: '/admin/users/petition-form/generate',
                data: JSON.stringify({
                    customerId: data.id,
                    content: data.content,
                    dateTime: data.datetime
                }),
                headers: { 'Content-Type': 'application/json' }
            });
        },
        /**
         * 绑定事件
         * */
        bindEvent: function () {
            //监听查询按钮
            layui.form.on('submit(customer-search)', function (data) {
                var field = data.field;
                if (field.keywords.length <= 1) {
                    layui.layer.msg('关键词信息过短', { icon: 2, time: 2000 });
                    return false;
                }
                document.getElementById("customer-list-body").style.visibility = "visible";
                field.companyId = layui.setter.productCompanyId;
                if (data.field.isRealname == '') {
                    data.field.isRealname = -1;
                }
                if (data.field.from == '') {
                    data.field.from = -1;
                }
                //执行重载
                layui.tableRequest.reload("customer-list", {
                    where: field
                });
            });
            //客户导出
            layui.form.on('submit(customer-export)', function (data) {
                var confirmIndex = layui.layer.confirm('确定导出客户吗？', {
                    icon: 3,
                    title: '提示',
                    btn: ['确定', '取消']
                }, function () {
                    layui.productCustomer.export(data);
                    layui.layer.close(confirmIndex);
                }, function () {
                    layui.layer.close(confirmIndex);
                });
            });
            //监听说明书表单提交
            layui.form.on('submit(formSubmit)', function (data) {
                layui.productCustomer.submitPetitionForm(data);
                return false;
            });
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    location.href = 'detail.html?id=' + data.id;
                }
                else if (obj.event === 'edit') {
                    layui.common.openIframe('编辑客户信息', 840, 560, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'read') {
                    if (layui.setter.productCompanyId != '6340ecbf4d7e5a21ed46e996') {
                        layui.common.alertAutoClose('暂不支持查看客户证照信息');
                    }
                    else {
                        layui.common.openIframe('查看客户证件照片', 540, 500, 'read.html?id=' + data.id);
                    }
                }
                else if (obj.event === 'del') {
                    if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.customer.delete')) {
                        var confirmIndex = layui.layer.confirm('确定删除该客户吗？', {
                            icon: 3,
                            title: '提示',
                            btn: ['确定', '取消']
                        }, function () {
                            layui.productCustomer.delete(data.id);
                        }, function () {
                            layui.layer.close(confirmIndex);
                        });
                    }
                    else {
                        layui.common.alertAutoClose("暂无操作权限");
                    }
                }
                else if (obj.event === 'status') {
                    var confirmIndex = layui.layer.confirm('确定' + (data.status == 1 ? '冻结' : '恢复') + '【' + data.name + '】用户账号？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.productCustomer.updateStatus(data.id, data.status);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event == 'reset') {
                    var confirmIndex = layui.layer.confirm('确定重置【' + data.name + '】用户密码？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.layer.close(confirmIndex);
                        layer.prompt({ title: '请输入新密码', formType: 1 }, function (pass, index) {
                            layer.close(index);
                            layui.productCustomer.updatePassword(data.id, pass);
                        });
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event == 'sign') {
                    layui.common.openIframe('签署说明书', 540, 400, 'sign.html?id=' + data.id);
                }
                else if (obj.event == 'sign-view') {
                    layui.request({
                        method: 'get',
                        url: '/admin/users/petition-form/get-contract-url?cid=' + data.id,
                        headers: { 'Content-Type': 'application/json' },
                    }).then(function (res) {
                        if (res.isSuccess) {
                            window.open(res.result);
                        }
                        else {
                            layui.common.alert(res.message || 获取合同链接失败, 0);
                        }
                    })
                }
            });
        }
    };
    exports('productCustomer', func);
});