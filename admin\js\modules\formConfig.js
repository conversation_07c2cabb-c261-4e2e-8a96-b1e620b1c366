﻿layui.define(['laytpl', 'form', 'laydate', 'xmSelect', 'request', 'tableRequest', 'common', 'domainConfig', 'customer'], function (exports) {
    var func = {
        /**
       * 渲染客户列表
       * @param {any} companyId
       * @param {any} selectedId
       */
        initCustomer: function (selectedId) {
            layui.customer.getAll(function (res) {
                res.result.unshift({ id: '', alias: '请选择客户' });
                var getTpl = document.getElementById("customer-tpl").innerHTML
                    , view = document.getElementById('customer-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (selectedId != undefined) {
                    view.value = selectedId;
                }
                layui.form.render('select');
            });
        },
        /**
       * 渲染域名列表
       * */
        initDomain: function () {
            layui.domainConfig.getAll(function (res) {
                res.result.unshift({ id: '', domainName: '' });
                var getTpl = document.getElementById("domain-tpl").innerHTML
                    , view = document.getElementById('domain-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('select');
            });
        },
        /**
         * 初始化域名列表
         * @param {any} selectIds
         */
        initDomainList: function (selectIds) {
            layui.domainConfig.getAll(function (res) {
                if (res.isSuccess) {
                    var data = [];
                    var selectItems = [];
                    if (res.result.length > 0) {
                        for (var i = 0; i < res.result.length; i++) {
                            var item = { name: res.result[i].domainName, value: res.result[i].domainName };
                            data.push(item);
                            if (selectIds.length > 0 && selectIds.indexOf(res.result[i].domainName) > -1) {
                                selectItems.push(item);
                            }
                        }
                    }
                    xmSel = layui.xmSelect.render({
                        el: '#domainList',
                        language: 'zn',
                        filterable: true,
                        tips: '请选择绑定的域名',
                        theme: { color: '#8799a3 ' },
                        data: data,
                        toolbar: { show: true },
                        autoRow: true,
                    });
                    if (selectItems.length > 0) {
                        xmSel.setValue(selectItems);
                    }
                }
            });
        },
        /**
         * 通过id获取资源分配规则信息
         * */
        get: function (id, eventName) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/form-config/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('#id').val(res.result.id);
                    layui.$('#title').val(res.result.title);
                    layui.$('#startTime').val(layui.common.timeFormat(res.result.startTime));
                    layui.$('#endTime').val(layui.common.timeFormat(res.result.endTime));
                    layui.formConfig.initDomainList(res.result.domainList);
                    layui.customer.getAll(function (customerRes) {
                        customerList = customerRes.result;
                        for (var i = 0; i < res.result.items.length; i++) {
                            var menuTpl = '';
                            if (eventName == 'update') {
                                menuTpl += '<div class="config-input-item" style="margin-bottom:10px;float:left">';
                                menuTpl += '<input class="configItemId" name="configItemId" value="' + res.result.items[i].id + '" type="hidden" />';
                                menuTpl += '<div class="layui-input-inline" style="width:170px">';
                                menuTpl += '<select lay-filter="customerid" ' + (eventName == 'update' ? 'disabled="disabled"' : '') + ' class="customerid" lay-verify="required">';
                                menuTpl += '<option value="">请选择客户</option>';
                                for (var j = 0; j < customerList.length; j++) {
                                    menuTpl += '<option ' + (customerList[j].id == res.result.items[i].customerId ? 'selected="true"' : '') + ' value="' + customerList[j].id + '">' + customerList[j].alias + '</option>';
                                }
                                menuTpl += '</select>';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width: 580px;margin-right:0 ">';
                                menuTpl += '<div class="layui-input-inline" style="width: 100px">';
                                menuTpl += '<input type="text" lay-verify="required|number" placeholder="分配总人数" value="' + res.result.items[i].totalCount + '" autocomplete="off" class="layui-input totalCount">';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:100px">';
                                menuTpl += '<input type="text" lay-verify="required|number" placeholder="单轮分配人数" value="' + res.result.items[i].singleCount + '" autocomplete="off" class="layui-input singleCount">';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:100px">';
                                menuTpl += '<input type="text" lay-verify="required|number" placeholder="排序" value="' + res.result.items[i].sort + '" autocomplete="off" class="layui-input sort">';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:80px">';
                                menuTpl += '<input type="text" lay-verify="required|number" placeholder="已分配总数" disabled="disabled" value="' + res.result.items[i].allocatedTotalCount + '" autocomplete="off" class="layui-input singleCount">';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:80px">';
                                menuTpl += '<input type="text" lay-verify="required|number" placeholder="当前分配数" disabled="disabled" value="' + res.result.items[i].allocatedSingleCount + '" autocomplete="off" class="layui-input singleCount">';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:50px;">';
                                menuTpl += '<button type="button" class="layui-btn layui-btn-primary remove-config">';
                                menuTpl += '<i class="layui-icon">&#xe640;</i>';
                                menuTpl += '</button>';
                                menuTpl += '</div>';
                                menuTpl += '</div>';
                                menuTpl += '</div>';
                            }
                            else {
                                menuTpl += '<div class="config-input-item" style="margin-bottom:10px;float:left">';
                                menuTpl += '<input class="configItemId" name="configItemId" value="' + res.result.items[i].id + '" type="hidden" />';
                                menuTpl += '<div class="layui-input-inline" style="width:210px">';
                                menuTpl += '<select lay-filter="customerid" ' + (eventName == 'update' ? 'disabled="disabled"' : '') + ' class="customerid" lay-verify="required">';
                                menuTpl += '<option value="">请选择客户</option>';
                                for (var j = 0; j < customerList.length; j++) {
                                    menuTpl += '<option ' + (customerList[j].id == res.result.items[i].customerId ? 'selected="true"' : '') + ' value="' + customerList[j].id + '">' + customerList[j].alias + '</option>';
                                }
                                menuTpl += '</select>';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width: 540px;margin-right:0 ">';
                                menuTpl += '<div class="layui-input-inline" style="width: 150px">';
                                menuTpl += '<input type="text" lay-verify="required|number" placeholder="分配总人数" value="' + res.result.items[i].totalCount + '" autocomplete="off" class="layui-input totalCount">';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:150px">';
                                menuTpl += '<input type="text" lay-verify="required|number" placeholder="单轮分配人数" value="' + res.result.items[i].singleCount + '" autocomplete="off" class="layui-input singleCount">';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:150px">';
                                menuTpl += '<input type="text" lay-verify="required|number" placeholder="排序" value="' + res.result.items[i].sort + '" autocomplete="off" class="layui-input sort">';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:50px;">';
                                menuTpl += '<button type="button" class="layui-btn layui-btn-primary remove-config">';
                                menuTpl += '<i class="layui-icon">&#xe640;</i>';
                                menuTpl += '</button>';
                                menuTpl += '</div>';
                                menuTpl += '</div>';
                                menuTpl += '</div>';
                            }
                            layui.$('.config-input-list').append(menuTpl);
                        }
                        layui.form.render('select');
                    });
                }
            })
        },
        /**
         * 获取资源分配规则列表
         * */
        query: function () {
            layui.tableRequest.request('form', true, 'form-config-list', '/admin/basis/form-config/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left', width: 80 },
                { field: 'title', title: '标题' },
                {
                    field: 'startTime', title: '生效时间', templet: function (e) {
                        return layui.common.timeFormat(e.startTime) + ' 至 ' + layui.common.timeFormat(e.endTime);
                    }
                },
                {
                    field: 'createdAt', title: '最后更新时间', templet: function (e) {
                        return layui.common.timeFormat(e.lastUpdateTime);
                    }
                },
                { fixed: 'right', title: '操作', align: 'left', width: 180, toolbar: '#form-config-bar' }
            ]);
            //监听表格事件
            layui.formConfig.tableEvent();
        },
        /**
        * 创建单个资源分配规则
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/form-config/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("配置创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑单个资源分配规则
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/form-config/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("配置编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除单个资源分配规则
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/form-config/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("配置删除成功");
                    layui.tableRequest.reload('form-config-list');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑资源分配规则', 950, 600, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'copy') {
                    layui.common.openIframe('创建资源分配规则', 950, 600, 'create.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该配置吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.formConfig.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        },
        /**
         * 绑定事件
         * */
        bindEvent: function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            //添加行
            layui.$(document).on("click", '.add-config', function () {
                var menuTpl = '<div class="config-input-item" style="margin-bottom:10px;float:left">';
                menuTpl += '<input class="configItemId" name="configItemId" value="0" type="hidden" />';
                menuTpl += '<div class="layui-input-inline" style="width:210px">';
                menuTpl += '<select lay-filter="customerid" class="customerid" lay-verify="required">';
                menuTpl += '<option value="">请选择客户</option>';
                for (var i = 0; i < customerList.length; i++) {
                    menuTpl += '<option value="' + customerList[i].id + '">' + customerList[i].alias + '</option>';
                }
                menuTpl += '</select>';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width: 540px;margin-right:0 ">';
                menuTpl += '<div class="layui-input-inline" style="width: 150px">';
                menuTpl += '<input type="text" lay-verify="required|number" placeholder="分配总人数" autocomplete="off" class="layui-input totalCount">';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:150px">';
                menuTpl += '<input type="text" lay-verify="required|number" placeholder="单轮分配人数" autocomplete="off" class="layui-input singleCount">';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:150px">';
                menuTpl += '<input type="text" lay-verify="required|number" placeholder="排序" autocomplete="off" class="layui-input sort">';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:50px;">';
                menuTpl += '<button type="button" class="layui-btn layui-btn-primary remove-config">';
                menuTpl += '<i class="layui-icon">&#xe640;</i>';
                menuTpl += '</button>';
                menuTpl += '</div>';
                menuTpl += '</div>';
                menuTpl += '</div>';
                layui.$('.config-input-list').append(menuTpl);
                layui.form.render('select');
            });
            //添加行
            layui.$(document).on("click", '.add-config-2', function () {
                var menuTpl = '<div class="config-input-item" style="margin-bottom:10px;float:left">';
                menuTpl += '<input class="configItemId" name="configItemId" value="" type="hidden" />';
                menuTpl += '<div class="layui-input-inline" style="width:170px">';
                menuTpl += '<select lay-filter="customerid" class="customerid" lay-verify="required">';
                menuTpl += '<option value="">请选择客户</option>';
                for (var i = 0; i < customerList.length; i++) {
                    menuTpl += '<option value="' + customerList[i].id + '">' + customerList[i].alias + '</option>';
                }
                menuTpl += '</select>';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width: 580px;margin-right:0 ">';
                menuTpl += '<div class="layui-input-inline" style="width: 100px">';
                menuTpl += '<input type="text" lay-verify="required|number" placeholder="分配总人数" autocomplete="off" class="layui-input totalCount">';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:100px">';
                menuTpl += '<input type="text" lay-verify="required|number" placeholder="单轮分配人数" autocomplete="off" class="layui-input singleCount">';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:100px">';
                menuTpl += '<input type="text" lay-verify="required|number" placeholder="排序" autocomplete="off" class="layui-input sort">';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:80px">';
                menuTpl += '<input type="text" lay-verify="required|number" placeholder="已分配总数" disabled="disabled" value="0" autocomplete="off" class="layui-input singleCount">';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:80px">';
                menuTpl += '<input type="text" lay-verify="required|number" placeholder="当前分配数" disabled="disabled" value="0" autocomplete="off" class="layui-input singleCount">';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:50px;">';
                menuTpl += '<button type="button" class="layui-btn layui-btn-primary remove-config">';
                menuTpl += '<i class="layui-icon">&#xe640;</i>';
                menuTpl += '</button>';
                menuTpl += '</div>';
                menuTpl += '</div>';
                menuTpl += '</div>';
                layui.$('.config-input-list').append(menuTpl);
                layui.form.render('select');
            });
            //删除行
            layui.$(document).on("click", '.remove-config', function () {
                layui.$(this).parent().parent().parent('.config-input-item').remove();
            });
        }
    }

    exports('formConfig', func);
});