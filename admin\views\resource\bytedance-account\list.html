﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属企微</label>
                        <div class="layui-input-inline">
                            <select name="workWxAppId" id="workwx-app-view">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="txb_keywords" name="keywords" placeholder="账户名称/账户ID" type="text" class="layui-input" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="bytedance-account-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list bytedance-account-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="bytedance-account-list" lay-filter="list"></table>
                <script type="text/html" id="bytedance-account-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>

    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'bytedanceAccount'], function () {
            layui.bytedanceAccount.initApp();
            layui.bytedanceAccount.query();
            //监听查询按钮
            layui.form.on('submit(bytedance-account-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("bytedance-account-list", {
                    where: field
                });
            });
            //打开添加弹框
            layui.$('.bytedance-account-create').click(function () {
                layui.common.openIframe('创建字节推广账户', 650, 480, 'create.html');
            });
        });
    </script>
</body>
</html>