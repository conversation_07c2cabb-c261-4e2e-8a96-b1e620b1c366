﻿layui.define(['laytpl', 'form', 'xmSelect', 'request', 'laydate', 'tableRequest', 'common', 'subsidiary', 'adminUser'], function (exports) {
    var func = {
        /**
         * 渲染产品列表
         * @param {any} checkedIds
         */
        initTypes: function (selectIds) {
            layui.request({
                method: 'post',
                url: '/admin/notification/system-notice-config/type/get-all',
            }).then(function (res) {
                if (res.isSuccess) {
                    var getTpl = document.getElementById("types-tpl").innerHTML
                        , view = document.getElementById('types-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    if (selectIds != undefined && selectIds.length > 0) {
                        for (var i = 0; i < selectIds.length; i++) {
                            layui.$("input:checkbox[name=type][value='" + selectIds[i] + "']").attr('checked', 'true');
                        }
                    }
                    layui.form.render('checkbox');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
          * 渲染所属组织下拉选框
          * */
        initSubsidiary: function (selectIds) {
            layui.subsidiary.getAll(function (res) {
                var data = [];
                var selectItems = [];
                if (res.result.length > 0) {
                    for (var i = 0; i < res.result.length; i++) {
                        var item = { name: res.result[i].name, value: res.result[i].id };
                        data.push(item);
                        if (selectIds != undefined && selectIds.length > 0 && selectIds.indexOf(res.result[i].id) > -1) {
                            selectItems.push(item);
                        }
                    }
                }
                xmSel = layui.xmSelect.render({
                    el: '#subsidiary-item',
                    language: 'zn',
                    filterable: true,
                    tips: '请选择所属组织',
                    theme: { color: '#0081ff ' },
                    data: data,
                    toolbar: { show: true },
                    autoRow: true,
                    height: '160px'
                });
                if (selectItems.length > 0) {
                    xmSel.setValue(selectItems);
                }
            });
        },
        /**
        * 渲染所属用户下拉选框
        * */
        initUser: function (selectIds) {
            layui.adminUser.queryByCompanyId(layui.setter.companyId, function (res) {
                var data = [];
                var selectItems = [];
                if (res.result.length > 0) {
                    for (var i = 0; i < res.result.length; i++) {
                        var item = { name: res.result[i].name, value: res.result[i].id };
                        data.push(item);
                        if (selectIds != undefined && selectIds.length > 0 && selectIds.indexOf(res.result[i].id) > -1) {
                            selectItems.push(item);
                        }
                    }
                }
                userXmSel = layui.xmSelect.render({
                    el: '#user-item',
                    language: 'zn',
                    filterable: true,
                    tips: '请选择所属用户',
                    theme: { color: '#0081ff ' },
                    data: data,
                    toolbar: { show: true },
                    autoRow: true,
                    height: '160px'
                });
                if (selectItems.length > 0) {
                    userXmSel.setValue(selectItems);
                }
            });
        },

        /**
         * 通过id获取单个站内信配置
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/notification/system-notice-config/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);

                    if (res.result.isAllSubsidiary) {
                        layui.$('#subsidiary-item').hide();
                        layui.$('input[name=isAllSubsidiary]').attr('checked', true);
                    }
                    layui.systemNoticeConfig.initTypes(res.result.types);
                    layui.systemNoticeConfig.initSubsidiary(res.result.subsidiaryIds);
                    layui.systemNoticeConfig.initUser(res.result.userIds);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取站内信配置列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'config-list', '/admin/notification/system-notice-config/query', 'application/json', [
                { field: 'name', title: '名称', width: '13%' },
                {
                    title: '类型', templet: function (e) {
                        return e.typeList.toString('，');
                    }
                },
                {
                    title: '数据范围', templet: function (e) {
                        if (e.isAllSubsidiary) {
                            return '全部';
                        }
                        else {
                            return e.subsidiaryList.toString('，');
                        }
                    }
                },
                {
                    title: '接收用户', templet: function (e) {
                        return e.userList.toString('，');
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 120, align: 'left', toolbar: '#config-bar' }
            ], {});
            //监听表格事件
            layui.systemNoticeConfig.tableEvent();
        },
        /**
        * 创建单个站内信配置
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/notification/system-notice-config/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("配置创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                        parent.location.reload();
                    }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑单个站内信配置
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/notification/system-notice-config/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("配置编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                        parent.location.reload();
                    }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除站内信配置
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/notification/system-notice-config/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("config-list");
                    layui.common.alertAutoClose("配置删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑站内信配置', 830, 600, 'update.html?id=' + data.id)
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该配置吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.systemNoticeConfig.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        },
    }

    exports('systemNoticeConfig', func);
});