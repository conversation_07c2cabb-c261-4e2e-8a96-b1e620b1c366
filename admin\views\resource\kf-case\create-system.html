﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 550px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
        .xiaoetong { display: none; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">方案名称</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入企微加粉方案名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">所属企微</label>
            <div class="layui-input-inline">
                <select name="workWxAppId" id="workwx-app-view" lay-filter="workwx-app" lay-verify="required">
                    <option value="">请选择所属企微</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">微信客服</label>
            <div class="layui-input-inline">
                <select name="openKfId" id="openkf-id" lay-search>
                    <option value="">请选择微信客服</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">使用场景</label>
            <div class="layui-input-inline">
                <select name="scene" id="scene" lay-filter="scene" lay-verify="required">
                    <option value="">请选择使用场景</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item tencent-ad-account" style="display:none;">
            <label class="layui-form-label">推广账户</label>
            <div class="layui-input-inline">
                <select name="tencentAdAccountId" id="tencentAdAccountId">
                    <option value="">请选择推广账户</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">承接用户</label>
            <div style="display:inline-block;width:550px;">
                <div class="layui-input-inline" id="workwx-user">
                </div>
                <div><a href="javascript:;" class="open-user-select" style="color:#0689b4;font-size:14px;">+按组织架构添加</a></div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">重复进线周期</label>
            <div class="layui-input-inline">
                <input type="text" name="cycle" lay-verify="required" placeholder="请输入重复进线周期（例:48小时内重复进线则填写48）" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">可添加员工数</label>
            <div class="layui-input-inline">
                <input type="text" name="maxAddService" lay-verify="required" placeholder="请输入可添加员工数量" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">引导文案</label>
            <div class="layui-input-inline">
                <textarea type="text" name="welcomeStartMessage" placeholder="请输入引导文案" autocomplete="off" class="layui-textarea"></textarea>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">点击菜单</label>
            <div class="layui-input-inline">
                <input type="text" name="menuNames" lay-verify="required" placeholder="引导语点击菜单，多个|分隔" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">链接地址</label>
            <div class="layui-input-inline">
                <input type="text" name="linkUrl" placeholder="菜单点击跳转地址，不填默认回复消息" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">回复引导文案</label>
            <div class="layui-input-inline">
                <input type="text" name="menuAutoReplyTips" placeholder="回复长按识别二维码引导文案，为链接时无需填写" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">无效资源比例</label>
            <div class="layui-input-inline">
                <input type="text" name="invalidRate" lay-verify="required" placeholder="请输入无效资源比例（例:扣除5%填写0.05）" value="0" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">回传配置</label>
            <div class="layui-input-inline">
                <input type="text" name="baiDuToken" placeholder="百度Token/回传配置" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item xiaoetong">
            <label class="layui-form-label">小鹅通规则ID</label>
            <div class="layui-input-inline">
                <input type="text" name="xiaoetongRuleId" placeholder="请输入小鹅通规则ID" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item xiaoetong">
            <label class="layui-form-label">小鹅通客服链接</label>
            <div class="layui-input-inline">
                <input type="text" name="xiaoetongKfUrl" placeholder="请输入小鹅通客服链接" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="kf-case-submit" value="确认添加">
            </div>
        </div>
    </div>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        var xmSelUser;
        layui.use(['kfCase'], function () {
            xmSelUser = layui.xmSelect.render({
                el: '#workwx-user',
                language: 'zn',
                filterable: true,
                tips: '请选择承接用户',
                theme: { color: '#0081ff ' },
                data: [],
                toolbar: { show: true },
                autoRow: true,
            });
            layui.kfCase.initApp();
            layui.kfCase.bindEvent('create-system');
            //神光显示小鹅通配置
            if (layui.setter.companyId == '6340ec834d7e5a21ed46e995') {
                layui.$('.xiaoetong').show();
            }
            //接收选择框消息
            top.addEventListener('message', event => {
                xmSelUser.setValue(event.data);
            });
            //打开选择框
            layui.$('.open-user-select').click(function () {
                var workWxAppId = layui.$('#workwx-app-view').val();
                if (workWxAppId == '') {
                    layui.common.alertAutoClose('请选择所属企微');
                    return;
                }
                var users = xmSelUser.getValue();
                var userIds = [];
                if (users.length > 0) {
                    for (var i = 0; i < users.length; i++) {
                        userIds.push(users[i].value);
                    }
                }
                parent.layui.common.openIframe('选择员工', 890, 630, '../workwx-app/select-user.html?workWxAppId=' + workWxAppId + '&ids=' + userIds.toString());
            });
        });
    </script>
</body>
</html>