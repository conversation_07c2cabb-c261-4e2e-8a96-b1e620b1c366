﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline {
            width: 200px;
        }
    </style>
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="巡查记录" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">直播账号</label>
                        <div class="layui-input-inline">
                            <select name="accountId" id="account-view" lay-search>
                                <option value="">请选择直播账号</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">巡查时间</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" />
                        </div>
                    </div>

                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="patrol-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                        <button class="layui-btn layuiadmin-btn-list layui-btn-primary" lay-submit
                            lay-filter="live-patrol-export">
                            <i class="layui-icon layui-icon-export layuiadmin-button-btn"></i>导出
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="patrol-list" lay-filter="list"></table>
                <script type="text/html" id="patrol-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                    {{# if(d.status!==2){}}
                        <a class="layui-btn layui-btn-xs" lay-event="editStatus">处理</a>
                        {{# } else{}}
                    {{# }}}
                </script>
            </div>
        </div>
    </div>

    <script id="account-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'laydate', 'common', 'livePatrol'], function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            layui.livePatrol.initAccount();
            layui.livePatrol.query();
            //监听查询按钮
            layui.form.on('submit(patrol-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("patrol-list", {
                    where: field
                });
            });
            //监听导出按钮
            layui.form.on('submit(live-patrol-export)', function (data) {
                var confirmIndex = layui.layer.confirm('确定导出巡查记录吗？', {
                    icon: 3,
                    title: '提示',
                    btn: ['确定', '取消']
                }, function () {
                    layui.layer.close(confirmIndex);
                    layui.livePatrol.export(data);
                }, function () {
                    layui.layer.close(confirmIndex);
                });
            });
        });
    </script>
</body>

</html>