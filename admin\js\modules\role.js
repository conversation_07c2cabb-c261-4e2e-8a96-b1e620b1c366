﻿layui.define(['laytpl', 'request', 'tableRequest', 'common', 'company'], function (exports) {
    var func = {
        /**
         * 通过企业id查询角色列表
         * @param {any} companyId
         * @param {any} callbackFun
         */
        queryByCompanyId: function (companyId, isIncludeCurRole, callbackFun) {
            layui.request({
                method: 'post',
                url: '/admin/sso/role/by-companyid/query?companyId=' + companyId + '&isIncludeCurRole=' + isIncludeCurRole,
            }).then(function (res) {
                callbackFun(res);
            });
        },
        /**
         * 渲染上级角色
         * @param {any} companyId
         * @param {any} id
         */
        initRole: function (companyId, id) {
            layui.role.queryByCompanyId(companyId, true, function (res) {
                res.result.unshift({ id: '', name: '请选择上级角色' });
                var getTpl = document.getElementById("role-tpl").innerHTML
                    , view = document.getElementById('parent-role-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('parent-role-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 渲染企业下拉选框
         * */
        initCompany: function (id) {
            layui.company.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企业' });
                var getTpl = document.getElementById("company-tpl").innerHTML
                    , view = document.getElementById('company-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('company-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过id获取角色信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/sso/role/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('#desc').val(res.result.desc);
                    layui.$('input[name=sort]').val(res.result.sort);
                    layui.role.initCompany(res.result.companyId);
                    layui.role.initRole(res.result.companyId, res.result.parentId);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取角色列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'role-list', '/admin/sso/role/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'companyName', title: '所属企业' },
                { field: 'name', title: '角色名称' },
                { field: 'desc', title: '描述' },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 170, align: 'left', toolbar: '#role-bar' }
            ]);
            //监听表格事件
            layui.role.tableEvent();
        },
        /**
        * 创建单个角色
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/sso/role/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("role-list");
                    parent.layui.common.alertAutoClose("角色创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑角色
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/sso/role/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("role-list");
                    parent.layui.common.alertAutoClose("角色编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除角色
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/sso/role/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("role-list");
                    layui.common.alertAutoClose("角色删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑角色', 650, 480, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该角色吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.role.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'permission') {
                    location.href = '../permission/settab.html?id=' + data.id + '&type=role&name=' + data.name;
                }
            });
        }
    }

    exports('role', func);
});