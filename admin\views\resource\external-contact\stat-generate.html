﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
</head>
<body>
    <div class="layui-fluid layui-form">
        <input id="workWxAppId" name="workWxAppId" type="hidden" />
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">加粉方案</label>
                        <div class="layui-input-inline">
                            <select name="kfCaseId" id="kf-case-view" lay-search>
                                <option value="">请选择加粉方案</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">数据时间</label>
                        <div class="layui-input-inline">
                            <input id="date" name="date" type="text" class="layui-input" placeholder="数据日期" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="stat-update-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="stat-list" class="layui-table" lay-filter="list">
                    <thead>
                        <tr>
                            <th style="display:none;">加粉方案id</th>
                            <th style="display:none;">加粉方案名称</th>
                            <th>投资顾问</th>
                            <th>消费</th>
                            <th>加微量</th>
                            <th>加微成本</th>
                            <th>去重加微量</th>
                            <th>去重加微成本</th>
                            <th>直播时长（小时）</th>
                            <th>新增率</th>
                            <th>助播</th>
                            <th>开口数</th>
                            <th>去重开口数</th>
                            <th>删除数</th>
                            <th>去重删除数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="stat-view">
                        <tr><td colspan="14" style="text-align:center;">请选择所属企微</td></tr>
                    </tbody>
                </table>
            </div>
        </div>
        <blockquote class="layui-elem-quote" style="background:#fff;">
            <div class="layui-inline">
                <label class="layui-form-label">是否工作日</label>

                <div class="layui-input-inline">
                    <select id="isWeekday">
                        <option value="">请选择</option>
                        <option value="true">是</option>
                        <option value="false">否</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline" style="margin-left:20px;">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="stat-save-submit" value="保存数据">
            </div>
        </blockquote>
    </div>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="kf-case-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="stat-tpl" type="text/html">
        {{# if(d.length==0){ }}
        <tr><td colspan="14" style="text-align:center;">暂无符合条件的数据</td></tr>
        {{# } else { }}
        {{#  layui.each(d, function(index, item){ }}
        <tr>
            <td style="display:none;">{{item.kfCaseId}}</td>
            <td style="display:none;">{{item.kfCaseName}}</td>
            <td>
                <input type="text" class="layui-input" style="width:230px" value="{{item.teacherName!=''?item.teacherName:item.kfCaseName}}" />
            </td>
            <td> <input type="text" class="layui-input consumption" style="width:100px" value="{{item.consumptionAmount==0?'':item.consumptionAmount}}" /></td>
            <td class="total-count">{{item.totalCount}}</td>
            <td><span class="cost">{{item.followCost}}</span></td>
            <td class="global-deduplication-count">{{item.globalDeDuplicationCount}}</td>
            <td><span class="distinct-cost">{{item.distinctFollowCost}}</span></td>
            <td><input type="text" class="layui-input" style="width:80px" value="{{item.liveDuration}}" /></td>
            <td>{{item.addRate}}</td>
            <td><input type="text" class="layui-input" style="width:80px" value="{{item.assistantName}}" /></td>
            <td>{{item.openCount}}</td>
            <td>{{item.afterDuplicationOpenCount}}</td>
            <td>{{item.deleteCount}}</td>
            <td>{{item.afterDuplicationDeleteCount}}</td>
            <td><button type="button" class="layui-btn layui-btn-primary layui-btn-xs del-rows">删除行</button></td>
        </tr>
        {{#  }); }}
        {{# } }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'laydate', 'common', 'externalContact'], function () {
            layui.laydate.render({
                elem: '#date',
                type: 'date',
                value: new Date(new Date(new Date().toLocaleDateString()).getTime())
            });
            var workWxAppId = layui.common.getUrlParam('workWxAppId') || '';
            if (workWxAppId == '') {
                layui.common.alertAutoClose("企微应用id有误");
                return;
            }
            layui.$('#workWxAppId').val(workWxAppId);
            layui.externalContact.bindEvent('stat');
            layui.externalContact.getKfCaseList(workWxAppId);
            layui.externalContact.queryStatForUpdate({ workWxAppId: workWxAppId, date: layui.$('#date').val() });

            var isWeekday = new Date().getDay() % 6 !== 0;
            layui.$('#isWeekday').val(isWeekday ? 'true' : 'false');

        })
    </script>
</body>
</html>