<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天对话</title>
    <style>
        html { background-color: #f0f0f0; }
        body { font-family: Arial, sans-serif; margin: 0; /*padding: 20px;*/ background-color: #f0f0f0; height: 450px; overflow-y: scroll; }
        .chat-container { max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; height: 100%; }
        .chat-title { font-size: 24px; font-weight: bold; margin-bottom: 20px; text-align: center; }
        .message { display: flex; margin-bottom: 15px; }
        .message.bot { justify-content: flex-start; }
        .message.user { justify-content: flex-end; }
        .message-content { max-width: 70%; padding: 10px; border-radius: 8px; position: relative; }
        .bot .message-content { background-color: #e6f3ff; }
        .user .message-content { background-color: #dcf8c6; }
        .avatar { width: 30px; height: 30px; border-radius: 50%; margin-right: 10px; background-color: #ddd; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 10px; }
        .bot .avatar { background-color: #5fb0ff; }
        .user .avatar { background-color: #7fba00; order: 1; margin-right: 0; margin-left: 10px; }
        .message-text { margin: 0; }
        .message-time { font-size: 12px; color: #888; margin-top: 5px; margin-bottom: 0 }
    </style>
</head>
<body>
    <div class="chat-container">
        <div id="chat-messages">
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'callbot'], function () {
            layui.callbot.getMessage();
        })
    </script>
</body>
</html>