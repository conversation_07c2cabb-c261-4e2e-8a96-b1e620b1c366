﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 450px; }
    </style>
</head>
<body>
    <form id="uploadForm" method="post" enctype="multipart/form-data">
        <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
            <div class="layui-form-item">
                <label class="layui-form-label">所属客户</label>
                <div class="layui-input-inline">
                    <select name="customerId" id="customer-view" lay-verify="required" lay-filter="customer-select" lay-search></select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">域名</label>
                <div class="layui-input-inline">
                    <select name="domainId" id="domain-view" lay-verify="required" lay-search>
                        <option value="">请选择域名</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">落地页</label>
                <div class="layui-input-inline">
                    <select name="pageId" id="page-view" lay-verify="required" lay-search></select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <div class="layui-input-inline">
                    <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="keyword-config-export-submit" value="确认导出">
                </div>
            </div>
        </div>
    </form>
    <script id="customer-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.alias}}</option>
        {{#  }); }}
    </script>
    <script id="domain-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.domainName}}</option>
        {{#  }); }}
    </script>
    <script id="page-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">
            {{item.title}}
            {{# if(item.path!=''){}}
            【{{item.path}}】
            {{# }}}
        </option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'keywordConfig'], function () {
            layui.keywordConfig.initCustomer();
            layui.keywordConfig.initPage();
            //切换客户时加载域名列表
            layui.form.on('select(customer-select)', function (data) {
                layui.keywordConfig.initDomain(data.value);
            });
            //监听提交事件
            layui.form.on('submit(keyword-config-export-submit)', function (data) {
                layui.keywordConfig.export(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>