﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 80px; }
        .layui-form-item .layui-input-inline { width: 400px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 10px 30px 0 0;">
        <input id="orderId" name="orderId" type="hidden" />
        <div class="layui-form-item" style="margin-bottom:0">
            <label class="layui-form-label">客户信息</label>
            <div class="layui-input-inline">
                <label class="layui-form-mid layui-word-aux" id="customerInfo"></label>
            </div>
        </div>
        <div class="layui-form-item" style="margin-bottom:0">
            <label class="layui-form-label">产品信息</label>
            <div class="layui-input-inline">
                <label class="layui-form-mid layui-word-aux" id="productInfo"></label>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">订单号</label>
            <div class="layui-input-inline">
                <label class="layui-form-mid layui-word-aux" id="orderInfo" style="width:400px;word-break:break-word;"></label>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">退款金额</label>
            <div class="layui-input-inline">
                <input type="text" id="amount" disabled="disabled" name="amount" autocomplete="off" class="layui-input layui-disabled">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">退款备注</label>
            <div class="layui-input-inline">
                <input type="text" id="remark" disabled="disabled" name="remark" autocomplete="off" class="layui-input layui-disabled">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">银行卡号</label>
            <div class="layui-input-inline">
                <input type="text" id="bankCardNo" name="bankCardNo" disabled="disabled" autocomplete="off" class="layui-input layui-disabled">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">开户行</label>
            <div class="layui-input-inline">
                <input type="text" id="openBankBranch" name="openBankBranch" disabled="disabled" autocomplete="off" class="layui-input layui-disabled">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">审核意见</label>
            <div class="layui-input-inline">
                <select name="auditStatus" id="auditStatus" lay-verify="required">
                    <option value="">请选择审核意见</option>
                    <option value="2">通过</option>
                    <option value="3">驳回</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">审核备注</label>
            <div class="layui-input-inline">
                <textarea type="text" name="auditRemark" placeholder="请输入审核备注信息" autocomplete="off" class="layui-textarea" style="min-height:60px"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="order-refund-submit" value="审核退款">
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['productOrder'], function () {
            layui.productOrder.getRefundOrder();
            //监听提交事件
            layui.form.on('submit(order-refund-submit)', function (data) {
                layui.productOrder.auditRefund(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>