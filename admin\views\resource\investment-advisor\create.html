﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">所属组织</label>
            <div class="layui-input-inline">
                <select name="subsidiaryId" id="subsidiary-view" lay-search></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">绑定账号</label>
            <div class="layui-input-inline">
                <select name="userId" id="user-view" lay-filter="account" lay-search>
                    <option value="">请选择绑定账号</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">名称</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入投顾姓名" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">执业编号</label>
            <div class="layui-input-inline">
                <input type="text" name="certNo" lay-verify="required" placeholder="请输入执业编号" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">凭证</label>
            <div class="layui-input-inline">
                <div class="layui-inline">
                    <button type="button" class="layui-btn layui-btn-primary " id="uploadImg_default"><i class="layui-icon"></i>上传凭证</button>
                    <input id="certificateUrl" name="certificateUrl" value="" type="hidden" />
                </div>
                <div class="layui-inline img-info">
                </div>
                <div class="layui-inline">
                    <div class="layui-form-mid layui-word-aux"><a href="javascript:;" style="color:#0094ff;margin-left:10px;margin-right:0" class="preview">查看示例</a></div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="submit" value="确认添加">
            </div>
        </div>
    </div>
    <script id="subsidiary-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="user-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.1"></script>
    <script type="text/javascript">
        layui.use(['form', 'investmentAdvisor'], function () {
            layui.investmentAdvisor.initSubsidiary();
            layui.investmentAdvisor.initUser();
            //监听提交事件
            layui.form.on('submit(submit)', function (data) {
                if (data.field.certificateUrl == '') {
                    layui.common.alertAutoClose("请上传凭证");
                    return;
                }
                layui.investmentAdvisor.create(data);
                return false; //阻止表单跳转
            });
            layui.$('.preview').click(function () {
                parent.layui.common.openIframe('查看示例', 700, 450, 'preview.html?url=//dn-f.oss-cn-shanghai.aliyuncs.com/files/tougu_example.png');
            });
            //上传凭证
            layui.uploadFile('resource', 'uploadImg_default', '/common/file/public/put?folder=admin/investment-advisor/cert', function (res) {
                if (res.isSuccess) {
                    layui.$('.img-info').html('<img src="' + res.result + '" class="" height="35" />');
                    layui.$('#certificateUrl').val(res.result);
                    layui.common.alertAutoClose("上传成功");
                }
                else {
                    layui.common.alert(res.message, 2);
                }
            });
            layui.form.on('select(account)', function (data) {
                if (data.value != '' && layui.$('input[name=name]').val() == '') {
                    layui.$('input[name=name]').val(layui.$(data.elem).find('option:selected').text());
                }
            });
        })
    </script>
</body>
</html>