﻿layui.define(['laytpl', 'form', 'request', 'tableRequest', 'common', 'company'], function (exports) {
    var func = {
        /**
         * 通过id获取单个抽奖活动信息
         * */
        get: function (page) {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                setTimeout(function () {
                    parent.location.reload();
                }, 3000);
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/marketing/lottery-draw/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    if (page == 'update') {
                        layui.$('input[name=id]').val(res.result.id);
                        layui.$('input[name=name]').val(res.result.name);
                        layui.$('#mobiles').val(res.result.mobiles.join('\r\n'));
                    }
                    else if (page == 'read') {
                        // 已知数据渲染
                        layui.table.render({
                            elem: '#user-list',
                            cols: [[ //标题栏
                                { field: 'number', title: '序号' },
                                { field: 'mobile', title: '手机号' },
                                { field: 'clientIP', title: '操作IP' },
                                {
                                    field: 'createdAt', title: '操作时间', templet: function (e) {
                                        return layui.common.timeFormat(e.createdAt);
                                    }
                                }
                            ]],
                            data: res.result.winningUsers,
                            limit: 200
                        });
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过条件分页查询抽奖活动列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'lottery-draw-list', '/admin/marketing/lottery-draw/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '活动名称' },
                { field: 'userCount', title: '参与用户数' },
                {
                    field: 'winningUserCount', title: '中奖用户数', templet: function (e) {
                        if (e.winningUserCount > 0) {
                            var onclick = 'layui.common.openIframe(\'查看中奖用户\', 700, 400, \'read.html?id=' + e.id + '\')';
                            return '<a href="javascript:;"  onclick="' + onclick + '" class="link">' + e.winningUserCount + '</a>';
                        } else {
                            return e.winningUserCount
                        }
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', align: 'left', width: 220, toolbar: '#lottery-draw-bar' }
            ]);
            //监听表格事件
            layui.lotteryDraw.tableEvent();
        },
        /**
        * 创建单个抽奖活动
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/marketing/lottery-draw/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("抽奖活动创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过id修改单个抽奖活动信息
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/marketing/lottery-draw/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("抽奖活动编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 重置抽奖活动中奖用户
        * @param {any} id
        */
        reset: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/marketing/lottery-draw/reset?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("重置抽奖活动中奖用户成功");
                    layui.lotteryDraw.query();
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除抽奖活动
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/marketing/lottery-draw/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("抽奖活动删除成功");
                    layui.lotteryDraw.query();
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑抽奖活动', 600, 400, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该抽奖活动吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.lotteryDraw.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'status') {
                    var confirmIndex = layui.layer.confirm('确定重置该抽奖活动中奖用户吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.lotteryDraw.reset(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('lotteryDraw', func);
});