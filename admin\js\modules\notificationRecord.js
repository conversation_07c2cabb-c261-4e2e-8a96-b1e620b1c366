﻿layui.define(['laytpl', 'request', 'tableRequest', 'common'], function (exports) {
    var func = {
        /**
         * 获取微信公众号消息推送记录列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'notification-record-list', '/admin/notification/message-record/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'appId', title: '公众号ID' },
                { field: 'type', title: '类型' },
                { field: 'toUser', title: '接收用户openid' },
                {
                    field: 'isSuccess', title: '请求状态', templet: function (e) {
                        return e.isSuccess ? '成功' : '失败-' + e.errMsg;
                    }
                },
                { field: 'requestParams', title: '请求参数' },
                { field: 'responseParams', title: '返回参数' },
                {
                    field: 'createdAt', title: '发送时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                }
            ]);
        }
    }
    exports('notificationRecord', func);
});