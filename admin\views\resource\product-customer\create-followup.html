﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 400px; margin: 0 }
    </style>
</head>
<body>
    <div class="layui-form">
        <input id="customerId" name="customerId" type="hidden" />
        <input id="orderId" name="orderId" type="hidden" />
        <div class="layui-form-item">
            <div class="layui-input-inline">
                <textarea name="content" lay-verify="required" placeholder="请输入沟通内容" autocomplete="off" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-inline" style="text-align:center;">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="followup-create-submit" value="确认添加">
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'productCustomer'], function () {
            var customerId = layui.common.getUrlParam('customerId');
            var orderId = layui.common.getUrlParam('orderId');
            if (customerId == '' && orderId == '') {
                parent.layui.common.alertAutoClose("参数有误");
                parent.layui.common.closeType('iframe');
                return;
            }
            layui.$('#customerId').val(customerId);
            layui.$('#orderId').val(orderId);
            layui.form.on('submit(followup-create-submit)', function (data) {
                layui.productCustomer.createFollowUpRecord(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>