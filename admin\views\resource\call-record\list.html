﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        audio { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">用户名</label>
                        <div class="layui-input-inline">
                            <input id="txb_userName" name="userName" placeholder="请输入用户名" type="text" class="layui-input" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">呼叫号码</label>
                        <div class="layui-input-inline">
                            <input id="txb_keywords" name="keywords" placeholder="主叫号码/被叫号码" type="text" class="layui-input" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">呼叫时间</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" autocomplete="off" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" autocomplete="off" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="call-record-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="call-record-list" lay-filter="list"></table>
                <script type="text/html" id="call-record-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="player">播放</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="download">下载</a>
                </script>
            </div>
        </div>
    </div>
    <div id="audio-dialog" style="display:none;">
        <audio id="audio_dom" style="width:100%;" autoplay="autoplay" controls>
            <source src="" type="audio/mpeg">
        </audio>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'laydate', 'callRecord'], function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            layui.callRecord.query();
            //监听查询按钮
            layui.form.on('submit(call-record-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("call-record-list", {
                    where: field
                });
            });
        });
    </script>
</body>
</html>