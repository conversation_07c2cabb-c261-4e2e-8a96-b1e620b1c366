﻿layui.define(['laytpl', 'xmSelect', 'request', 'tableRequest', 'setter', 'common', 'company', 'role', 'department'], function (exports) {
    var func = {
        currentCompanyId: function () {
            return layui.common.getUrlParam('companyId') || ''
        },
        /**
         * 获取所有下级用户列表
         * @param {any} companyId
         * @param {any} isIncludeCurUser
         * @param {any} callbackFunc
         */
        getChildUser: function (companyId, isIncludeCurUser, callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/sso/user/childs-for-add/query',
                data: JSON.stringify({ companyId: companyId, isIncludeCurUser: isIncludeCurUser }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 通过权限配置获取下级用户列表
         * @param {any} companyId
         * @param {any} selectId
         */
        getChildUserByPermission: function (companyId, selectId) {
            layui.adminUser.getChildUser(companyId, true, function (res) {
                res.result.unshift({ id: '', name: '请选择所属上级' });
                var getTpl = document.getElementById("parent-user-tpl").innerHTML
                    , view = document.getElementById('parent-user-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (selectId != '') {
                    view.value = selectId;
                }
                layui.form.render('select');
            })
        },
        /**
         * 通过企业id获取用户列表（账号脱敏处理）
         * @param {any} companyId
         * @param {any} callbackFunc
         */
        getUserList: function (companyId, callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/sso/user/for-sign/query?companyId=' + companyId,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 通过企业id获取用户列表
         * @param {any} companyId
         * @param {any} callbackFunc
         */
        queryByCompanyId: function (companyId, callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/sso/user/by-companyid/query?companyId=' + companyId,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 渲染企业下拉选框
         * @param {any} id
         */
        initCompany: function (id, page, workUserId) {
            layui.company.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企业' });
                var getTpl = document.getElementById("company-tpl").innerHTML
                    , view = document.getElementById('company-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined && id != '') {
                    document.getElementById('company-view').value = id;
                }
                layui.form.render('select');
                if (page == 'list') {
                    layui.adminUser.query();
                }
                if (page == 'create' && id != '') {
                    layui.$('#company-item').hide();
                    layui.adminUser.initDepartment(id, '');
                    layui.adminUser.getChildUserByPermission(id, '');
                    layui.adminUser.initRole(id, '');
                }
                if (page == 'update') {
                    //渲染企微账号下拉选框
                    var workUserIds = [];
                    if (workUserId != null && workUserId != '') {
                        workUserIds = workUserId.split(',');
                    }
                    layui.adminUser.initWorkUser(layui.$('#company-view option:selected').data('corpid'), workUserIds);
                }
            });
        },
        /**
         * 渲染部门下拉选框
         * @param {any} companyId
         * @param {any} selectId
         */
        initDepartment: function (companyId, selectId) {
            layui.department.queryByCompanyId(companyId, function (res) {
                var json = [{ id: '', name: '请选择所属部门' }];
                for (var i = 0; i < res.result.length; i++) {
                    var level1 = { id: res.result[i].id, name: res.result[i].name };
                    json.push(level1);
                    var child2 = res.result[i].childs;
                    if (child2 != null && child2.length > 0) {
                        for (var j = 0; j < child2.length; j++) {
                            var level2 = { id: child2[j].id, name: res.result[i].name + ' > ' + child2[j].name };
                            json.push(level2);
                            var child3 = child2[j].childs;
                            if (child3 != null && child3.length > 0) {
                                for (var k = 0; k < child3.length; k++) {
                                    var level3 = { id: child3[k].id, name: res.result[i].name + ' > ' + child2[j].name + ' > ' + child3[k].name };
                                    json.push(level3);
                                    var child4 = child3[k].childs;
                                    if (child4 != null && child4.length > 0) {
                                        for (var m = 0; m < child4.length; m++) {
                                            var level4 = { id: child4[m].id, name: res.result[i].name + ' > ' + child2[j].name + ' > ' + child3[k].name + ' > ' + child4[m].name };
                                            json.push(level4);
                                            var child5 = child4[m].childs;
                                            if (child5 != null && child5.length > 0) {
                                                for (var n = 0; n < child5.length; n++) {
                                                    var level5 = { id: child5[n].id, name: res.result[i].name + ' > ' + child2[j].name + ' > ' + child3[k].name + ' > ' + child4[l].name + ' > ' + child5[n].name };
                                                    json.push(level5);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                var getTpl = document.getElementById("department-tpl").innerHTML
                    , view = document.getElementById('department-view');
                layui.laytpl(getTpl).render(json, function (html) {
                    view.innerHTML = html;
                });
                if (selectId != '') {
                    view.value = selectId;
                }
                layui.form.render('select');
            });
        },
        /**
         * 渲染所属上级下拉选框
         * @param {any} companyId
         * @param {any} selectId
         */
        initParentUser: function (companyId, selectId) {
            layui.adminUser.queryByCompanyId(companyId, function (res) {
                res.result.unshift({ id: '', name: '请选择所属上级' });
                var getTpl = document.getElementById("parent-user-tpl").innerHTML
                    , view = document.getElementById('parent-user-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (selectId != '') {
                    view.value = selectId;
                }
                layui.form.render('select');
            });
        },
        /**
         * 初始化下级用户列表
         * @param {any} companyId
         * @param {any} selectId
         */
        initChildUser: function (companyId, selectId) {
            layui.request({
                method: 'post',
                url: '/admin/sso/user/childs/query',
                data: JSON.stringify({ companyId: companyId, isIncludeCurUser: true }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    res.result.unshift({ id: '', name: '请选择所属上级' });
                    var getTpl = document.getElementById("parent-user-tpl").innerHTML
                        , view = document.getElementById('parent-user-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    if (selectId != '') {
                        view.value = selectId;
                    }
                    layui.form.render('select');
                }
            })
        },
        /**
         * 通过应用id获取企微成员列表
         * @param {any} workWxAppId
         * @param {any} selectIds
         */
        initWorkUser: function (corpid, selectIds) {
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/workwx/department-user/by-corpid/query?corpid=' + corpid,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    var data = [];
                    var selectItems = [];
                    if (res.result.length > 0) {
                        for (var i = 0; i < res.result.length; i++) {
                            var item = { name: res.result[i].name + '（' + res.result[i].userIdFormat + '）', value: res.result[i].userId };
                            data.push(item);
                            if (selectIds.length > 0 && selectIds.indexOf(res.result[i].userId) > -1) {
                                selectItems.push(item);
                            }
                        }
                    }
                    xmSel = layui.xmSelect.render({
                        el: '#workwx-user',
                        language: 'zn',
                        filterable: true,
                        tips: '请选择绑定的企微账号',
                        theme: { color: '#0081ff ' },
                        data: data,
                        toolbar: { show: true },
                        autoRow: true,
                    });
                    if (selectItems.length > 0) {
                        xmSel.setValue(selectItems);
                    }
                }
                else {
                    xmSel = layui.xmSelect.render({
                        el: '#workwx-user',
                        language: 'zn',
                        filterable: true,
                        tips: '请选择绑定的企微账号',
                        theme: { color: '#0081ff ' },
                        data: [],
                        toolbar: { show: true },
                        autoRow: true,
                    });
                }
            })
        },
        /**
         * 渲染角色列表
         * @param {any} companyId
         * @param {any} checkedIds
         */
        initRole: function (companyId, checkedIds) {
            layui.role.queryByCompanyId(companyId, false, function (res) {
                if (res.result != null && res.result.length > 0 && checkedIds.length > 0) {
                    for (var i = 0; i < res.result.length; i++) {
                        var isChecked = checkedIds.includes(res.result[i].id);
                        res.result[i].isChecked = isChecked;
                    }
                }
                var getTpl = document.getElementById("role-tpl").innerHTML
                    , view = document.getElementById('role-list-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('checkbox');
            });
        },
        /**
         * 渲染下级用户角色列表
         * @param {any} companyId
         * @param {any} checkedIds
         */
        initChildRole: function (companyId, checkedIds) {
            var result = [{ id: '640a886e985635ef61aceab0', name: '销售主任' },
            { id: '63637210032f950cc014a1c3', name: '销售专员' }];
            if (result != null && result.length > 0 && checkedIds.length > 0) {
                for (var i = 0; i < result.length; i++) {
                    var isChecked = checkedIds.includes(result[i].id);
                    result[i].isChecked = isChecked;
                }
            }
            var getTpl = document.getElementById("role-tpl").innerHTML
                , view = document.getElementById('role-list-view');
            layui.laytpl(getTpl).render(result, function (html) {
                view.innerHTML = html;
            });
            layui.form.render('checkbox');
        },
        /**
         * 通过id获取用户信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id') || '';
            var companyId = layui.common.getUrlParam('companyId') || '';
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/sso/user/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=account]').val(res.result.account);
                    layui.adminUser.initCompany(res.result.companyId, 'update', res.result.workUserId);
                    layui.adminUser.initDepartment(res.result.companyId, res.result.departmentId);
                    layui.adminUser.getChildUserByPermission(res.result.companyId, res.result.parentId);
                    layui.adminUser.initRole(res.result.companyId, res.result.roleIds);
                    layui.$('#isSuperAdmin').val(res.result.isSuperAdmin ? 'true' : 'false');
                    if (companyId != '') {
                        layui.$('#company-item').hide();
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取下级用户详情
         * */
        getChild: function () {
            var id = layui.common.getUrlParam('id') || '';
            layui.request({
                method: 'post',
                url: '/admin/sso/user/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=account]').val(res.result.account);
                    layui.$('input[name=workUserId]').val(res.result.workUserId);
                    layui.$('input[name=companyId]').val(res.result.companyId);
                    //layui.$('input[name=departmentId]').val(res.result.departmentId);
                    layui.adminUser.initChildUser(res.result.companyId, res.result.parentId);
                    layui.adminUser.initChildRole(res.result.companyId, res.result.roleIds);
                    layui.adminUser.initDepartment(res.result.companyId, res.result.departmentId);
                    //渲染企微账号下拉选框
                    var workUserIds = [];
                    if (res.result.workUserId != null && res.result.workUserId != '') {
                        workUserIds = res.result.workUserId.split(',');
                    }
                    layui.adminUser.initWorkUser(layui.$('#company-view option:selected').data('corpid'), workUserIds);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取后台用户列表
         * */
        query: function () {
            var companyId = layui.common.getUrlParam('companyId') || '';
            if (companyId != '') {
                layui.$('#company-view').val(companyId);
                layui.$('#company-view').attr('disabled', 'true');
                layui.form.render('select');
                layui.$('#company-item').hide();
            }
            layui.tableRequest.request('resource', true, 'admin-user-list', '/admin/sso/user/query', 'application/json', [
                { type: 'checkbox', fixed: 'left' },
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'companyName', title: '所属企业' },
                {
                    field: 'name', title: '姓名', templet: function (e) {
                        var name = e.name;
                        if (e.parentName != '') {
                            name += '（上级：' + e.parentName + '）';
                        }
                        return name;
                    }
                },
                { field: 'account', title: '登录账号' },
                { field: 'departmentName', title: '所属部门' },
                {
                    field: 'roleNames', title: '角色', templet: function (e) {
                        var roleNames = '-';
                        if (e.roleNames != null && e.roleNames.length > 0) {
                            roleNames = '';
                            for (var i = 0; i < e.roleNames.length; i++) {
                                if (roleNames != '')
                                    roleNames += '，';
                                roleNames += e.roleNames[i];
                            }
                        }
                        return roleNames;
                    }
                },
                {
                    field: 'status', title: '状态', width: 80, templet: function (e) {
                        var statusDesc = '';
                        switch (e.status) {
                            case 1:
                                statusDesc = '正常';
                                break;
                            case 2:
                                statusDesc = '冻结';
                                break;
                            case 3:
                                statusDesc = '离职';
                                break;
                            default:
                        }
                        return statusDesc;
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 220, align: 'center', toolbar: '#admin-user-bar' }
            ], { companyId: companyId }, '#topToolBar');
            //监听表格事件
            layui.adminUser.tableEvent();
        },
        queryChilds: function () {
            var companyId = layui.common.getUrlParam('companyId') || '';
            if (companyId == '') {
                layui.common.closeType('iframe');
                layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.tableRequest.request('resource', false, 'admin-user-list', '/admin/sso/user/childs/query', 'application/json', [
                { type: 'checkbox', fixed: 'left' },
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '姓名' },
                { field: 'account', title: '登录账号' },
                { field: 'departmentName', title: '所属部门' },
                {
                    field: 'roleNames', title: '角色', templet: function (e) {
                        var roleNames = '-';
                        if (e.roleNames != null && e.roleNames.length > 0) {
                            roleNames = '';
                            for (var i = 0; i < e.roleNames.length; i++) {
                                if (roleNames != '')
                                    roleNames += '，';
                                roleNames += e.roleNames[i];
                            }
                        }
                        return roleNames;
                    }
                },
                {
                    field: 'status', title: '状态', width: 80, templet: function (e) {
                        var statusDesc = '';
                        switch (e.status) {
                            case 1:
                                statusDesc = '正常';
                                break;
                            case 2:
                                statusDesc = '冻结';
                                break;
                            case 3:
                                statusDesc = '离职';
                                break;
                            default:
                        }
                        return statusDesc;
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 120, align: 'left', toolbar: '#admin-user-bar' }
            ], { companyId: companyId }, '#topToolBar');
            //监听表格事件
            layui.adminUser.tableEvent();
        },
        /**
         * 创建后台用户
         * @param {any} data
         */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            var encryptor = new JSEncrypt();
            encryptor.setPublicKey(layui.setter.rsaPublicKey);
            data.field.password = encryptor.encrypt(data.field.password);
            layui.request({
                method: 'post',
                url: '/admin/sso/user/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("admin-user-list");
                    parent.layui.common.alertAutoClose("用户创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过id修改单个用户信息
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/sso/user/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("admin-user-list");
                    parent.layui.common.alertAutoClose("用户修改成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除用户
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/sso/user/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("admin-user-list");
                    layui.common.alertAutoClose("用户删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 批量修改用户登录密码
         * @param {any} ids
         * @param {any} password
         */
        batchUpdatePwd: function (ids, password) {
            var encryptor = new JSEncrypt();
            encryptor.setPublicKey(layui.setter.rsaPublicKey);
            password = encryptor.encrypt(password);
            layui.request({
                method: 'post',
                url: '/admin/sso/user/password/batch-update',
                data: JSON.stringify({
                    ids: ids,
                    password: password
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("admin-user-list");
                    layui.common.alertAutoClose("用户密码重置成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 批量修改用户状态
         * @param {any} ids
         * @param {any} status
         * @param {any} eventName
         */
        batchUpdateStatus: function (ids, status, eventName) {
            layui.request({
                method: 'post',
                url: '/admin/sso/user/status/batch-update',
                data: JSON.stringify({
                    ids: ids,
                    status: status
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("admin-user-list");
                    layui.common.alertAutoClose("用户" + eventName + "成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 批量删除用户
         * @param {any} ids
         */
        batchDelete: function (ids) {
            layui.request({
                method: 'post',
                url: '/admin/sso/user/batch-delete',
                data: JSON.stringify({
                    ids: ids
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("admin-user-list");
                    layui.common.alertAutoClose("用户删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    var updateUrl = 'update.html?id=' + data.id;
                    var companyId = layui.common.getUrlParam('companyId') || '';
                    if (companyId != '') {
                        updateUrl += '&companyId=' + companyId;
                    }
                    layui.common.openIframe('编辑用户', 650, 600, updateUrl);
                }
                if (obj.event === 'child-edit') {
                    var updateUrl = 'update-child.html?id=' + data.id;
                    var companyId = layui.common.getUrlParam('companyId') || '';
                    var workWxAppId = layui.common.getUrlParam('workWxAppId') || '';
                    if (companyId != '') {
                        updateUrl += '&companyId=' + companyId;
                    }
                    if (workWxAppId != '') {
                        updateUrl += '&workWxAppId=' + workWxAppId;
                    }
                    layui.common.openIframe('编辑用户', 650, 500, updateUrl);
                }
                if (obj.event === 'seats') {
                    layui.common.openIframe('配置用户坐席', 460, 300, '../../resource/seats/update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该用户吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.adminUser.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'permission') {
                    var saveUrl = '../permission/settab.html?id=' + data.id + '&type=user&name=' + data.name;
                    var companyId = layui.common.getUrlParam('companyId') || '';
                    if (companyId != '') {
                        saveUrl += '&companyId=' + companyId;
                    }
                    var from = layui.common.getUrlParam('from') || '';
                    if (from != '') {
                        saveUrl += '&from=' + from;
                    }
                    location.href = saveUrl;
                }
                else if (obj.event === 'data-permission') {
                    location.href = '../../resource/external-contact/set-permission.html?userId=' + data.id + '&name=' + data.name + '';
                }
            });
            layui.table.on('toolbar(list)', function (obj) {
                var checkedList = layui.table.checkStatus(obj.config.id);
                if (checkedList.data.length == 0) {
                    layui.common.alertAutoClose("请选择需要操作的用户");
                    return;
                }
                var names = '';
                var ids = [];
                for (var i = 0; i < checkedList.data.length; i++) {
                    if (names != '')
                        names += '，';
                    names += checkedList.data[i].name;
                    ids.push(checkedList.data[i].id);
                }
                if (obj.event == 'frozen' || obj.event == 'quit' || obj.event == 'recovery') {
                    var typeName = obj.event == 'frozen' ? '冻结' : obj.event == 'quit' ? '离职' : '恢复';
                    var typeId = obj.event == 'frozen' ? 2 : obj.event == 'quit' ? 3 : 1;
                    var confirmIndex = layui.layer.confirm('确定' + typeName + '【' + names + '】用户吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.layer.close(confirmIndex);
                        layui.adminUser.batchUpdateStatus(ids, typeId, typeName);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event == 'reset') {
                    var confirmIndex = layui.layer.confirm('确定重置【' + names + '】用户密码吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.layer.close(confirmIndex);
                        layer.prompt({ title: '请输入新密码', formType: 1 }, function (pass, index) {
                            layer.close(index);
                            layui.adminUser.batchUpdatePwd(ids, pass);
                        });
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除【' + names + '】用户信息吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.adminUser.batchDelete(ids);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        },
        bindEvent: function () {
            //切换企业时加载数据
            layui.form.on('select(company-view)', function (data) {
                //加载部门
                layui.adminUser.initDepartment(data.value, '');
                //加载上级领导
                layui.adminUser.getChildUserByPermission(data.value, '');
                //加载角色
                layui.adminUser.initRole(data.value, '');

                //渲染企微账号下拉选框
                layui.adminUser.initWorkUser(layui.$('#company-view option:selected').data('corpid'), []);
            });
        }
    }

    exports('adminUser', func);
});