﻿layui.define(['laytpl', "layer", 'form', 'request', 'uploadSsoFile', 'tableRequest', 'common', 'setter', 'subsidiary'], function (exports) {
    const headers = {
        // 指定该Object被下载时的网页缓存行为。
        "Cache-Control": "no-cache",
        // 指定该Object被下载时的名称。
        "Content-Disposition": "example.txt",
        // 指定该Object被下载时的内容编码格式。
        "Content-Encoding": "utf-8",
        // 指定过期时间，单位为毫秒。
        Expires: "1000",
        // 指定Object的存储类型。
        "x-oss-storage-class": "Standard",
        // 指定Object标签，可同时设置多个标签。
        "x-oss-tagging": "Tag1=1&Tag2=2",
        // 指定初始化分片上传时是否覆盖同名Object。此处设置为true，表示禁止覆盖同名Object。
        "x-oss-forbid-overwrite": "true",
    };
    var func = {
        /**
      * 渲染所属组织下拉选框
      * */
        initSubsidiary: function (id) {
            layui.subsidiary.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择所属组织' });
                var getTpl = document.getElementById("subsidiary-tpl").innerHTML
                    , view = document.getElementById('subsidiary-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('subsidiary-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 初始化富文本编辑器
         * */
        initTinymce: function () {
            var config = {
                selector: '#content',
                language: 'zh_CN',
                menubar: false,
                branding: false,
                plugins: 'preview fullscreen image link media code table advlist lists wordcount emoticons autosave autoresize axupimgs',
                toolbar: 'code pastetext fontsize fontfamily forecolor backcolor link alignleft aligncenter alignright alignjustify image media axupimgs  outdent indent styleselect bullist numlist bold italic underline strikethrough blockquote subscript superscript removeformat table charmap emoticons pagebreak insertdatetime preview fullscreen bdmap indent2em lineheight formatpainter',
                toolbar_mode: 'wrap',
                min_height: 400,
                max_height: 600,
                font_size_formats: '11px 12px 13px 14px 15px 16px 17px 18px 20px 24px 36px 48px 56px 72px',
                font_size_input_default_unit: "px",
                font_family_formats: '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;',
                importcss_append: true,
                content_style: "img {max-width: 100%}",
                images_upload_handler(blobInfo, progress) {
                    return new Promise((resolve, reject) => {
                        var upurl = layui.setter.request.resourceBaseUrl + '/common/file/public/tinymce/put?folder=files/media/detail';
                        var file = blobInfo.blob();
                        var xhr, formData;
                        xhr = new XMLHttpRequest();
                        xhr.withCredentials = false;
                        xhr.open('POST', upurl);
                        xhr.onload = function () {
                            if (xhr.status != 200) {
                                reject('HTTP Error: ' + xhr.status);
                                return;
                            }
                            var json = JSON.parse(xhr.responseText);
                            if (!json || json.location == '') {
                                reject(xhr.responseText);
                                return;
                            }
                            resolve(json.location, { width: 'auto', height: 'auto' });
                        };
                        formData = new FormData();
                        formData.append('file', file, file.name);
                        xhr.send(formData);
                    })
                },
                //自定义文件选择器的回调内容
                file_picker_callback: function (callback, value, meta) {
                    var filetype = '.pdf, .txt, .zip, .rar, .7z, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .mp3, .mp4';
                    var upurl = layui.setter.request.resourceBaseUrl + '/common/file/public/tinymce/put?folder=files/media/detail';
                    switch (meta.filetype) {
                        case 'image':
                            filetype = '.jpg, .jpeg, .png, .gif';
                            break;
                        case 'media':
                            filetype = '.mp3, .mp4';
                            break;
                        default:
                    }
                    var input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    input.setAttribute('accept', filetype);
                    input.click();
                    input.onchange = function () {
                        var file = this.files[0];
                        var xhr, formData;
                        xhr = new XMLHttpRequest();
                        xhr.withCredentials = false;
                        xhr.open('POST', upurl);
                        xhr.onload = function () {
                            var json;
                            if (xhr.status != 200) {
                                failure('HTTP Error: ' + xhr.status);
                                return;
                            }
                            json = JSON.parse(xhr.responseText);
                            if (!json || typeof json.location != 'string') {
                                failure('Invalid JSON: ' + xhr.responseText);
                                return;
                            }
                            callback(json.location, { width: 'auto', height: 'auto' });
                        };
                        formData = new FormData();
                        formData.append('file', file, file.name);
                        xhr.send(formData);
                    };
                },
                autosave_ask_before_unload: false
            };
            tinymce.init(config);
        },
        /**
         * 上传附件
         * */
        initFile: function () {
            var files = [];
            let accessKeyId, accessKeySecret, securityToken, client;
            layui.request({
                method: 'get',
                url: '/admin/common/get/assume',
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    accessKeyId = res.result.accessKeyId
                    accessKeySecret = res.result.accessKeySecret
                    securityToken = res.result.securityToken
                    client = new OSS({
                        // yourRegion填写Bucket所在地域。以华东1（杭州）为例，Region填写为oss-cn-hangzhou。
                        region: "oss-cn-shanghai",
                        secure: true,
                        // 从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
                        accessKeyId,
                        accessKeySecret,
                        // 从STS服务获取的安全令牌（SecurityToken）。
                        stsToken: securityToken,
                        // 填写Bucket名称，例如examplebucket。
                        bucket: "dn-pub",
                    });
                }
            })
            layui.uploadSsoFile('sso', 'uploadAnnex', function (obj) {
                obj.preview(function (index, file, result) {
                    files.push({ index: index, name: file.name, size: file.size, url: '' })
                    layui.$('.file-list').append('<li id="' + index + '" style="margin:10px 30px;width: calc(100% - 60px);" class="layui-progress" lay-filter="' + index + '"> <div class="layui-progress-bar layui-bg-orange" lay-percent="0%"></div></li>')
                    layui.element.render('progress', index);
                    layui.media.ossFile(file, client, index)
                });
            });
        },
        /**切片上传 */
        async ossFile(data, client, index, isView, callback, filename = "files/media/annex/", loddingIndex) {
            let type = data.name.substring(data.name.lastIndexOf(".") + 1)
            filename = filename + layui.common.GUID() + "." + type
            try {
                // 分片上传。
                const res = await client.multipartUpload(filename, data, {
                    // 获取分片上传进度、断点和返回值。
                    progress: (p, cpt, res) => {
                        // console.log(p);
                        layui.element.progress(index, p * 100 + "%");
                        if (p == 1) {
                            if (!isView) {
                                layui.$("#" + index)[0].remove()
                            } else {
                                callback(data, filename)
                            }
                        }
                        if (loddingIndex != undefined && loddingIndex != null) {
                            layui.$('#layui-layer' + loddingIndex + ' .layui-layer-content').html('<i class="layui-layer-ico layui-layer-ico16"></i>上传中（' + (p * 100).toFixed(0) + "%" + '）');
                        }
                    },
                    // 设置并发上传的分片数量。
                    parallel: 4,
                    // 设置分片大小。默认值为1 MB，最小值为100 KB。
                    partSize: 1024 * 1024 * 50,
                    // headers,
                    // 自定义元数据，通过HeadObject接口可以获取Object的元数据。
                    meta: {},
                    // mime: "text/plain",
                });
                if (!isView) {
                    layui.$('.file-list').append('<li id="li_' + index +
                        '"><a href="' + "https://file.dnyx.cn/" + filename + '" target="_blank"><i class="layui-icon layui-icon-link"></i> ' +
                        data.name + '</a><span data-size="' + data.size + '">' + (data.size / 1024).toFixed(1) + 'KB</span> <i class="layui-icon layui-icon-delete remove" onclick="layui.$(this).parent(\'li\').remove()"></i></li>');
                }
            } catch (err) {
                console.log(err);
            }
        },
        /**
         * 通过id获取审核记录
         * @param {*} page
         * @returns
         */
        getRecordQuery: function (id) {
            layui.tableRequest.request('resource', false, 'logTable', '/admin/media/source-material/audit-record/query?id=' + id, 'application/json', [
                {
                    field: 'auditUsername', width: 140, fixed: 'left', title: '内容', templet: "#queryContentTpl"
                },
                {
                    field: "annex", title: "附件列表", templet: "#annexTpl", width: "400"
                },
                { field: 'auditUsername', title: '审核人', width: "150" },
                { field: 'auditRemark', title: '审核意见', width: "150" },
                {
                    field: 'auditTime', title: '审核时间', fixed: 'right', width: 170, templet: function (e) {
                        if (e.auditTime != null) {
                            return layui.common.timeFormat(e.auditTime);
                        }
                        else {
                            return '-';
                        }
                    }
                },
            ], {}, '', 'auto', null, function (resCallback, curr, count) {
                console.log(resCallback)
                if (resCallback.data.length > 0) {
                    layui.$('.log-item').show();
                }
            });
            //监听表格事件
            layui.media.tableEvent();
        },
        /**
         * 通过id获取单个素材信息
         * */
        get: function (page) {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/media/source-material/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    if (page == "info") {
                        layui.$("#contentInfo").append(res.result.content)
                    } else if (page == "edit") {
                        layui.$('input[name=id]').val(res.result.id);
                        layui.$('input[name=title]').val(res.result.title);
                        layui.$('input[name=channelName]').val(res.result.channelName);
                        layui.$('#contentPath').val(res.result.contentPath);
                        if (res.result.annex != null && res.result.annex.length > 0) {
                            for (var i = 0; i < res.result.annex.length; i++) {
                                layui.$('.file-list').append('<li id="li_' + i + '"><a href="' + res.result.annex[i].url + '" target="_blank"><i class="layui-icon layui-icon-link"></i> ' + res.result.annex[i].name + '</a><span data-size="' + res.result.annex[i].size + '">' + (res.result.annex[i].size / 1024).toFixed(1) + 'KB</span> <i class="layui-icon layui-icon-delete remove" onclick="layui.$(this).parent(\'li\').remove()"></i></li>');
                            }
                        }
                        layui.media.initFile();
                        layui.media.initTinymce();
                        layui.media.initSubsidiary(res.result.subsidiaryId);
                        layui.$('#content').val(res.result.content);
                        layui.$('input[name=remark]').val(res.result.remark);
                    } else {
                        layui.$('#subsidiaryId')[0].innerHTML = res.result.subsidiaryName
                        layui.$('#title')[0].innerHTML = res.result.title;
                        layui.$('#channelName')[0].innerHTML = res.result.channelName;
                        layui.$('#contentPath')[0].innerHTML = res.result.contentPath;
                        if (res.result.annex != null && res.result.annex.length > 0) {
                            for (var i = 0; i < res.result.annex.length; i++) {
                                layui.$('.file-list').append('<li id="li_' + i + '"><a href="' + res.result.annex[i].url + '" target="_blank"><i class="layui-icon layui-icon-link"></i> ' + res.result.annex[i].name + '</a><span data-size="' + res.result.annex[i].size + '">' + (res.result.annex[i].size / 1024).toFixed(1) + 'KB</span> <i class="layui-icon layui-icon-delete remove" onclick="layui.$(this).parent(\'li\').remove()"></i></li>');
                            }
                        }
                        layui.media.initFile();
                        layui.media.getRecordQuery(res.result.id)
                        // layui.media.initSubsidiary(res.result.subsidiaryId);
                        layui.$('#content')[0].innerHTML = res.result.content;
                        layui.$('#remark')[0].innerHTML = res.result.remark;
                        if (page == "read") {
                            layui.$('#status')[0].innerHTML = ["-", "待送审", "审核中", "通过", "驳回"][res.result.status];
                            layui.$('#auditRemark')[0].innerHTML = res.result.auditRemark;
                        }
                    }
                    layui.form.render('select');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 查询合规审核素材列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'media-list', '/admin/media/source-material/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'subsidiaryName', title: '所属组织' },
                { field: 'title', title: '标题' },
                { field: 'channelName', title: '渠道名称' },
                { field: 'contentPath', title: '对外内容路径' },
                {
                    title: '合规审核', templet: function (e) {
                        var result = '-';
                        if (e.status == 1) {
                            result = '待送审';
                        }
                        else if (e.status == 2) {
                            result = '待审核';
                        }
                        else if (e.status == 3) {
                            result = '完成';
                        }
                        else if (e.status == 4) {
                            result = '驳回';
                        }
                        if (e.auditRemark != null && e.auditRemark != '') {
                            result += '（' + e.auditRemark + '）';
                        }
                        return result;
                    }
                },
                // {
                //     title: '风控审核', templet: function (e) {
                //         var result = '-';
                //         if (e.fkAuditStatus == 2) {
                //             result = '待审核';
                //         }
                //         else if (e.fkAuditStatus == 3) {
                //             result = '完成';
                //         }
                //         else if (e.fkAuditStatus == 4) {
                //             result = '驳回';
                //         }
                //         if (e.fkAuditRemark != null && e.fkAuditRemark != '') {
                //             result += '（' + e.fkAuditRemark + '）';
                //         }
                //         return result;
                //     }
                // },
                { field: 'auditUsername', title: '审核人' },
                { field: 'userName', title: '用户' },
                { field: 'departmentName', title: '部门' },
                {
                    field: 'submitTime', title: '送审时间', width: 170, templet: function (e) {
                        if (e.submitTime != null) {
                            return layui.common.timeFormat(e.submitTime);
                        }
                        else {
                            return '-';
                        }
                    }
                },
                { fixed: 'right', title: '操作', width: 120, align: 'left', toolbar: '#media-bar' }
            ], { companyId: layui.setter.productCompanyId, classify: layui.$('#classify').val() });
            //监听表格事件
            layui.media.tableEvent();
        },
        /**
         * 通过用户id获取素材列表
         * */
        queryByUserId: function () {
            layui.tableRequest.request('resource', true, 'media-list', '/admin/media/source-material/by-userid/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'subsidiaryName', title: '所属组织' },
                { field: 'title', title: '标题' },
                { field: 'channelName', title: '渠道名称' },
                { field: 'contentPath', title: '对外内容路径' },
                {
                    title: '合规审核', templet: function (e) {
                        var result = '-';
                        if (e.status == 1) {
                            result = '待送审';
                        }
                        else if (e.status == 2) {
                            result = '待审核';
                        }
                        else if (e.status == 3) {
                            result = '完成';
                        }
                        else if (e.status == 4) {
                            result = '驳回';
                        }
                        if (e.auditRemark != null && e.auditRemark != '') {
                            result += '（' + e.auditRemark + '）';
                        }
                        return result;
                    }
                },
                // {
                //     title: '风控审核', templet: function (e) {
                //         var result = '-';
                //         if (e.fkAuditStatus == 2) {
                //             result = '待审核';
                //         }
                //         else if (e.fkAuditStatus == 3) {
                //             result = '完成';
                //         }
                //         else if (e.fkAuditStatus == 4) {
                //             result = '驳回';
                //         }
                //         if (e.fkAuditRemark != null && e.fkAuditRemark != '') {
                //             result += '（' + e.fkAuditRemark + '）';
                //         }
                //         return result;
                //     }
                // },
                { field: 'auditUsername', title: '审核人' },
                { field: 'remark', title: '备注' },
                { field: 'userName', title: '用户' },
                { field: 'departmentName', title: '部门' },
                {
                    field: 'submitTime', title: '送审时间', width: 170, templet: function (e) {
                        if (e.submitTime != null) {
                            return layui.common.timeFormat(e.submitTime);
                        }
                        else {
                            return '-';
                        }
                    }
                },
                { fixed: 'right', title: '操作', width: 210, align: 'left', toolbar: '#media-bar' }
            ], { companyId: layui.setter.productCompanyId, classify: layui.$('#classify').val() });
            //监听表格事件
            layui.media.tableEvent();
        },
        /**
        * 创建单个素材
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/media/source-material/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("创建成功");
                    setTimeout(function () { parent.location.reload() }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑单个素材
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/media/source-material/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("编辑成功");
                    setTimeout(function () { parent.location.reload() }, 2000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 客服编辑送审状态
         * @param {any} id
         * @param {any} status
         */
        updateStatusForKf: function (id, status) {
            var data = { id: id, status: status }
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/media/source-material/status-kf/update',
                data: JSON.stringify(data),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("操作成功");
                    layui.tableRequest.reload('media-list');
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 合规编辑审核状态
        * @param {any} id
        * @param {any} status
        */
        updateStatusForHg: function (data) {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            data.field.id = id;
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/media/source-material/status-hg/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("操作成功");
                    layui.tableRequest.reload('media-list');
                    setTimeout(function () { parent.location.reload() }, 2000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
       * 风控编辑审核状态
       * @param {any} id
       * @param {any} status
       */
        updateStatusForFk: function (data) {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            data.field.id = id;
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/media/source-material/status-fk/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("操作成功");
                    layui.tableRequest.reload('media-list');
                    setTimeout(function () { parent.location.reload() }, 2000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除单个素材
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/media/source-material/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("media-list");
                    layui.common.alertAutoClose("删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 导出
         * @param {any} data
         */
        export: function (data) {
            var confirmIndex = layui.layer.confirm('确定导出当前素材/研报吗？', {
                icon: 3,
                title: '提示',
                btn: ['确定', '取消']
            }, function () {
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/media/source-material/export',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.layer.close(confirmIndex);
                    if (res.isSuccess) {
                        location.href = res.result;
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            }, function () {
                layui.layer.close(confirmIndex);
            });
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('创建素材/研报', 750, 700, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'read') {
                    location.href = 'read.html?id=' + data.id;
                    // layui.common.openIframe('查看素材/研报', 780, 700, 'read.html?id=' + data.id);
                }
                else if (obj.event === 'audit') {
                    location.href = 'audit.html?id=' + data.id;
                    // layui.common.openIframe('审核素材/研报', 780, 700, 'audit.html?id=' + data.id);
                }
                else if (obj.event === 'fk-audit') {
                    layui.common.openIframe('审核素材/研报', 780, 700, 'audit-fk.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该素材/研报吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.media.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'submit') {
                    var confirmIndex = layui.layer.confirm('确定送审该素材/研报吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.media.updateStatusForKf(data.id, 2);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'cancel') {
                    var confirmIndex = layui.layer.confirm('确定撤回该素材/研报吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.media.updateStatusForKf(data.id, 1);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('media', func);
});