﻿layui.define(['laytpl', 'form', 'request', 'common', 'kfCase'], function (exports) {
    var func = {
        /**
         * 查询客服加粉方案列表
         */
        query: function () {
            var name = layui.common.getUrlParam('name') || '-';
            layui.$('.user-name').text(name);
            var permissionType = layui.common.getUrlParam('permissionType') || 0;
            var objId = layui.common.getUrlParam('id') || '';
            layui.kfCase.getAll(function (res) {
                if (res.isSuccess) {
                    layui.request({
                        requestBase: 'resource',
                        method: 'post',
                        url: '/admin/workwx/user-kfcase-permission/get',
                        data: JSON.stringify({
                            permissionType: permissionType,
                            objId: objId
                        }),
                        headers: { 'Content-Type': 'application/json' }
                    }).then(function (perRes) {
                        if (perRes.isSuccess) {
                            if (perRes.result != null && perRes.result.isReadAll) {
                                layui.$('.all').prop('checked', true);
                            }
                            for (var i = 0; i < res.result.length; i++) {
                                for (var j = 0; j < res.result[i].kfCases.length; j++) {
                                    if (res.result[i].kfCases[j].source == 'kf') {
                                        res.result[i].kfCases[j].name = '【微信客服】' + res.result[i].kfCases[j].name;
                                    }
                                    else if (res.result[i].kfCases[j].source == 'mini') {
                                        res.result[i].kfCases[j].name = '【小程序】' + res.result[i].kfCases[j].name;
                                    }
                                    if (perRes.result != null) {
                                        if (!perRes.result.isReadAll) {
                                            res.result[i].kfCases[j].isChecked = perRes.result.kfCaseIds.find((ev) => {
                                                return ev === res.result[i].kfCases[j].id;
                                            });
                                        }
                                    }
                                    else {
                                        res.result[i].kfCases[j].isChecked = false;
                                    }
                                }
                            }
                            var getTpl = document.getElementById("kf-case-tpl").innerHTML
                                , view = document.getElementById('kf-case-view');
                            layui.laytpl(getTpl).render(res.result, function (html) {
                                view.innerHTML = html;
                            });
                            layui.form.render('checkbox');
                        }
                        else {
                            layui.common.alert(perRes.message, 0);
                        }
                    })
                }
            });
            layui.userKfCasePermission.bindEvent();
        },

        /**
         * 保存用户权限信息
         * @param {any} data
         */
        save: function (data) {
            var permissionType = layui.common.getUrlParam('permissionType') || 0;
            var objId = layui.common.getUrlParam('id') || '';
            var isReadAll = layui.$('input[name=all]').is(':checked');
            var kfCaseIds = [];
            if (!isReadAll) {
                var permissionList = layui.$('.kfcase:checked');
                if (permissionList.length > 0) {
                    for (var j = 0; j < permissionList.length; j++) {
                        kfCaseIds.push(permissionList.eq(j).val());
                    }
                }
            }
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/workwx/user-kfcase-permission/save',
                data: JSON.stringify({
                    objId: objId,
                    permissionType: permissionType,
                    isReadAll: isReadAll,
                    kfCaseIds: kfCaseIds
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("权限编辑成功");
                    setTimeout(function () {
                        parent.historyBack();
                    }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 绑定事件
        * @param {any} opType
        */
        bindEvent: function () {
            //提交保存
            layui.form.on('submit(permission-save-submit)', function (data) {
                layui.userKfCasePermission.save(data);
            });
            //返回上一页
            layui.form.on('submit(cancel)', function (data) {
                parent.historyBack();
            });
            layui.form.on('submit(product-permission)', function (data) {
                var objId = layui.common.getUrlParam('id') || '';
                var name = layui.common.getUrlParam('name') || '';
                var permissionType = layui.common.getUrlParam('permissionType') || 0;
                location.href = '../../resource/product-order/set-package-permission.html?permissionType=' + permissionType + '&id=' + objId + '&name=' + name;
            });
        },
    }

    exports('userKfCasePermission', func);
});