layui.define(['table', 'setter', 'common'], function (exports) {
    layui.table.request = function (requestBase, page, domId, path, contentType, cols, where, toolbar, height, subCols, doneFunc) {
        var baseUrl = '';
        if (requestBase == "sso") {
            baseUrl = layui.setter.request.ssoBaseUrl;
        }
        else if (requestBase == "form") {
            baseUrl = layui.setter.request.formBaseUrl;
        }
        else if (requestBase == "log") {
            baseUrl = layui.setter.request.logBaseUrl;
        }
        else if (requestBase == "pay") {
            baseUrl = layui.setter.request.payBaseUrl;
        }
        else {
            baseUrl = layui.setter.request.resourceBaseUrl
        }
        var headers = {};
        headers[layui.setter.request.tokenName] = layui.common.getCookie(layui.setter.request.tokenName);
        var orderToken = localStorage.getItem('x-order-token');
        if (orderToken!='') {
            headers['x-order-token'] = orderToken
        }
        var userStr = localStorage.getItem('ly-admin-user');
        if (userStr != null) {
            var userObj = JSON.parse(userStr);
            headers['uid'] = userObj.id;
        }
        var colsInfo = [cols];
        if (subCols) {
            colsInfo = [cols, subCols];
        }
        var table = layui.table;
        var conf = {
            elem: '#' + domId,
            url: baseUrl + path,
            id: domId,
            page: page,
            method: 'POST',
            loading: true,
            editTrigger: 'dblclick',
            cols: colsInfo,
            height: (height != null && height != '') || height > 0 ? height : 650,
            contentType: contentType,
            request: {
                pageName: 'pageIndex',
                limitName: 'pageSize'
            },
            where: where,
            limit: 20,
            limits: [10, 20, 30, 40, 50],
            headers: headers,
            parseData: function (res) {
                return {
                    "code": res.isSuccess ? 0 : res.code,
                    "msg": res.message,
                    "count": page ? (res.isSuccess ? res.result.totalCount : 0) : (res.result != null ? res.result.count : 0),
                    "data": page ? (res.isSuccess ? res.result.items : []) : res.result
                };
            },
            done: function (res, curr, count) {
                if (doneFunc != undefined && doneFunc != null) {
                    doneFunc(res, curr, count)
                }
            }
        }
        if (where) {
            conf.where = where;
        }
        if (toolbar) {
            conf.toolbar = toolbar;
            conf.defaultToolbar = [];
        }
        table.render(conf);
    }
    //输出 tableRequest 接口
    exports('tableRequest', layui.table);
});