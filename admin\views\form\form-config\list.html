﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键字</label>
                        <div class="layui-input-inline">
                            <input id="txb_keywords" name="keywords" placeholder="配置标题" type="text" class="layui-input" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">所属客户</label>
                        <div class="layui-input-inline">
                            <select name="customerId" id="customer-view">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">域名</label>
                        <div class="layui-input-inline">
                            <select name="domainName" id="domain-view" lay-search>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="form-config-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list form-config-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="form-config-list" lay-filter="list"></table>
                <script type="text/html" id="form-config-bar">
                    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="copy">复制</a>
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>
    <script id="customer-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.alias}}</option>
        {{#  }); }}
    </script>
    <script id="domain-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.domainName}}">
            {{# if(item.domainName!=''){}}
            {{item.domainName}}
            {{# }else{}}
            请选择域名
            {{# }}}
        </option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['common', 'formConfig'], function () {
            layui.formConfig.initCustomer();
            layui.formConfig.initDomain();
            layui.formConfig.query();
            //监听查询按钮
            layui.form.on('submit(form-config-search)', function (data) {
                var field = data.field;
                layui.tableRequest.reload("form-config-list", {
                    where: field
                });
            });
            layui.$('.form-config-create').click(function () {
                layui.common.openIframe('创建资源分配规则', 950, 600, 'create.html');
            });
        })
    </script>
</body>
</html>