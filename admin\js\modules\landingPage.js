﻿layui.define(['laytpl', 'form', 'request', 'tableRequest', 'common', 'company'], function (exports) {
    var func = {
        /**
        * 渲染企业下拉选框
        * */
        initCompany: function (id) {
            layui.company.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企业' });
                var getTpl = document.getElementById("company-tpl").innerHTML
                    , view = document.getElementById('company-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('company-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过id获取产品落地页信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                setTimeout(function () {
                    parent.location.reload();
                }, 3000);
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/product/landing-page/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=title]').val(res.result.title);
                    layui.$('input[name=realname]').val(res.result.realname);
                    layui.$('#url').val(res.result.url);
                    layui.$('#status').val(res.result.status);
                    layui.$('#tips').val(res.result.tips);
                    layui.form.render('select');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过企业id获取产品落地页列表
         * */
        getByCompanyId: function (companyId, callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/product/landing-page/by-companyid/get?companyId=' + companyId,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 获取产品落地页列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'landing-page-list', '/admin/product/landing-page/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                /* { field: 'companyName', title: '所属企业' },*/
                { field: 'title', title: '标题' },
                { field: 'realname', title: '真实姓名' },
                {
                    field: 'status', title: '状态', width: 80, templet: function (e) {
                        var statusDesc = '-';
                        if (e.status == 1) {
                            statusDesc = '正常';
                        }
                        else if (e.status == 2) {
                            statusDesc = '已下线';
                        }
                        return statusDesc;
                    }
                },
                {
                    field: 'auditStatus', title: '审核状态', templet: function (e) {
                        var statusDesc = '-';
                        if (e.auditStatus == 1) {
                            statusDesc = '审核中';
                        }
                        else if (e.auditStatus == 2) {
                            statusDesc = '通过';
                        }
                        else if (e.auditStatus == 3) {
                            statusDesc = '驳回';
                        }
                        if (e.auditRemark != null && e.auditRemark != '') {
                            statusDesc += '（' + e.auditRemark + '）'
                        }
                        return statusDesc;
                    }
                },
                { field: 'url', title: '地址' },
                {
                    field: 'auditTime', title: '审核时间', templet: function (e) {
                        if (e.auditTime != null) {
                            return layui.common.timeFormat(e.auditTime);
                        }
                        else {
                            return '-';
                        }
                    }
                },
                {
                    field: 'offlineTime', title: '停用时间', templet: function (e) {
                        if (e.status == 2) {
                            return layui.common.timeFormat(e.offlineTime);
                        }
                        else {
                            return '-';
                        }
                    }
                },
                {
                    field: 'createdAt', title: '上线时间', templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', align: 'left', width: 220, toolbar: '#landing-page-bar' }
            ]);
            //监听表格事件
            layui.landingPage.tableEvent();
        },
        /**
        * 创建产品产品落地页
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/landing-page/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("产品落地页创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑产品产品落地页
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/landing-page/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("产品落地页编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 编辑产品产品落地页状态
        * @param {any} data
        */
        updateStatus: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/landing-page/status/update',
                data: JSON.stringify({
                    id: data.field.id,
                    status: data.field.status,
                    tips: data.field.tips
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("产品落地页状态编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑产品产品落地页审核状态
         * @param {any} data
         */
        updateAuditStatus: function (data) {
            var id = layui.common.getUrlParam('id');
            data.field.id = id;
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/landing-page/audit-status/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("产品落地页审核成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除产品落地页
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/product/landing-page/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("产品落地页删除成功");
                    layui.landingPage.query();
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑落地页', 600, 370, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该产品落地页吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.landingPage.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'status') {
                    layui.common.openIframe('编辑落地页状态', 600, 300, 'update-status.html?id=' + data.id);
                }
                else if (obj.event === 'audit') {
                    layui.common.openIframe('审核落地页', 500, 300, 'audit.html?id=' + data.id);
                }
            });
        }
    }

    exports('landingPage', func);
});