﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        /* .layui-form-item .layui-input-inline { width: 300px; }*/
        .layui-fluid { padding: 0 !important }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <input id="userId" name="userId" type="hidden" />
                    <input id="startTime" name="startTime" type="hidden" />
                    <input id="endTime" name="endTime" type="hidden" />
                    <div class="layui-inline">
                        <label class="layui-form-label">加粉方案</label>
                        <div class="layui-input-inline">
                            <select name="kfCaseId" id="kf-case-view" lay-search>
                                <option value="">请选择加粉方案</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="微信昵称" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-inline">
                            <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="external-contact-byuserid-search">
                                <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="external-contact-byuserid-list" lay-filter="list"></table>
            </div>
        </div>
    </div>
    <script id="kf-case-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['externalContact'], function () {
            var workWxAppId = layui.common.getUrlParam('workWxAppId') || '';
            if (workWxAppId == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.$('#workWxAppId').val(workWxAppId);
            layui.externalContact.getKfCaseList(workWxAppId)
            layui.externalContact.queryByUserId(workWxAppId);
            layui.form.on('submit(external-contact-byuserid-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("external-contact-byuserid-list", {
                    where: field
                });
            });
        });
    </script>
</body>
</html>