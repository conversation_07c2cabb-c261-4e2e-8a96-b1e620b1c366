﻿layui.define(['laytpl', 'form', 'laydate', 'request', 'tableRequest', 'common', 'logApp'], function (exports) {
    var func = {
        /**
        * 渲染应用下拉选框
        * */
        initApp: function (id) {
            layui.logApp.getAll(function (res) {
                res.result.unshift({ appId: '', appName: '请选择应用' });
                var getTpl = document.getElementById("app-tpl").innerHTML
                    , view = document.getElementById('app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('app-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
        * 通过id获取业务日志信息
        * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.alertAutoClose("参数有误");
                setTimeout(function () { history.back(-1) }, 2000);
                return;
            }
            layui.request({
                method: 'post',
                requestBase: 'log',
                url: '/admin/business-log/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('#id').text(res.result.id);
                    layui.$('#appId').text(res.result.appId);
                    layui.$('#appName').text(res.result.appName);
                    layui.$('#platform').text(res.result.platform);
                    layui.$('#module').text(res.result.module);
                    layui.$('#type').text(res.result.type);
                    layui.$('#title').text(res.result.title);
                    layui.$('#opUserName').text(res.result.opUserName);
                    layui.$('#clientIP').text(res.result.clientIP);
                    layui.$('#opObjectId').text(res.result.opObjectId);
                    if (res.result.beforeOpContent != '') {
                        var beforeOpContentJson = JSON.stringify(JSON.parse(res.result.beforeOpContent), null, 2);
                        layui.$('#beforeOpContent').html(beforeOpContentJson);
                    }
                    if (res.result.afterOpContent != '') {
                        var afterOpContentJson = JSON.stringify(JSON.parse(res.result.afterOpContent), null, 2);
                        layui.$('#afterOpContent').html(afterOpContentJson);
                    }
                    layui.$('#createdAt').text(layui.common.timeFormat(res.result.createdAt));
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 获取业务日志列表
        * */
        query: function () {
            layui.tableRequest.request('log', true, 'business-log-list', '/admin/business-log/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'appName', title: '应用' },
                { field: 'platform', title: '操作平台' },
                { field: 'module', title: '操作模块' },
                { field: 'type', title: '操作类型' },
                { field: 'title', title: '日志标题' },
                { field: 'opUserName', title: '操作用户' },
                { field: 'clientIP', title: '客户端IP' },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 80, align: 'left', toolbar: '#business-log-bar' }
            ]);
            //监听表格事件
            layui.businessLog.tableEvent();
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    location.href = 'detail.html?id=' + data.id;
                }
            });
        },
        /**
         * 绑定事件
         * */
        bindEvent: function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            //监听查询按钮
            layui.form.on('submit(business-log-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("business-log-list", {
                    where: field
                });
            });
        }
    };
    exports('businessLog', func);
});