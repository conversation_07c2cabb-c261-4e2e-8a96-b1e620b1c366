﻿layui.define(['laytpl', 'form', 'request', 'tableRequest', 'common', 'customer'], function (exports) {
    var func = {
        /**
         * 获取所有域名列表
         * @param {any} callbackFunc
         */
        getAll: function (callbackFunc) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/domain-config/get-all',
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 渲染客户列表
         * @param {any} companyId
         * @param {any} selectedId
         */
        initCustomer: function (selectedId) {
            layui.customer.getAll(function (res) {
                res.result.unshift({ id: '', alias: '请选择客户' });
                var getTpl = document.getElementById("customer-tpl").innerHTML
                    , view = document.getElementById('customer-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (selectedId != undefined) {
                    view.value = selectedId;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过客户id查询域名列表
         * @param {any} customerId
         * @param {any} callbackFunc
         */
        queryByCustomerId: function (customerId, callbackFunc) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/domain-config/by-customerid/query?customerId=' + customerId,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 通过id获取域名配置信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                setTimeout(function () {
                    parent.location.reload();
                }, 3000);
                return;
            }
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/domain-config/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=domainName]').val(res.result.domainName);
                    layui.$('#copyRight').val(res.result.copyRight);
                    layui.domainConfig.initCustomer(res.result.customerId);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取域名配置列表
         * */
        query: function () {
            layui.tableRequest.request('form', true, 'domain-config-list', '/admin/basis/domain-config/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left', width: 80 },
                { field: 'customerName', title: '绑定客户' },
                { field: 'domainName', title: '域名', minWidth: 300 },
                { field: 'copyRight', title: '版权信息' },
                {
                    field: 'createdAt', title: '创建时间', templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', align: 'left', toolbar: '#domain-config-bar' }
            ]);
            //监听表格事件
            layui.domainConfig.tableEvent();
        },
        /**
        * 创建单个域名配置
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/domain-config/create',
                data: JSON.stringify({
                    customerId: data.field.customerId,
                    domainName: data.field.domainName,
                    copyRight: data.field.copyRight,
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("域名创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑域名配置信息
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/domain-config/update',
                data: JSON.stringify({
                    id: data.field.id,
                    customerId: data.field.customerId,
                    domainName: data.field.domainName,
                    copyRight: data.field.copyRight,
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("域名编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除域名配置信息
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/domain-config/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("域名删除成功");
                    layui.tableRequest.reload("domain-config-list");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑域名配置信息', 600, 380, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该配置吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.domainConfig.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('domainConfig', func);
});