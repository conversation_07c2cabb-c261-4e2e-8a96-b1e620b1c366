﻿layui.define(['laytpl', 'form', 'laydate', 'request', 'tableRequest', 'common', 'workWxApp', 'kfCase'], function (exports) {
    var func = {
        /**
        * 渲染应用下拉选框
        * */
        initApp: function (id) {
            layui.workWxApp.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企微' });
                var getTpl = document.getElementById("workwx-app-tpl").innerHTML
                    , view = document.getElementById('workwx-app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('workwx-app-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
        * 获取客服消息列表
        * */
        query: function () {
            layui.tableRequest.request('resource', true, 'kf-msg-list', '/admin/workwx/kf-msg/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'workWxAppName', title: '企微名称' },
                { field: 'kfCaseName', title: '加粉方案' },
                {
                    field: 'openKfId', title: '客服ID', templet: function (e) {
                        if (e.msgType == 'event' && e.eventInfo != null && (e.eventInfo.eventType == 'enter_session' || e.eventInfo.eventType == 'user_recall_msg')) {
                            return e.eventInfo.openKfId;
                        }
                        return e.openKfId;
                    }
                },
                {
                    field: 'externalUserID', title: '外部联系人ID', templet: function (e) {
                        if (e.msgType == 'event' && e.eventInfo != null && (e.eventInfo.eventType == 'enter_session' || e.eventInfo.eventType == 'user_recall_msg')) {
                            return e.eventInfo.externalUserID;
                        }
                        return e.externalUserID;
                    }
                },
                {
                    field: 'msgType', title: '消息类型', templet: function (e) {
                        if (e.msgType == 'event') {
                            return '事件消息';
                        }
                        else if (e.msgType == 'text') {
                            return '文本消息';
                        }
                        else if (e.msgType == 'image') {
                            return '图片消息';
                        }
                        else if (e.msgType == 'voice') {
                            return '语音消息';
                        }
                        else if (e.msgType == 'video') {
                            return '视频消息';
                        }
                        else if (e.msgType == 'file') {
                            return '文件消息';
                        }
                        else if (e.msgType == 'location') {
                            return '位置消息';
                        }
                        else if (e.msgType == 'link') {
                            return '链接消息';
                        }
                        else if (e.msgType == 'business_card') {
                            return '名片消息';
                        }
                        else if (e.msgType == 'miniprogram') {
                            return '小程序消息';
                        }
                        else if (e.msgType == 'msgmenu') {
                            return '菜单消息';
                        }
                        return '';
                    }
                },
                {
                    title: '消息内容', templet: function (e) {
                        if (e.msgType == 'event' && e.eventInfo != null) {
                            var msgContent = '';
                            if (e.eventInfo.eventType == 'enter_session') {
                                msgContent = '【事件】用户进入会话';
                            }
                            else if (e.eventInfo.eventType == 'msg_send_fail') {
                                msgContent = '【事件】消息发送失败[msgid:' + e.eventInfo.failMsgId + '，failType:' + e.eventInfo.failType + ']';
                            }
                            else if (e.eventInfo.eventType == 'servicer_status_change') {
                                msgContent = '【事件】接待人员接待状态变更';
                            }
                            else if (e.eventInfo.eventType == 'session_status_change') {
                                msgContent = '【事件】会话状态变更';
                            }
                            else if (e.eventInfo.eventType == 'user_recall_msg') {
                                msgContent = '【事件】用户撤回消息';
                            }
                            else if (e.eventInfo.eventType == 'servicer_recall_msg') {
                                msgContent = '【事件】接待人员撤回消息';
                            }
                            return '<span style="color:#ccc">' + msgContent + '</span>';
                        }
                        else if (e.msgType == 'text' && e.textInfo != null) {
                            return e.textInfo.content;
                        }
                        return '';
                    }
                },
                {
                    field: 'replyResult', title: '回复结果'
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 80, align: 'left', toolbar: '#kf-msg-bar' }
            ]);
            layui.kfMsg.tableEvent();
        },
        /**
         * 发送欢迎语
         * @param {any} data
         */
        send: function (data) {
            var msgId = layui.common.getUrlParam('id');
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-msg/send',
                data: JSON.stringify({
                    msgId: msgId,
                    kfCaseId: data.field.kfCaseId
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("发送完成");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 绑定事件
         * */
        bindEvent: function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            //监听查询按钮
            layui.form.on('submit(kf-msg-search)', function (data) {
                var field = data.field;
                if (field.callbackStatus == '') {
                    field.callbackStatus = -1;
                }
                if (field.reportStatus == '') {
                    field.reportStatus = -1;
                }
                //执行重载
                layui.tableRequest.reload("kf-msg-list", {
                    where: field
                });
            });
            //切换应用时加载加粉方案
            layui.form.on('select(workwx-app-id)', function (data) {
                layui.kfCase.queryByWorkWxAppId(data.value, function (res) {
                    if (res.result == null) {
                        res.result = [{ id: '', name: '请选择加粉方案' }]
                    }
                    else {
                        res.result.unshift({ id: '', name: '请选择加粉方案' });
                    }
                    var getTpl = document.getElementById("kf-case-tpl").innerHTML
                        , view = document.getElementById('kf-case-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.form.render('select');
                });
            });
        },
        /**
       * 监听表格单击事件
       * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'send') {
                    layui.common.openIframe('重发客服消息', 550, 300, 'send.html?id=' + data.id);
                }
            });
        }
    };
    exports('kfMsg', func);
});