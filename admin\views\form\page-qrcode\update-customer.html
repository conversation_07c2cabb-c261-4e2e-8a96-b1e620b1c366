﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <input id="id" name="id" type="hidden" />
        <input id="pageUrl" name="pageUrl" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">落地页标题</label>
            <div class="layui-input-inline">
                <input type="text" name="title" lay-verify="required" placeholder="请输入落地页标题" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">显示文案</label>
            <div class="layui-input-inline">
                <input type="text" name="showText" placeholder="请输入弹框显示文案" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-block">
                <label class="layui-form-label">二维码</label>
                <div class="layui-input-block">
                    <button type="button" class="layui-btn layui-btn-primary" id="uploadImg">
                        <i class="layui-icon">&#xe67c;</i>请上传一张二维码图片
                    </button>
                    <div class="layui-inline  uploadimg" id="uploadDemoView"><img height="50px" /></div>
                    <input id="qrCodeUrl" name="qrCodeUrl" type="hidden" />
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="page-qrcode-update-submit" value="确认修改">
            </div>
        </div>
    </div>
    <script id="customer-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.alias}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'uploadFile', 'pageQrCode'], function () {
            layui.pageQrCode.get('customer');
            //上传二维码
            layui.uploadFile('form', 'uploadImg', '/common/file/public/put?folder=form/page/qrcode', function (res) {
                if (res.isSuccess) {
                    layui.$("#qrCodeUrl").val(res.result);
                    layui.common.alertAutoClose("上传成功");
                    layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.result);
                }
                else {
                    layui.common.alert(res.message, 2);
                }
            });
            //监听提交事件
            layui.form.on('submit(page-qrcode-update-submit)', function (data) {
                layui.pageQrCode.updateByCustomer(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>