﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">

                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list app-update-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="app-update-list" lay-filter="list"></table>
                <script type="text/html" id="app-update-bar">
                    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="download">下载</a>
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.1"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'appUpdate'], function () {
            layui.appUpdate.query();

            //打开添加弹框
            layui.$('.app-update-create').click(function () {
                layui.common.openIframe('创建App更新版本', 500, 350, 'create.html');
            });
        });
    </script>
</body>
</html>