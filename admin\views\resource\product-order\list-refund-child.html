﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 200px; }
        .layui-fluid { padding: 0; }
        .layui-card-header { padding: 20px 0 10px 0 !important; }
        .layui-card-body { padding: 0 }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="姓名/产品/支付/订单号" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">退款状态</label>
                        <div class="layui-input-inline">
                            <select name="status" id="status">
                                <option value="">请选择退款状态</option>
                                <option value="1">未退款</option>
                                <option value="2">已提交申请</option>
                                <option value="3">退款成功</option>
                                <option value="4">退款失败</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">退款时间</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" autocomplete="off" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" autocomplete="off" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="refund-order-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="refund-order-list" lay-filter="refund-order-list"></table>
                <script type="text/html" id="refund-order-bar">
                    {{# if(layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.refund.audit')&&d.auditStatus==1){}}
                    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="financial-audit">审核</a>
                    {{#}}}
                    <a class="layui-btn layui-btn-xs" lay-event="read">查看协议</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="download">下载协议</a>
                </script>
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=2.3"></script>
    <script type="text/javascript">
        layui.use(['productOrder'], function () {
            layui.productOrder.queryRefundOrderForAudit();
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            layui.form.on('submit(refund-order-search)', function (data) {
                if (data.field.status == '') {
                    data.field.status = -1;
                }
                layui.tableRequest.reload("refund-order-list", {
                    where: data.field,
                    page: {
                        curr: 1
                    }
                });
            });
            layui.table.on('tool(refund-order-list)', function (obj) {
                var data = obj.data;
                if (obj.event == 'financial-audit') {
                    layui.common.openIframe('审核退款申请', 570, 580, 'audit-refund.html?id=' + data.id);
                }
                else if (obj.event == 'read') {
                    layui.productOrder.getRefundOrderContractUrl(data.id, 1);
                }
                else if (obj.event == 'download') {
                    layui.productOrder.getRefundOrderContractUrl(data.id, 2);
                }
            });
            if (!layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.copy')) {
                layui.$(document).bind("contextmenu", function () { return false; });
                layui.$(document).bind("selectstart", function () { return false; });
            }
        });
    </script>
</body>
</html>