﻿layui.define(['laytpl', 'form', 'request', 'tableRequest', 'common', 'uploadFile'], function (exports) {
    var func = {
        /**
         * 获取意见反馈列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'xetlive-list', '/admin/xet/live/query', 'application/json', [
                {
                    title: '封面', width: 100, templet: function (e) {
                        return '<a href="' + e.imgUrl + '" target="_blank"><img src="' + e.imgUrl + '" class="" height="35" /></a>';
                    }
                },
                { field: 'title', title: '直播标题' },
                {
                    title: '直播状态', width: 80, templet: function (e) {
                        if (e.aliveState == 0) {
                            return '未开始';
                        }
                        else if (e.aliveState == 1) {
                            return '直播中';
                        }
                        else if (e.aliveState == 2) {
                            return '已结束';
                        }
                        return '-';
                    }
                },
                {
                    title: '直播时间', templet: function (e) {
                        return layui.common.timeFormat(e.aliveStartAt) + '至' + layui.common.timeFormat(e.eliveStopAt);
                    }
                },
                { field: 'viewCount', width: 90, title: '观看人次' },
                { fixed: 'right', width: 80, title: '操作', align: 'left', toolbar: '#xetlive-bar' }
            ]);
            //监听表格事件
            layui.xetlive.tableEvent();
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event == 'play') {
                    var newWindow = window.open(data.pageUrl, '_blank');
                    if (!newWindow) {
                        layui.common.alertAutoClose("窗口被阻止");
                    }
                }
            });
        }
    }

    exports('xetlive', func);
});