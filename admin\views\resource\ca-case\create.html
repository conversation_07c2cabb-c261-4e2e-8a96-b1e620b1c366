﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 550px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">方案名称</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入企微加粉方案名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">所属企微</label>
            <div class="layui-input-inline">
                <select name="workWxAppId" id="workwx-app-view" lay-filter="workwx-app" lay-verify="required">
                    <option value="">请选择所属企微</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">使用场景</label>
            <div class="layui-input-inline">
                <select name="scene" id="scene" lay-filter="scene" lay-verify="required">
                    <option value="">请选择使用场景</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">承接用户</label>
            <div style="display:inline-block;width:400px;">
                <div class="layui-input-inline" id="workwx-user">
                </div>
                <div><a href="javascript:;" class="open-user-select" style="color:#0689b4;font-size:14px;">+按组织架构添加</a></div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">承接部门</label>
            <div class="layui-input-inline" id="workwx-department">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">回传配置</label>
            <div class="layui-input-inline">
                <input type="text" name="callbackConfig" placeholder="百度Token/回传配置" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">无效资源比例</label>
            <div class="layui-input-inline">
                <input type="text" name="invalidRate" lay-verify="required" placeholder="请输入无效资源比例（例:扣除5%填写0.05）" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="ca-case-submit" value="确认添加">
            </div>
        </div>
    </div>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        var xmSelUser, xmSelDepartment;
        layui.use(['caCase'], function () {
            layui.caCase.initApp();
            layui.caCase.bindEvent('create');

            //渲染使用场景
            layui.dic.query('workwx_scene', function (res) {
                var options = '<option value="">请选择使用场景</option>'
                if (res.isSuccess) {
                    for (var i = 0; i < res.result.length; i++) {
                        options += '<option value="' + res.result[i].key + '">' + res.result[i].value + '</option>';
                    }
                }
                layui.$('#scene').html(options);
                layui.form.render('select');
            });
            xmSelUser = layui.xmSelect.render({
                el: '#workwx-user',
                language: 'zn',
                filterable: true,
                tips: '请选择承接用户',
                theme: { color: '#0081ff ' },
                data: [],
                toolbar: { show: true },
                autoRow: true,
            });
            xmSelDepartment = layui.xmSelect.render({
                el: '#workwx-department',
                language: 'zn',
                filterable: true,
                tips: '请选择承接部门',
                theme: { color: '#0081ff ' },
                data: [],
                toolbar: { show: true },
                autoRow: true,
            });

            //接收选择框消息
            top.addEventListener('message', event => {
                xmSelUser.setValue(event.data);
            });
            //打开选择框
            layui.$('.open-user-select').click(function () {
                var workWxAppId = layui.$('#workwx-app-view').val();
                if (workWxAppId == '') {
                    layui.common.alertAutoClose('请选择所属企微');
                    return;
                }
                var users = xmSelUser.getValue();
                var userIds = [];
                if (users.length > 0) {
                    for (var i = 0; i < users.length; i++) {
                        userIds.push(users[i].value);
                    }
                }
                parent.layui.common.openIframe('选择员工', 890, 630, '../workwx-app/select-user.html?workWxAppId=' + workWxAppId + '&ids=' + userIds.toString());
            });
        });
    </script>
</body>
</html>