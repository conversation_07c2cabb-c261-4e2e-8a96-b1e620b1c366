﻿//config的设置是全局的
layui.config({
    base: '/admin/js/modules/', //存放拓展模块的根目录
    version: 20230329
}).extend({
    //自定义路径
    common: '{/}/admin/js/common',
    request: "{/}/admin/js/request",
    home: '{/}/admin/js/home',
    setter: '{/}/admin/js/config',
    admin: "{/}/admin/js/admin",
    view: "{/}/admin/js/view",
    uploadFile: "{/}/admin/js/uploadFile",
    tableRequest: "{/}/admin/js/tableRequest",
    axios: "{/}/lib/layui/axios",
    xmSelect: "{/}/lib/layui/lay/modules/xm-select",
    dropdown: "{/}/lib/layui/lay/modules/dropdown",
    opTable: "{/}/lib/layui/lay/modules/opTable"
}).use('home');