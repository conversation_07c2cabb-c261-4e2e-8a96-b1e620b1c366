﻿layui.define(['laytpl', 'laydate', 'tree', 'request', 'tableRequest', 'common', 'workWxApp', 'kfCase'], function (exports) {
    var myChart;
    var option;
    var func = {
        /**
         * 初始化加粉方案转化曲线图
         * */
        initChat: function () {
            var chartDom = document.getElementById('main');
            myChart = echarts.init(chartDom);
            option = {
                title: {
                    text: '加微数曲线图',
                },
                tooltip: {
                    trigger: 'axis'
                },
                grid: {
                    left: '0',
                    /*right: '20%',*/
                    bottom: '0',
                    containLabel: true
                },
                toolbox: {
                    feature: {
                        saveAsImage: {}
                    }
                },
                yAxis: {
                    type: 'value',
                    minInterval: 1
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: []
                },
                legend: {
                    data: [],
                    type: 'scroll',
                    orient: 'vertical',
                    right: 20,
                    top: 40,
                    //bottom: 20,
                },
                series: []
            };
            myChart.setOption(option);
            layui.externalContact.getChatStat();
        },
        /**
         * 渲染企微应用下拉选框
         * @param {any} id
         */
        initApp: function (id) {
            layui.workWxApp.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企微' });
                var getTpl = document.getElementById("workwx-app-tpl").innerHTML
                    , view = document.getElementById('workwx-app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('workwx-app-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过id获取外部联系人信息
         * @param {any} id
         * @param {any} callbackFunc
         */
        getById: function (id, callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/external-contact/get?id=' + id,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 通过条件搜索外部联系人列表
         * @param {any} workWxAppId
         * @param {any} keywords
         * @param {any} callbackFunc
         */
        search: function (workWxAppId, keywords, callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/external-contact/search',
                data: JSON.stringify({ workWxAppId: workWxAppId, keywords: keywords }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },

        /**
         * 绑定客服加粉方案下拉列表
         * @param {any} workWxAppId
         */
        getKfCaseList: function (workWxAppId) {
            layui.kfCase.queryByWorkWxAppId(workWxAppId, function (res) {
                if (res.result == null) {
                    res.result = [{ id: '', name: '请选择加粉方案' }]
                }
                else {
                    res.result.unshift({ id: '', name: '请选择加粉方案' });
                }
                var getTpl = document.getElementById("kf-case-tpl").innerHTML
                    , view = document.getElementById('kf-case-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('select');
            }, 'permission');
        },
        /**
         * 获取单个企微加粉统计生成记录
         */
        getStatRecord: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/follow-user/statistics/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=date]').val(layui.common.timeFormat(res.result.date).split(' ')[0]);
                    layui.$('#isWeekday').val(res.result.isWeekday.toString());
                    layui.$('input[name=teacherName]').val(res.result.teacherName);
                    layui.$('input[name=consumptionAmount]').val(res.result.consumptionAmount);
                    layui.$('input[name=followCount]').val(res.result.followCount);
                    layui.$('input[name=followCost]').val(res.result.followCost);
                    layui.$('input[name=distinctFollowCount]').val(res.result.distinctFollowCount);
                    layui.$('input[name=distinctFollowCost]').val(res.result.distinctFollowCost);
                    layui.$('input[name=liveDuration]').val(res.result.liveDuration);
                    layui.$('input[name=addedRate]').val(res.result.addedRate);
                    layui.$('input[name=assistantName]').val(res.result.assistantName);
                    layui.$('input[name=opendCountFor24Hours]').val(res.result.opendCountFor24Hours);
                    layui.$('input[name=distinctOpendCountFor24Hours]').val(res.result.distinctOpendCountFor24Hours);
                    layui.$('input[name=deletedCountFor24Hours]').val(res.result.deletedCountFor24Hours);
                    layui.$('input[name=distinctDeletedCountFor24Hours]').val(res.result.distinctDeletedCountFor24Hours);
                    layui.form.render('select');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过客服加粉方案id查询加微统计数
         * */
        getChatStat: function () {
            var kfCaseId = layui.common.getUrlParam('kfCaseId') || '';
            var from = layui.common.getUrlParam('from') || '';
            var data = { kfCaseId: kfCaseId, startTime: layui.$('#startTime').val(), endTime: layui.$('#endTime').val(), isAll: from == 'customer' ? false : true }
            layui.request({
                method: 'post',
                url: '/admin/workwx/external-contact/chat-statistics/get',
                data: JSON.stringify(data),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    var series = [];
                    if (res.result.series.length > 0) {
                        for (var i = 0; i < res.result.series.length; i++) {
                            series.push({
                                showSymbol: false,
                                name: res.result.series[i].name,
                                type: 'line',
                                smooth: true,
                                data: res.result.series[i].data
                            })
                        }
                    }
                    option.series = series;
                    option.legend.data = res.result.kfCaseNames;
                    option.xAxis.data = res.result.times
                    myChart.setOption(option, true);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            });
        },
        /**
         * 获取部门列表
         * @param {any} workWxAppId
         */
        getDepartmentTree: function (workWxAppId) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/department/tree/get?workWxAppId=' + workWxAppId,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tree.render({
                        elem: '#department-view'
                        , data: res.result
                        , onlyIconControl: true
                        , click: function (obj) {
                            layui.$(".layui-tree-set").removeClass('layui-tree-set-active');
                            obj.elem.addClass('layui-tree-set-active');
                            layui.$('#departmentId').val(obj.data.id);
                            var field = { workWxAppId: workWxAppId, keywords: layui.$('#keywords').val(), startTime: layui.$('#startTime').val(), endTime: layui.$('#endTime').val(), departmentId: layui.$('#departmentId').val() };
                            layui.tableRequest.reload("stat-user-list", {
                                where: field
                            });
                        }
                    });
                    if (res.result.length > 0) {
                        layui.$('.layui-tree .layui-tree-set').eq(0).addClass('layui-tree-set-active');
                        layui.$('#departmentId').val(res.result[0].id);
                        layui.externalContact.queryStatForUser(workWxAppId);
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取企业标签
         * */
        getCorpTag: function () {
            var id = layui.common.getUrlParam('id') || '';
            layui.request({
                method: 'post',
                url: '/admin/workwx/external-contact/corp-tag/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    var getTpl = document.getElementById("tag-tpl").innerHTML
                        , view = document.getElementById('tag-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.$(document).on('click', '.tag-btn', function () {
                        var that = layui.$(this);
                        if (that.hasClass('cur')) {
                            that.removeClass('cur');
                        }
                        else {
                            that.addClass('cur');
                        }
                    });
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取企微成员加微统计数列表
         * @param {any} workWxAppId
         */
        queryStatForUser: function (workWxAppId) {
            var companyId = layui.common.getUrlParam('companyId') || '';
            layui.tableRequest.request('resource', false, 'stat-user-list', '/admin/workwx/external-contact/statistics/for-user/query', 'application/json', [
                { field: 'name', title: '姓名' },
                {
                    field: 'totalCount', title: '加微总数', minWidth: 100, templet: function (e) {
                        if (e.totalCount > 0) {
                            var onclick = 'layui.common.openIframe(\'外部联系人列表\', 900, 666, \'list-byuserid.html?workWxAppId=' + workWxAppId + '&userId=' + e.userId + '\')';
                            return '<a href="javascript:;" class="link" onclick="' + onclick + '">' + e.totalCount + '</a>';
                        } else {
                            return e.totalCount
                        }
                    }
                },
                {
                    field: 'currentCount', title: '当前加微总数', minWidth: 120, templet: function (e) {
                        if (e.currentCount > 0) {
                            var onclick = 'layui.common.openIframe(\'外部联系人列表\', 900, 666, \'list-byuserid.html?workWxAppId=' + workWxAppId + '&userId=' + e.userId + '&startTime=' + layui.$('#startTime').val() + '&endTime=' + layui.$('#endTime').val() + '\')';
                            return '<a href="javascript:;" class="link" onclick="' + onclick + '">' + e.currentCount + '</a>';
                        } else {
                            return e.currentCount
                        }
                    }
                },
                { field: 'currentDistinctCount', title: '当前去重加微总数', minWidth: 120 },
                { field: 'currentOpenCount', title: '当前开口总数', minWidth: 120 },
                { field: 'currentDeleteCount', title: '当前删除总数', minWidth: 120 },
                { field: 'currentOrderSuccessCount', title: '成交数', minWidth: 80 },
                {
                    field: 'currentOrderSuccessRate', title: '开发率', minWidth: 80, templet: function (e) {
                        return e.currentOrderSuccessRate + '%';
                    }
                },
            ], { workWxAppId: workWxAppId, companyId: companyId, keywords: layui.$('#keywords').val(), startTime: layui.$('#startTime').val(), endTime: layui.$('#endTime').val(), departmentId: layui.$('#departmentId').val() });
        },
        /**
         * 获取企微加粉统计列表
         * */
        queryStat: function (workWxAppId) {
            var from = layui.common.getUrlParam('from') || '';
            var fields = [
                {
                    field: 'kfCaseName', title: '微信加粉方案名称', minWidth: 250, templet: function (e) {
                        if (e.source == 'kf') {
                            return '【微信客服】' + e.kfCaseName
                        }
                        else if (e.source == 'mini') {
                            return '【小程序】' + e.kfCaseName
                        }
                        else if (e.source == 'hkzs') {
                            return '【获客助手】' + e.kfCaseName
                        }
                        else if (e.source == 'sph') {
                            return '【视频号】' + e.kfCaseName
                        }
                        else {
                            return e.kfCaseName
                        }
                    }
                },
                {
                    field: 'totalCount', title: '企微加粉总数', minWidth: 120, templet: function (e) {
                        if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.workwx.stat.chat')) {
                            if (e.totalCount > 0 && e.kfCaseId != '') {
                                return '<a href="stat-chat.html?kfCaseId=' + e.kfCaseId + '&startTime=' + layui.$('#startTime').val() + '&endTime=' + layui.$('#endTime').val() + '&from=' + from + '" class="link">' + e.totalCount + '</a>';
                            } else {
                                return e.totalCount
                            }
                        }
                        else {
                            return e.totalCount
                        }
                    }
                }
            ];
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.stat.read-daqc')) {
                fields.push({ field: 'curTimeDeDuplicationCount', title: '当前去重后总数', minWidth: 130 });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.stat.read-qjqc')) {
                fields.push({ field: 'globalDeDuplicationCount', title: '全局去重后总数', minWidth: 130 });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.read-showqrcode')) {
                fields.push({
                    field: 'showQrCodeCount', title: '二维码展示次数', minWidth: 130, templet: function (e) {
                        return e.showQrCodeCount;
                    }
                });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.stat.read-kk')) {
                fields.push({ field: 'openCount', title: '24小时内开口总数', minWidth: 150 });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.stat.read-kkqc')) {
                fields.push({ field: 'afterDuplicationOpenCount', title: '24小时内全局去重开口总数', minWidth: 200 });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.stat.read-sc')) {
                fields.push({ field: 'deleteCount', title: '24小时内删除总数', minWidth: 150 });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.stat.read-scqc')) {
                fields.push({ field: 'afterDuplicationDeleteCount', title: '24小时内全局去重删除总数', minWidth: 200 });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.delrate.24')) {
                fields.push({
                    field: 'deletedRateFor24', title: '24小时删除率', minWidth: 150, templet: function (e) {
                        return e.deletedRateFor24 == 0 ? '-' : e.deletedRateFor24 + '%';
                    }
                });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.delrate.72')) {
                fields.push({
                    field: 'deletedRateFor72', title: '72小时删除率', minWidth: 150, templet: function (e) {
                        return e.deletedRateFor72 == 0 ? '-' : e.deletedRateFor72 + '%';
                    }
                });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.twoopenrate.24')) {
                fields.push({
                    field: 'twoOpendRateFor24', title: '24小时二次开口率', minWidth: 150, templet: function (e) {
                        return e.twoOpendRateFor24 == 0 ? '-' : e.twoOpendRateFor24 + '%';
                    }
                });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.twoopenrate.72')) {
                fields.push({
                    field: 'twoOpendRateFor72', title: '72小时二次开口率', minWidth: 150, templet: function (e) {
                        return e.twoOpendRateFor72 == 0 ? '-' : e.twoOpendRateFor72 + '%';
                    }
                });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.read-qcandsc')) {
                fields.push({
                    field: 'deDuplicationAndDelCount', title: '当前渠道重复且24小时内删除', minWidth: 200, templet: function (e) {
                        return e.deDuplicationAndDelCount;
                    }
                });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.read-qqdqcandsc')) {
                fields.push({
                    field: 'globalDeDuplicationAndDelCount', title: '全渠道重复且24小时内删除', minWidth: 200, templet: function (e) {
                        return e.globalDeDuplicationAndDelCount;
                    }
                });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.read-open3.48')) {
                fields.push({
                    field: 'openCountFor48Hour', title: '48小时开口三次', minWidth: 150, templet: function (e) {
                        return e.openCountFor48Hour;
                    }
                });
            }

            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.workwx.stat.kaifalv')) {
                fields.push({ field: 'doneCount', title: '成交数', minWidth: 80 });
                fields.push({
                    field: 'doneRate', title: '开发率', minWidth: 80, templet: function (e) {
                        return e.doneRate + '%';
                    }
                })
            }
            layui.tableRequest.request('resource', false, 'stat-list', '/admin/workwx/external-contact/statistics/query', 'application/json', fields, { workWxAppId: workWxAppId, startTime: layui.$('#startTime').val(), endTime: layui.$('#endTime').val(), isAll: from == 'customer' ? false : true }, null, null, null, function (resDone, curr, count) {
                layui.$('#stat-search').removeClass('layui-btn-disabled').removeAttr("disabled");
            });
        },
        /**
         * 获取企微加粉统计列表
         * */
        queryStatForSG: function (workWxAppId) {
            var fields = [
                { field: 'kfCaseName', title: '微信加粉方案名称', minWidth: 250 },
                {
                    field: 'totalCount', title: '企微加粉总数', minWidth: 120, templet: function (e) {
                        if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.workwx.stat.chat')) {
                            if (e.totalCount > 0 && e.kfCaseId != '') {
                                return '<a href="stat-chat.html?kfCaseId=' + e.kfCaseId + '&startTime=' + layui.$('#startTime').val() + '&endTime=' + layui.$('#endTime').val() + '" class="link">' + e.totalCount + '</a>';
                            } else {
                                return e.totalCount
                            }
                        }
                        else {
                            return e.totalCount
                        }
                    }
                }
            ];
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.stat.read-daqc')) {
                fields.push({ field: 'curTimeDeDuplicationCount', title: '当前去重后总数', minWidth: 130 });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.stat.read-qjqc')) {
                fields.push({ field: 'globalDeDuplicationCount', title: '全局去重后总数', minWidth: 130 });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.stat.read-kk')) {
                fields.push({ field: 'openCount', title: '24小时内开口总数', minWidth: 150 });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.stat.read-kkqc')) {
                fields.push({ field: 'afterDuplicationOpenCount', title: '24小时内全局去重开口总数', minWidth: 200 });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.stat.read-sc')) {
                fields.push({ field: 'deleteCount', title: '24小时内删除总数', minWidth: 150 });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.stat.read-scqc')) {
                fields.push({ field: 'afterDuplicationDeleteCount', title: '24小时内全局去重删除总数', minWidth: 200 });
            }
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.workwx.stat.kaifalv')) {
                fields.push({ field: 'doneCount', title: '成交数', minWidth: 80 });
                fields.push({
                    field: 'doneRate', title: '开发率', minWidth: 80, templet: function (e) {
                        return e.doneRate + '%';
                    }
                })
            }
            layui.tableRequest.request('resource', false, 'stat-list', '/admin/workwx/external-contact/statistics/for-sg/query', 'application/json', fields, { workWxAppId: workWxAppId, startTime: layui.$('#startTime').val(), endTime: layui.$('#endTime').val() });
        },
        /**
         * 获取企微加粉统计列表
         * */
        queryStatCustomize: function (workWxAppId) {
            var fields = [];
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.c-statistics.all.save')) {
                fields = [
                    { field: 'kfCaseName', title: '微信加粉方案名称', minWidth: 250 },
                    { field: 'totalCount', title: '企微加粉总数', minWidth: 120, edit: true },
                    { field: 'curTimeDeDuplicationCount', title: '当前去重后总数', minWidth: 130, edit: true },
                    { field: 'globalDeDuplicationCount', title: '全局去重后总数', minWidth: 130, edit: true },
                    { field: 'openCount', title: '24小时内开口总数', minWidth: 150, edit: true },
                    { field: 'afterDuplicationOpenCount', title: '24小时内全局去重开口总数', minWidth: 200, edit: true },
                    { field: 'deleteCount', title: '24小时内删除总数', minWidth: 150, edit: true },
                    { field: 'afterDuplicationDeleteCount', title: '24小时内全局去重删除总数', minWidth: 200, edit: true },
                    { field: 'virusRecourceCountFor24', title: '24小时病毒性资源数', minWidth: 130, edit: true },
                    { field: 'sameIndustryCount', title: '同行人员资源数', minWidth: 150, edit: true }
                ];
            }
            else {
                var isAuth = layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.c-statistics.save');
                fields = [
                    { field: 'kfCaseName', title: '微信加粉方案名称', minWidth: 250 },
                    {
                        field: 'totalCount', title: '企微加粉总数', minWidth: 120, templet: function (e) {
                            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.workwx.stat.chat')) {
                                if (e.totalCount > 0 && e.kfCaseId != '') {
                                    return '<a href="stat-chat.html?kfCaseId=' + e.kfCaseId + '&startTime=' + layui.$('#date').val() + ' 00:00:00' + '&endTime=' + layui.$('#date').val() + ' 23:59:59' + '" class="link">' + e.totalCount + '</a>';
                                } else {
                                    return e.totalCount
                                }
                            }
                            else {
                                return e.totalCount
                            }
                        }
                    },
                    { field: 'curTimeDeDuplicationCount', title: '当前去重后总数', minWidth: 130 },
                    { field: 'globalDeDuplicationCount', title: '全局去重后总数', minWidth: 130 },
                    { field: 'globalDeleteCountFor24', title: '24小时全局去重删除总数', minWidth: 130, edit: (isAuth ? true : false) },
                    { field: 'virusRecourceCountFor24', title: '24小时病毒性资源数', minWidth: 130, edit: (isAuth ? true : false) },
                    { field: 'sameIndustryCount', title: '同行人员资源数', minWidth: 150, edit: (isAuth ? true : false) }
                ];
            }

            layui.tableRequest.request('resource', false, 'stat-list', '/admin/workwx/external-contact/customize-statistics/query', 'application/json', fields, { workWxAppId: workWxAppId, date: layui.$('#date').val() });
        },
        /**
         * 查询企微加粉统计列表（生成统计数据页）
         * @param {any} workWxAppId
         */
        queryStatForUpdate: function (field) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/external-contact/statistics-update/query',
                data: JSON.stringify(field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    var statList = [];
                    var startDate = new Date(layui.$('#date').val());
                    var isWeekday = startDate.getDay() % 6 !== 0;
                    layui.$('#isWeekday').val(isWeekday ? 'true' : 'false');
                    if (res.result != null && res.result.length > 0) {
                        for (var i = 0; i < res.result.length; i++) {
                            res.result[i].kfCaseName = res.result[i].kfCaseName.replace(/抖音推广-/g, '').replace(/抖音直播-/g, '');
                            res.result[i].addRate = parseFloat((res.result[i].globalDeDuplicationCount / res.result[i].totalCount * 100).toFixed(2)) + '%';
                            if (res.result[i].consumptionAmount > 0) {
                                if (res.result[i].followCost == 0) {
                                    res.result[i].followCost = (res.result[i].consumptionAmount / res.result[i].totalCount).toFixed(2);
                                }
                                if (res.result[i].distinctFollowCost == 0) {
                                    res.result[i].distinctFollowCost = (res.result[i].consumptionAmount / res.result[i].globalDeDuplicationCount).toFixed(2);
                                }
                            }
                            statList.push(res.result[i]);
                        }
                    }
                    var getTpl = document.getElementById("stat-tpl").innerHTML
                        , view = document.getElementById('stat-view');
                    layui.laytpl(getTpl).render(statList, function (html) {
                        view.innerHTML = html;
                    });
                    layui.form.render('select');
                    //删除行
                    layui.$('.del-rows').click(function () {
                        layui.$(this).parent().parent('tr').remove();
                    });
                    //输入消费金额计算成本
                    layui.$('.del-rows').click(function () {
                        layui.$(this).parent().parent('tr').remove();
                    });
                    layui.$(".consumption").blur(function () {
                        var $this = layui.$(this);
                        if ($this.val() != '') {
                            var totalCount = parseInt($this.parent().parent('tr').find('td.total-count').text());
                            var globalDeDuplicationCount = parseInt($this.parent().parent('tr').find('td.global-deduplication-count').text());
                            var consumption = parseFloat($this.val());
                            var cost = consumption;
                            if (totalCount > 0) {
                                cost = consumption / totalCount;
                            }
                            var distinctCost = consumption;
                            if (globalDeDuplicationCount > 0) {
                                distinctCost = consumption / globalDeDuplicationCount;
                            }
                            $this.parent().parent('tr').find('td').find('span.cost').text(cost.toFixed(2));
                            $this.parent().parent('tr').find('td').find('span.distinct-cost').text(distinctCost.toFixed(2));
                        }
                        else {
                            $this.parent().parent('tr').find('td').find('span.cost').text('-');
                            $this.parent().parent('tr').find('td').find('span.distinct-cost').text('-');
                        }
                    });
                }
                else {
                    var getTpl = document.getElementById("stat-tpl").innerHTML
                        , view = document.getElementById('stat-view');
                    layui.laytpl(getTpl).render([], function (html) {
                        view.innerHTML = html;
                    });
                    //layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 获取外部联系人列表
        * */
        query: function () {
            layui.tableRequest.request('resource', true, 'external-contact-list', '/admin/workwx/external-contact/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'workWxAppName', title: '企微应用名称', minWidth: 120 },
                {
                    field: 'kfCaseName', title: '加粉方案', minWidth: 150, templet: function (e) {
                        return e.kfCaseName == '' ? '-' : e.kfCaseName;
                    }
                },
                { field: 'externalUserid', title: '外部联系人ID', minWidth: 180 },
                {
                    field: 'externalUserName', title: '外部联系人名称', minWidth: 180, templet: function (e) {
                        return '<a target="_blank" href="' + e.avatar + '"><img src="' + e.avatar + '" style="height:25px"/></a> ' + e.name;
                    }
                },
                {
                    width: 100, align: 'center', title: '备注', templet: function (e) {
                        var res = '-';
                        if (e.salesRemark != null)
                            res = e.salesRemark;
                        return res;
                    }
                },
                {
                    title: '标签', minWidth: 180, templet: function (e) {
                        var tags = '';
                        if (e.tags.length > 0) {
                            for (var i = 0; i < e.tags.length; i++) {
                                tags += '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary">' + e.tags[i] + '</button>';
                            }
                        }
                        return tags;
                    }
                },
                {
                    align: 'center', title: '性别', minWidth: 60, templet: function (e) {
                        var res = '-';
                        if (e.gender == 1)
                            res = '男';
                        else if (e.gender == 2)
                            res = '女';
                        return res;
                    }
                },
                { field: 'currentFollowUserId', title: '分配用户ID', minWidth: 130 },
                {
                    title: '是否开口', minWidth: 90, templet: function (e) {
                        return e.workWxOpend ? '是 [' + layui.common.timeFormat(e.opendTime) + ']' : '否';
                    }
                },
                {
                    title: '是否删除', minWidth: 90, templet: function (e) {
                        return e.workWxDeleted ? '是' + (e.deleteTime != null ? ' [' + layui.common.timeFormat(e.deleteTime) + ']' : '') : '否';
                    }
                },
                {
                    title: '添加来源', minWidth: 150, templet: function (e) {
                        var addWay = '-';
                        if (e.addWay == 0) {
                            addWay = '未知来源';
                        }
                        else if (e.addWay == 1) {
                            addWay = '扫描二维码';
                        }
                        else if (e.addWay == 2) {
                            addWay = '搜索手机号';
                        }
                        else if (e.addWay == 3) {
                            addWay = '名片分享';
                        }
                        else if (e.addWay == 4) {
                            addWay = '群聊';
                        }
                        else if (e.addWay == 5) {
                            addWay = '手机通讯录';
                        }
                        else if (e.addWay == 6) {
                            addWay = '微信联系人';
                        }
                        else if (e.addWay == 8) {
                            addWay = '安装第三方应用时自动添加的客服人员';
                        }
                        else if (e.addWay == 9) {
                            addWay = '搜索邮箱';
                        }
                        else if (e.addWay == 10) {
                            addWay = '视频号添加';
                        }
                        else if (e.addWay == 11) {
                            addWay = '通过日程参与人添加';
                        }
                        else if (e.addWay == 12) {
                            addWay = '通过会议参与人添加';
                        }
                        else if (e.addWay == 13) {
                            addWay = '添加微信好友对应的企业微信';
                        }
                        else if (e.addWay == 14) {
                            addWay = '通过智慧硬件专属客服添加';
                        }
                        else if (e.addWay == 15) {
                            addWay = '通过上门服务客服添加';
                        }
                        else if (e.addWay == 16) {
                            addWay = '通过获客链接添加';
                        }
                        else if (e.addWay == 17) {
                            addWay = '通过定制开发添加';
                        }
                        else if (e.addWay == 18) {
                            addWay = '通过需求回复添加';
                        }
                        else if (e.addWay == 21) {
                            addWay = '通过第三方售前客服添加';
                        }
                        else if (e.addWay == 22) {
                            addWay = '通过可能的商务伙伴添加';
                        }
                        else if (e.addWay == 24) {
                            addWay = '通过接受微信账号收到的好友申请添加';
                        }
                        else if (e.addWay == 201) {
                            addWay = '内部成员共享';
                        }
                        else if (e.addWay == 202) {
                            addWay = '管理员/负责人分配';
                        }
                        return addWay;
                    }
                },
                { field: 'totalKfCaseRepeatCount', title: '总添加次数', minWidth: 100 },
                { field: 'currentKfCaseRepeatCount', title: '当前方案添加次数', minWidth: 150 },
                {
                    field: 'createdAt', title: '创建时间', fixed: 'right', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                }
            ]);
        },
        /**
         * 通过客服加粉方案id分页查询外部联系人列表
         * */
        queryByKfCaseId: function () {
            var kfCaseId = layui.common.getUrlParam('kfCaseId') || '';
            var from = layui.common.getUrlParam('from') || '';
            var data = { kfCaseId: kfCaseId, startTime: layui.$('#startTime').val(), endTime: layui.$('#endTime').val(), isAll: from == 'customer' ? false : true }
            layui.tableRequest.request('resource', true, 'external-contact-bykfcaseid-list', '/admin/workwx/external-contact/by-kfcaseid/query', 'application/json', [
                {
                    field: 'externalUserName', fixed: 'left', title: '姓名', minWidth: 180, templet: function (e) {
                        return '<a target="_blank" href="' + e.avatar + '"><img src="' + e.avatar + '" style="height:25px"/></a> ' + e.name;
                    }
                },
                {
                    width: 100, align: 'center', title: '备注', templet: function (e) {
                        var res = '-';
                        if (e.salesRemark != null)
                            res = e.salesRemark;
                        return res;
                    }
                },
                {
                    align: 'center', title: '性别', width: 60, templet: function (e) {
                        var res = '-';
                        if (e.gender == 1)
                            res = '男';
                        else if (e.gender == 2)
                            res = '女';
                        return res;
                    }
                },
                {
                    title: '标签', minWidth: 180, templet: function (e) {
                        var tags = '';
                        if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.tag.update')) {
                            var onclick = 'layui.common.openIframe(\'编辑企业标签\', 700, 600, \'update-tag.html?id=' + e.id + '\')';
                            tags = '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary" onclick="' + onclick + '"><i class="layui-icon layui-icon-edit"></i></button>';
                        }
                        if (e.tags.length > 0) {
                            for (var i = 0; i < e.tags.length; i++) {
                                tags += '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary">' + e.tags[i] + '</button>';
                            }
                        }
                        return tags;
                    }
                },
                {
                    title: '是否开口', minWidth: 150, templet: function (e) {
                        return e.workWxOpend ? '是 [' + layui.common.timeFormat(e.opendTime) + ']' : '否';
                    }
                },
                {
                    title: '是否删除', minWidth: 150, templet: function (e) {
                        if (e.workWxDeleted == 0) {
                            return "否";
                        }
                        else if (e.workWxDeleted == 1) {
                            return '是 [' + layui.common.timeFormat(e.deleteTime) + ']';
                        }
                        else if (e.workWxDeleted == 2) {
                            return '继承 [' + layui.common.timeFormat(e.deleteTime) + ']';
                        }
                    }
                },
                { field: 'totalKfCaseRepeatCount', title: '总添加次数', width: 100 },
                { field: 'currentKfCaseRepeatCount', title: '当前方案添加次数', width: 150 },
                {
                    field: 'createdAt', title: '添加时间', fixed: 'right', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                }
            ], data);
        },
        /**
         * 通过企微成员id查询外部联系人列表
         * */
        queryByUserId: function (workWxAppId) {
            var userId = layui.common.getUrlParam('userId') || '';
            if (userId == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            var startTime = layui.common.getUrlParam('startTime') || '';
            var endTime = layui.common.getUrlParam('endTime') || '';

            layui.$('#userId').val(userId);
            layui.$('#startTime').val(startTime);
            layui.$('#endTime').val(endTime);
            layui.tableRequest.request('resource', true, 'external-contact-byuserid-list', '/admin/workwx/external-contact/by-userid/query', 'application/json', [

                {
                    field: 'externalUserName', title: '外部联系人名称', minWidth: 100, templet: function (e) {
                        return '<a target="_blank" href="' + e.avatar + '"><img src="' + e.avatar + '" style="height:25px"/></a> ' + e.name;
                    }
                },
                {
                    width: 100, align: 'center', title: '备注', templet: function (e) {
                        var res = '-';
                        if (e.salesRemark != null)
                            res = e.salesRemark;
                        return res;
                    }
                },
                {
                    align: 'center', title: '性别', width: 60, templet: function (e) {
                        var res = '-';
                        if (e.gender == 1)
                            res = '男';
                        else if (e.gender == 2)
                            res = '女';
                        return res;
                    }
                },
                {
                    field: 'kfCaseName', title: '加粉方案', minWidth: 150, templet: function (e) {
                        return e.kfCaseName == '' ? '-' : e.kfCaseName;
                    }
                },
                {
                    title: '是否开口', minWidth: 90, templet: function (e) {
                        return e.workWxOpend ? '是 [' + layui.common.timeFormat(e.opendTime) + ']' : '否';
                    }
                },
                {
                    title: '是否删除', minWidth: 90, templet: function (e) {
                        return e.workWxDeleted ? '是 [' + layui.common.timeFormat(e.deleteTime) + ']' : '否';
                    }
                },
                {
                    title: '成交状态', minWidth: 90, templet: function (e) {
                        if (e.doneType == 1) {
                            return '一开 [' + layui.common.timeFormat(e.oneDoneTime) + ']';
                        }
                        else if (e.doneType == 2) {
                            return '二开 [' + layui.common.timeFormat(e.twoDoneTime) + ']';
                        }
                        else {
                            return '未成交';
                        }
                    }
                },
                {
                    field: 'createdAt', title: '添加时间', fixed: 'right', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                }
            ], { workWxAppId: workWxAppId, userId: userId, startTime: startTime, endTime: endTime }, '', 500);
        },
        /**
         * 获取企微加粉统计生成记录列表
         * */
        queryStatRecord: function () {
            layui.tableRequest.request('resource', true, 'stat-record-list', '/admin/workwx/follow-user/statistics/query', 'application/json', [
                { field: 'workWxAppName', title: '所属企微', width: 100, },
                {
                    title: '【工作日】日期', width: 150, templet: function (e) {
                        if (e.id == null) {
                            return '';
                        }
                        else {
                            return '【' + (e.isWeekday ? '是' : '否') + '】' + layui.common.timeFormat(e.date).split(' ')[0];
                        }
                    }
                },
                { field: 'teacherName', title: '投资顾问', width: 100 },
                { field: 'consumptionAmount', title: '消费总金额', width: 100 },
                { field: 'followCount', title: '加微数量', width: 100 },
                { field: 'followCost', title: '加微成本', width: 100 },
                { field: 'distinctFollowCount', title: '去重加微数量', width: 120 },
                { field: 'distinctFollowCost', title: '去重加微成本', width: 120 },
                { field: 'liveDuration', title: '直播时长(小时)', width: 130 },
                {
                    field: 'addedRate', title: '新增率', width: 100, templet: function (e) {
                        return e.addedRate + '%';
                    }
                },
                {
                    field: 'openRate', title: '开口率', width: 100, templet: function (e) {
                        return e.openRate + '%';
                    }
                },
                {
                    field: 'deleteRateFor24Hours', title: '删除率(24h)', width: 105, templet: function (e) {
                        return e.deleteRateFor24Hours + '%';
                    }
                },
                {
                    field: 'deleteRateFor48Hours', title: '删除率(48h)', width: 105, templet: function (e) {
                        return e.deleteRateFor48Hours + '%';
                    }
                },
                { field: 'assistantName', title: '助播', width: 100 },
                { field: 'opendCountFor24Hours', title: '24小时内开口总数', width: 150 },
                { field: 'distinctOpendCountFor24Hours', title: '24小时全局去重开口总数', width: 190 },
                { field: 'deletedCountFor24Hours', title: '24小时删除总数', width: 150 },
                { field: 'distinctDeletedCountFor24Hours', title: '24小时全局去重删除总数', width: 190 },
                {
                    field: 'createdAt', title: '生成时间', width: 160, templet: function (e) {
                        if (e.id == null) {
                            return '';
                        }
                        else {
                            return layui.common.timeFormat(e.createdAt);
                        }
                    }
                },
                { fixed: 'right', title: '操作', width: 130, align: 'left', toolbar: '#stat-record-bar' }
            ], {}, '', 'full-150');
            //监听表格事件
            layui.externalContact.tableEvent();
        },
        /**
         * 编辑企微外部联系人标签
         * */
        updateCorpTag: function (data) {
            var id = layui.common.getUrlParam('id') || '';
            layui.request({
                method: 'post',
                url: '/admin/workwx/external-contact/corp-tag/update',
                data: JSON.stringify({ id: id, tags: data.field.tags }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.tableRequest.reload("external-contact-bykfcaseid-list");
                    parent.layui.common.alertAutoClose("保存成功");
                    parent.layui.common.closeType('iframe');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过id修改加微历史数据
         * @param {any} data
         */
        updateStatRecord: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/follow-user/statistics/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                if (res.isSuccess) {
                    parent.layui.tableRequest.reload("stat-record-list");
                    parent.layui.common.alertAutoClose("编辑成功");
                    parent.layui.common.closeType('iframe');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除企微加粉统计生成记录
         * @param {any} id
         */
        deleteStatRecord: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/follow-user/statistics/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("stat-record-list");
                    layui.common.alertAutoClose("删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 更新自定义统计数据
         * @param {any} workWxAppId
         * @param {any} kfCaseId
         * @param {any} type
         * @param {any} old
         * @param {any} obj
         */
        updateCustomizeStatistics: function (workWxAppId, kfCaseId, type, old, obj) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/external-contact/customize-statistics/save',
                data: JSON.stringify({
                    workWxAppId: workWxAppId,
                    type: type,
                    count: obj.value,
                    date: layui.$('#date').val(),
                    kfCaseId: kfCaseId
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("编辑成功");
                    if (obj.value < 0) {
                        setTimeout(function () {
                            layui.tableRequest.reload("stat-list");
                        }, 2000);
                    }
                }
                else {
                    if (type == 1) {
                        obj.update({ globalDeleteCountFor24: old, });
                    }
                    else if (type == 2) {
                        obj.update({ virusRecourceCountFor24: old, });
                    }
                    else if (type == 3) {
                        obj.update({ sameIndustryCount: old, });
                    }
                    else if (type == 10) {
                        obj.update({ totalCount: old, });
                    }
                    else if (type == 11) {
                        obj.update({ curTimeDeDuplicationCount: old, });
                    }
                    else if (type == 12) {
                        obj.update({ globalDeDuplicationCount: old, });
                    }
                    else if (type == 13) {
                        obj.update({ deleteCount: old, });
                    }
                    else if (type == 14) {
                        obj.update({ afterDuplicationDeleteCount: old, });
                    }
                    else if (type == 15) {
                        obj.update({ openCount: old, });
                    }
                    else if (type == 16) {
                        obj.update({ afterDuplicationOpenCount: old, });
                    }
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 保存数据统计信息
         * */
        saveStat: function () {
            var workWxAppId = layui.$('#workWxAppId').val();
            var dataTime = layui.$('#date').val();
            if (dataTime == '') {
                layui.common.alertAutoClose("请选择数据日期");
                return;
            }
            var isWeekday = layui.$('#isWeekday').val();
            if (isWeekday == '') {
                layui.common.alertAutoClose("请选择是否为工作日");
                return;
            }
            var data = {
                workWxAppId: workWxAppId,
                date: dataTime,
                isWeekday: isWeekday,
                kfCaseStatistics: []
            };
            var trs = layui.$('#stat-list tbody tr');
            if (trs.length == 0) {
                layui.common.alertAutoClose("暂无可保存的数据");
                return;
            }
            for (var i = 0; i < trs.length; i++) {
                var teacherName = trs.eq(i).find('td').eq(2).find('input').val();
                if (teacherName == '') {
                    layui.common.alertAutoClose('存在未填写的投资顾问名称');
                    return;
                }
                var consumptionAmount = trs.eq(i).find('td').eq(3).find('input').val();
                if (consumptionAmount == '') {
                    layui.common.alertAutoClose('存在未填写的消费金额');
                    return;
                }
                var liveDuration = trs.eq(i).find('td').eq(8).find('input').val();
                //if (liveDuration == '') {
                //    layui.common.alertAutoClose('存在未填写的直播时长');
                //    return;
                //}
                var assistantName = trs.eq(i).find('td').eq(10).find('input').val();
                //if (assistantName == '') {
                //    layui.common.alertAutoClose('存在未填写的助播名称');
                //    return;
                //}
                data.kfCaseStatistics.push({
                    kfCaseId: trs.eq(i).find('td').eq(0).text(),
                    kfCaseName: trs.eq(i).find('td').eq(1).text(),
                    teacherName: teacherName,
                    consumptionAmount: consumptionAmount,
                    followCount: trs.eq(i).find('td').eq(4).text(),
                    followCost: trs.eq(i).find('td').eq(5).text(),
                    distinctFollowCount: trs.eq(i).find('td').eq(6).text(),
                    distinctFollowCost: trs.eq(i).find('td').eq(7).text(),
                    liveDuration: liveDuration,
                    addedRate: trs.eq(i).find('td').eq(9).text().replace('%', ''),
                    assistantName: assistantName,
                    opendCountFor24Hours: trs.eq(i).find('td').eq(11).text(),
                    distinctOpendCountFor24Hours: trs.eq(i).find('td').eq(12).text(),
                    deletedCountFor24Hours: trs.eq(i).find('td').eq(13).text(),
                    distinctDeletedCountFor24Hours: trs.eq(i).find('td').eq(14).text()
                });
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/follow-user/statistics/save',
                data: JSON.stringify(data),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.externalContact.queryStatForUpdate({ workWxAppId: workWxAppId, date: dataTime, kfCaseId: layui.$('#kf-case-view').val() });
                    layui.common.alertAutoClose('数据保存成功');
                    //var confirmIndex = layui.layer.confirm('保存数据成功，是否立即导出？', {
                    //    icon: 3,
                    //    title: '提示',
                    //    btn: ['确定', '取消']
                    //}, function () {
                    //    layui.externalContact.exportStat(res.result);
                    //}, function () {
                    //    layui.layer.close(confirmIndex);
                    //});
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            });
        },

        /**
        * 通过加粉方案id导出外部联系人
        * */
        exportByKfCaseId: function (data) {
            var kfCaseId = layui.common.getUrlParam('kfCaseId') || '';
            var from = layui.common.getUrlParam('from') || '';
            if (from == 'customer') {
                data.field.isAll = false;
            }
            data.field.kfCaseId = kfCaseId;
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/external-contact/by-kfcaseid/export',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                if (res.isSuccess) {
                    location.href = res.result;
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 导出统计数据
         * */
        exportStat: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/follow-user/statistics/export',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                if (res.isSuccess) {
                    location.href = res.result;
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 同步部门
         * @param {any} workWxAppId
         */
        syncDepartment: function (workWxAppId) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/department/sync?workWxAppId=' + workWxAppId,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose('同步企微部门成功');
                    layui.externalContact.getDepartmentTree(workWxAppId);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 同步企微部门成员
         * @param {any} workWxAppId
         */
        syncDepartmentUser: function (workWxAppId) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/department-user/sync?workWxAppId=' + workWxAppId,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose('同步部门成员成功');
                    layui.tableRequest.reload("stat-user-list");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 绑定事件
        * */
        bindEvent: function (page) {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            //监听查询按钮
            layui.form.on('submit(external-contact-search)', function (data) {
                var field = data.field;
                if (field.workWxDeleted == '') {
                    field.workWxDeleted = -1;
                }
                if (field.workWxOpend == '') {
                    field.workWxOpend = -1;
                }
                //执行重载
                layui.tableRequest.reload("external-contact-list", {
                    where: field
                });
            });
            //切换企微应用时加载加粉方案
            layui.form.on('select(workwx-app-id)', function (data) {
                layui.kfCase.queryByWorkWxAppId(data.value, function (res) {
                    if (res.result == null) {
                        res.result = [{ id: '', name: '请选择加粉方案' }]
                    }
                    else {
                        res.result.unshift({ id: '', name: '请选择加粉方案' });
                    }
                    var getTpl = document.getElementById("kf-case-tpl").innerHTML
                        , view = document.getElementById('kf-case-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.form.render('select');
                });
            });

            //监听查询按钮
            layui.form.on('submit(stat-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("stat-list", {
                    where: field
                });
            });
            //监听统计数据修改页查询按钮
            layui.form.on('submit(stat-update-search)', function (data) {
                layui.externalContact.queryStatForUpdate(data.field);
            });
            //监听保存统计数据按钮
            layui.form.on('submit(stat-save-submit)', function (data) {
                layui.externalContact.saveStat();
            });
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    location.href = 'stat-record-detail.html?id=' + data.id;
                }
                else if (obj.event === 'down') {
                    var confirmIndex = layui.layer.confirm('确认导出当前数据吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.externalContact.exportStat(data.id);
                        layui.layer.close(confirmIndex);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'edit') {
                    layui.common.openIframe('编辑加微数据', 870, 520, 'stat-update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该数据吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.externalContact.deleteStatRecord(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('externalContact', func);
});