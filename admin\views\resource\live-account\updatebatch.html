<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 40px; }
        .layui-form-item .layui-input-inline { width: 200px; }
    </style>
</head>

<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list"
         style="padding: 0px 20px 0 0;">
        <div class="layui-form layui-card-header layuiadmin-card-header-auto">
            <div class="layui-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline">
                    <select name="status" id="status">
                        <option value="1">使用</option>
                        <option value="3">停用</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="margin-top: 20px;">
                <label class="layui-form-label"></label>
                <div class="layui-input-inline">
                    <input type="button" lay-submit="" class="layui-btn layui-btn-normal"
                           lay-filter="plan-create-submit" value="确认修改">
                </div>
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=2.2"></script>
    <script type="text/javascript">
        layui.use(['form', 'laydate', 'liveAccount'], function () {
            layui.form.on('submit(plan-create-submit)', function (data) {
                var ids = layui.common.getUrlParam('id').split(",");
                data.field.ids = ids;
                layui.liveAccount.batchUpdate(data);
                return false; //阻止表单跳转
            });
        });
    </script>
</body>
</html>