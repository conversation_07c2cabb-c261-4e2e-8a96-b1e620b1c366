﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 120px; }
        .layui-form-item .layui-input-inline { width: 400px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">登录账号</label>
            <div class="layui-input-inline">
                <input type="text" name="account" lay-verify="required" placeholder="请输入登录账号（手机号）" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">姓名</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入用户姓名" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">登录密码</label>
            <div class="layui-input-inline">
                <input type="password" name="password" lay-verify="required" placeholder="请输入登录密码" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item" id="company-item">
            <label class="layui-form-label">所属企业</label>
            <div class="layui-input-inline">
                <select name="companyId" id="company-view" lay-filter="company-view" lay-verify="required"></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">所属部门</label>
            <div class="layui-input-inline">
                <select name="departmentId" id="department-view" lay-verify="required" lay-search>
                    <option value="">请选择所属部门</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">上级领导</label>
            <div class="layui-input-inline">
                <select name="parentId" id="parent-user-view" lay-search>
                    <option value="">请选择所属上级</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item" id="isSuperAdmin-item" style="display:none">
            <label class="layui-form-label">账号类型</label>
            <div class="layui-input-inline">
                <select name="isSuperAdmin" id="isSuperAdmin">
                    <option value="false">普通账号</option>
                    <option value="true">超管账号</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">企微账号</label>
            <div class="layui-input-inline" id="workwx-user">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">角色</label>
            <div class="layui-input-inline" id="role-list-view">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="admin-user-create-submit" value="确认添加">
            </div>
        </div>
    </div>
    <script id="company-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}" data-corpid="{{item.corpid}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="role-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <input type="checkbox" name="role" value="{{item.id}}" lay-skin="primary" title="{{item.name}}">
        {{#  }); }}
    </script>
    <script id="parent-user-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="department-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/lib/jsencrypt/jsencrypt.min.js"></script>
    <script src="/admin/js/global.js?v=2.3"></script>
    <script type="text/javascript">
        var xmSel;
        layui.use(['form', 'adminUser'], function () {
            var companyId = layui.common.getUrlParam('companyId') || '';
            layui.adminUser.initCompany(companyId, 'create');
            layui.adminUser.bindEvent();
            //监听提交事件
            layui.form.on('submit(admin-user-create-submit)', function (data) {
                var roleIds = [];
                layui.$(":checkbox[name=role]:checked").each(function () {
                    roleIds.push(layui.$(this).val());
                });
                data.field.roleIds = roleIds;
                data.field.workUserId = '';
                var workUserIds = xmSel.getValue('value');
                if (workUserIds.length > 0) {
                    data.field.workUserId = workUserIds.toString();
                }
                layui.adminUser.create(data);
                return false; //阻止表单跳转
            });
            xmSel = layui.xmSelect.render({
                el: '#workwx-user',
                language: 'zn',
                filterable: true,
                tips: '请选择绑定的企微账号',
                theme: { color: '#0081ff ' },
                data: [],
                toolbar: { show: true },
                autoRow: true,
            });
            var aUserStr = localStorage.getItem('ly-admin-user');
            if (aUserStr != null) {
                var aUserObj = JSON.parse(aUserStr);
                if (aUserObj.isSuperAdmin) {
                    layui.$('#isSuperAdmin-item').show();
                }
            }
        })
    </script>
</body>
</html>