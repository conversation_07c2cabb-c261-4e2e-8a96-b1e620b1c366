﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 80px; }
        .layui-form-item .layui-input-inline { width: 400px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list">
        <input id="productId" name="productId" type="hidden" />
        <div style="float:left;">
            <div class="layui-form-item" style="margin-top: 10px;">
                <div class="layui-inline">
                    <label class="layui-form-label">产品：</label>
                    <div class="layui-input-inline">
                        <label class="layui-form-mid layui-word-aux" id="lab_product"></label>
                    </div>
                </div>
            </div>
            <div class="layui-form-item auth-item" style="display:none;">
                <div class="layui-inline">
                    <label class="layui-form-label">落地页：</label>
                    <div class="layui-input-inline">
                        <select name="h5Url" id="landing-page-view" lay-search>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">渠道：</label>
                    <div class="layui-input-inline">
                        <select name="channelKey" id="product-channel-view" lay-verify="required" lay-filter="product-channel" lay-search>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item" id="admin-user-item">
                <div class="layui-inline">
                    <label class="layui-form-label">所属用户：</label>
                    <div class="layui-input-inline">
                        <select name="saleUserId" id="admin-user-view" lay-search>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item auth-item" style="display:none;">
                <div class="layui-inline">
                    <label class="layui-form-label">客服：</label>
                    <div class="layui-input-inline">
                        <select name="isShowKf" lay-verify="required">
                            <option value="false" selected="selected">支付完成不添加客服助理</option>
                            <option value="true">支付完成添加客服助理</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">优惠活动：</label>
                    <div class="layui-input-inline">
                        <select name="activityId" id="activity-view">
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item" id="buyUrl" style="display:none">
                <div class="layui-inline">
                    <label class="layui-form-label">地址：</label>
                    <div class="layui-input-inline">
                        <label class="layui-form-mid layui-word-aux" id="lab_url" style="width: 350px; word-break: break-all"></label>
                    </div>
                </div>
            </div>
        </div>
        <div style="float: left; margin-top: 85px">
            <img style="width: 200px;" id="erwm" />
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="gen-url-submit" value="生成链接">
                <input type="button" lay-submit="" class="layui-btn layui-btn-primary" lay-filter="copy-url-submit" value="复制链接">
            </div>
        </div>
    </div>
    <script id="landing-page-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.url}}">{{item.title}}</option>
        {{#  }); }}
    </script>
    <script id="product-channel-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.key}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="activity-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">
            {{item.name}}
            -应付:{{item.payableAmount}}
            {{#if(item.id!=''){}}
            ({{layui.common.timeFormat(item.endTime)}}到期)
            {{# } }}
        </option>
        {{#  }); }}
    </script>
    <script id="admin-user-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">
            {{item.name}}
            {{#if(item.account!=''){}}
            （{{item.account}}）
            {{# }else{ }}
            {{item.account}}
            {{# } }}
        </option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=2.2"></script>
    <script type="text/javascript">
        layui.use(['product'], function () {
            layui.product.initProductChannel();
            //layui.product.initAdminUser();
            layui.product.getBrief();
            layui.product.initAdminUser();
            layui.product.bindEvent('gen');
        })
    </script>
</body>
</html>