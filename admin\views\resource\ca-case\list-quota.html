﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属企微</label>
                        <div class="layui-input-inline">
                            <select name="workWxAppId" id="workwx-app-view" lay-verify="required">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="ca-case-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-mid layui-word-aux" id="quotaDesc"></div>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table class="layui-table" lay-skin="line">
                    <thead>
                        <tr>
                            <th>剩余额度</th>
                            <th>过期时间</th>
                        </tr>
                    </thead>
                    <tbody id="quota-view">
                        <tr>
                            <td colspan="2" style="text-align:center;">请选择企微</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <script id="quota-tpl" type="text/html">
        {{# if(d.length==0){ }}
        <tr><td colspan="2" style="text-align:center;">暂无数据</td></tr>
        {{# } else { }}
        {{#  layui.each(d, function(index, item){ }}
        <tr>
            <td>{{item.balance}}</td>
            <td>{{layui.common.timeFormat(item.expireDate)}}</td>
        </tr>
        {{#  }); }}
        {{# } }}
    </script>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['common', 'caCase'], function () {
            layui.caCase.initApp();
            //监听查询按钮
            layui.form.on('submit(ca-case-search)', function (data) {
                layui.caCase.getQuota();
            });
        })
    </script>
</body>
</html>