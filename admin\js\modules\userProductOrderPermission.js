﻿layui.define(['laytpl', 'form', 'request', 'common', 'productPackage', 'product'], function (exports) {
    var func = {
        /**
         * 查询产品包列表
         */
        query: function () {
            var name = layui.common.getUrlParam('name') || '-';
            var permissionType = layui.common.getUrlParam('permissionType') || 0;
            var objId = layui.common.getUrlParam('id') || '';
            layui.$('.user-name').text(name);
            layui.productPackage.getAll(function (res) {
                if (res.isSuccess) {
                    layui.request({
                        requestBase: 'resource',
                        method: 'post',
                        url: '/admin/product/order-permission/get',
                        data: JSON.stringify({
                            type: 1,
                            permissionType: permissionType,
                            objId: objId
                        }),
                        headers: { 'Content-Type': 'application/json' },
                    }).then(function (perRes) {
                        if (perRes.isSuccess) {
                            for (var i = 0; i < res.result.length; i++) {
                                res.result[i].teachers = [];
                                for (var j = 0; j < res.result[i].teacherNames.length; j++) {
                                    var isChecked = false;
                                    if (perRes.result != null && perRes.result.items != null) {
                                        isChecked = perRes.result.items.find((ev) => {
                                            return ev.id == res.result[i].id && ev.teacherNames.indexOf(res.result[i].teacherNames[j]) > -1;
                                        });
                                    }
                                    res.result[i].teachers.push({ id: res.result[i].teacherNames[j], name: res.result[i].teacherNames[j], isChecked: isChecked });
                                }
                            }
                            var getTpl = document.getElementById("package-tpl").innerHTML
                                , view = document.getElementById('package-view');
                            layui.laytpl(getTpl).render(res.result, function (html) {
                                view.innerHTML = html;
                            });
                            layui.form.render('checkbox');
                        }
                        else {
                            layui.common.alert(perRes.message, 0);
                        }
                    })
                }
            });
            layui.userProductOrderPermission.bindEvent(1, permissionType, objId);
        },
        /**
        * 查询产品列表
        */
        queryForProduct: function () {
            var name = layui.common.getUrlParam('name') || '-';
            var permissionType = layui.common.getUrlParam('permissionType') || '';
            var objId = layui.common.getUrlParam('id') || '';
            layui.$('.user-name').text(name);
            layui.product.getAll(function (res) {
                if (res.isSuccess) {
                    layui.request({
                        requestBase: 'resource',
                        method: 'post',
                        url: '/admin/product/order-permission/get',
                        data: JSON.stringify({
                            type: 2,
                            permissionType: permissionType,
                            objId: objId
                        }),
                        headers: { 'Content-Type': 'application/json' },
                    }).then(function (perRes) {
                        if (perRes.isSuccess) {
                            for (var i = 0; i < res.result.length; i++) {
                                res.result[i].teachers = [];
                                for (var j = 0; j < res.result[i].teacherNames.length; j++) {
                                    var isChecked = false;
                                    if (perRes.result != null && perRes.result.items != null) {
                                        isChecked = perRes.result.items.find((ev) => {
                                            return ev.id == res.result[i].id && ev.teacherNames.indexOf(res.result[i].teacherNames[j]) > -1;
                                        });
                                    }
                                    res.result[i].teachers.push({ id: res.result[i].teacherNames[j], name: res.result[i].teacherNames[j], isChecked: isChecked });
                                }
                            }
                            var getTpl = document.getElementById("package-tpl").innerHTML
                                , view = document.getElementById('package-view');
                            layui.laytpl(getTpl).render(res.result, function (html) {
                                view.innerHTML = html;
                            });
                            layui.form.render('checkbox');
                        }
                        else {
                            layui.common.alert(perRes.message, 0);
                        }
                    })
                }
            });
            layui.userProductOrderPermission.bindEvent(2, permissionType, objId);
        },

        /**
         * 保存用户权限信息
         * @param {any} data
         */
        save: function (data, type, permissionType, objId) {
            var userId = layui.common.getUrlParam('userId') || '';
            var packageItems = layui.$('.package-item');
            var items = [];
            for (var i = 0; i < packageItems.length; i++) {
                var packageId = packageItems.eq(i).data('id');
                var teacherNames = [];
                var teacherItems = packageItems.eq(i).find('td .teacherName:checked');
                for (var j = 0; j < teacherItems.length; j++) {
                    teacherNames.push(teacherItems.eq(j).val());
                }
                if (teacherNames.length > 0) {
                    items.push({ id: packageId, teacherNames: teacherNames });
                }
            }
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/product/order-permission/save',
                data: JSON.stringify({
                    type: type,
                    permissionType: permissionType,
                    objId: objId,
                    items: items
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("权限编辑成功");
                    setTimeout(function () {
                        parent.historyBack();
                    }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 绑定事件
        * @param {any} opType
        */
        bindEvent: function (type, permissionType, objId) {
            //提交保存
            layui.form.on('submit(permission-save-submit)', function (data) {
                layui.userProductOrderPermission.save(data, type, permissionType, objId);
            });
            //返回上一页
            layui.form.on('submit(cancel)', function (data) {
                parent.historyBack();
            });
            //跳转企微数据权限配置页
            layui.form.on('submit(workwx-permission)', function (data) {
                var name = layui.common.getUrlParam('name') || '';
                location.href = '../../resource/external-contact/set-permission.html?permissionType=' + permissionType + '&id=' + objId + '&name=' + name;
            });
        },
    }

    exports('userProductOrderPermission', func);
});