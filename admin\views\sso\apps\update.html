﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="app-update-view" style="padding: 20px 30px 0 0;">
    </div>
    <script id="app-update-tpl" type="text/html">
        <input id="id" name="id" value="{{d.id}}" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">应用名称</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入应用名称" value="{{d.name}}" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">主页地址</label>
            <div class="layui-input-inline">
                <input type="text" name="homePage" placeholder="请输入主页地址" value="{{d.homePage}}" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-inline">
                <select name="status" id="status" lay-verify="required">
                    <option value="">请选择状态</option>
                    <option value="1" {{#if(d.status==1){}} selected="selected" {{#  } }}>正常</option>
                    <option value="2" {{#if(d.status==2){}} selected="selected" {{#  } }}>冻结</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="app-update-submit" value="确认修改">
            </div>
        </div>
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'app'], function () {
            layui.app.get();
            layui.form.on('submit(app-update-submit)', function (data) {
                layui.app.update(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>