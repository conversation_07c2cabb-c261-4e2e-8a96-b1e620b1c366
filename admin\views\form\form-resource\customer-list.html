﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">分配用户</label>
                        <div class="layui-input-inline">
                            <select name="saleUserId" id="sale-user-view">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="关键词/姓名/手机号/代码" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">创建日期</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="form-resource-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="form-resource-list" lay-filter="list"></table>
                <script type="text/html" id="form-resource-bar">
                    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit-followup">跟进情况</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>
    <script type="text/html" id="topToolBar">
        <div class="layui-btn-container">
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="batch-del">批量删除</button>
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="export-form">导出表单资源</button>
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="batch-updateuserid">重新分配</button>
        </div>
    </script>
    <div class="layui-form" id="update-user-dialog" style="padding: 20px 0 0 30px; width: 350px;display:none;">
        <input id="resourceIds" name="resourceIds" value="" type="hidden" />
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">分配用户：</label>
                <div class="layui-input-inline">
                    <select name="userId" id="sale-user-2-view" lay-search>
                    </select>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="update-saleruserid-submit" value="确认分配">
            </div>
        </div>
    </div>

    <audio controls="controls" id="bgMusic" style="display:none;">
        <source src="../../../lib/notification.wav" />
    </audio>
    <script id="sale-user-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="sale-user-2-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">
            {{item.name}}
            {{#if(item.account!=''){}}
            （{{item.account}}）
            {{# }else{ }}
            {{item.account}}
            {{# } }}
        </option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.8"></script>
    <script type="text/javascript">
        layui.use(['common', 'formAdminUser', 'formResource'], function () {
            layui.formResource.initSaleUser();
            layui.formResource.customerQuery();
            layui.formResource.bindEvent();

            ////消息通知权限获取
            //Notification.requestPermission().then(function (result) {
            //    if (result === 'denied') {
            //        //layui.common.alertAutoClose('已设置禁止接收新消息通知,如需接收通知，请设置为允许');
            //    }
            //});
            //定时获取是否有新资源进线
            setInterval(function () {
                layui.formResource.getUnReadCount();
            }, 20000);

        })
    </script>
</body>
</html>