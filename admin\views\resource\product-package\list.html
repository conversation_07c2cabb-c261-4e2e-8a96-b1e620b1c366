﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 200px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="产品包名称" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">产品</label>
                        <div class="layui-input-inline" style="width:300px;">
                            <select name="productId" id="product-view" lay-search>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="product-package-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list product-package-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="product-package-list" lay-filter="list"></table>
                <script type="text/html" id="product-package-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>
    <script id="product-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'productPackage'], function () {
            layui.productPackage.initProductForList();
            layui.productPackage.query();
            //监听查询按钮
            layui.form.on('submit(product-package-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("product-package-list", {
                    where: field
                });
            });
            //打开添加弹框
            layui.$('.product-package-create').click(function () {
                layui.common.openIframe('创建产品产品包', 620, 550, 'create.html');
            });
        });
    </script>
</body>
</html>