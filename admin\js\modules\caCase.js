﻿layui.define(['laytpl', 'request', 'tableRequest', 'common', 'uploadFile', 'dic', 'xmSelect', 'workWxApp', 'kfAccount', 'tencentAccount'], function (exports) {
    var func = {
        /**
         * 渲染企微应用下拉选框
         * */
        initApp: function (id) {
            layui.workWxApp.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企微' });
                var getTpl = document.getElementById("workwx-app-tpl").innerHTML
                    , view = document.getElementById('workwx-app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('workwx-app-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过应用id获取企微成员列表
         * @param {any} workWxAppId
         * @param {any} selectIds
         */
        initWorkUser: function (workWxAppId, selectIds) {
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/workwx/department-user/get-all?workWxAppId=' + workWxAppId,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    var data = [];
                    if (res.result.length > 0) {
                        for (var i = 0; i < res.result.length; i++) {
                            var item = { name: res.result[i].name, value: res.result[i].userId };
                            data.push(item);
                        }
                    }
                    xmSelUser = layui.xmSelect.render({
                        el: '#workwx-user',
                        language: 'zn',
                        filterable: true,
                        tips: '请选择承接用户',
                        theme: { color: '#0081ff ' },
                        data: data,
                        toolbar: { show: true },
                        autoRow: true,
                    });
                    var selectItems = [];
                    if (selectIds.length > 0) {
                        for (var i = 0; i < selectIds.length; i++) {
                            selectItems.push(data.find(function (item) {
                                return item.value === selectIds[i];
                            }))
                        }
                    }
                    if (selectItems.length > 0) {
                        xmSelUser.setValue(selectItems);
                    }
                }
            })
        },
        /**
         * 通过应用id获取企微部门列表
         * @param {any} workWxAppId
         * @param {any} selectIds
         */
        initWorkDepartment: function (workWxAppId, selectIds) {
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/workwx/department/get-all?workWxAppId=' + workWxAppId,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    var data = [];
                    if (res.result.length > 0) {
                        for (var i = 0; i < res.result.length; i++) {
                            var item = { name: res.result[i].name, value: res.result[i].departmentId };
                            data.push(item);
                        }
                    }
                    xmSelDepartment = layui.xmSelect.render({
                        el: '#workwx-department',
                        language: 'zn',
                        filterable: true,
                        tips: '请选择承接部门',
                        theme: { color: '#0081ff ' },
                        data: data,
                        toolbar: { show: true },
                        autoRow: true,
                    });
                    var selectItems = [];
                    if (selectIds.length > 0) {
                        for (var i = 0; i < selectIds.length; i++) {
                            selectItems.push(data.find(function (item) {
                                return item.value === selectIds[i];
                            }))
                        }
                    }
                    if (selectItems.length > 0) {
                        xmSelDepartment.setValue(selectItems);
                    }
                }
            })
        },
        /**
         * 通过id获取客服加粉方案
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/ca-case/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=callbackConfig]').val(res.result.callbackConfig);
                    layui.$('input[name=invalidRate]').val(res.result.invalidRate);

                    //加载企微应用列表
                    layui.caCase.initApp(res.result.workWxAppId);

                    //加载使用场景
                    layui.dic.query('workwx_scene', function (resScene) {
                        var options = '<option value="">请选择使用场景</option>'
                        if (resScene.isSuccess) {
                            for (var i = 0; i < resScene.result.length; i++) {
                                options += '<option ' + (res.result.scene == resScene.result[i].key ? 'selected="true"' : '') + ' value="' + resScene.result[i].key + '">' + resScene.result[i].value + '</option>';
                            }
                        }
                        layui.$('#scene').html(options);
                        layui.form.render('select');
                    });

                    //渲染企微账号下拉选框
                    layui.caCase.initWorkUser(res.result.workWxAppId, res.result.userIds);
                    layui.caCase.initWorkDepartment(res.result.workWxAppId, res.result.departmentIds);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取企业标签
         * */
        getCorpTag: function () {
            var id = layui.common.getUrlParam('id') || '';
            layui.request({
                method: 'post',
                url: '/admin/workwx/ca-case/corp-tag/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    var getTpl = document.getElementById("tag-tpl").innerHTML
                        , view = document.getElementById('tag-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.$(document).on('click', '.tag-btn', function () {
                        var that = layui.$(this);
                        if (that.hasClass('cur')) {
                            that.removeClass('cur');
                        }
                        else {
                            that.addClass('cur');
                        }
                    });
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取配额
         * */
        getQuota: function () {
            var workWxAppId = layui.$('#workwx-app-view').val();
            layui.request({
                method: 'post',
                url: '/admin/workwx/ca-case/quota/get?workWxAppId=' + workWxAppId,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('#quotaDesc').text('历史累计使用量：' + res.result.total + '，剩余使用量：' + res.result.balance);
                    var getTpl = document.getElementById("quota-tpl").innerHTML
                        , view = document.getElementById('quota-view');
                    layui.laytpl(getTpl).render(res.result.quotaList, function (html) {
                        view.innerHTML = html;
                    });
                }
                else {
                    layui.$('#quotaDesc').text('');
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取客服加粉方案列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'ca-case-list', '/admin/workwx/ca-case/query', 'application/json', [
                { type: 'checkbox', fixed: 'left' },
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '方案名称' },
                { field: 'workWxAppName', title: '所属企微' },
                { field: 'scene', title: '使用场景' },
                {
                    field: 'userNames', title: '承接用户', templet: function (e) {
                        var userNames = '';
                        if (e.userNames != null && e.userNames.length > 0) {
                            for (var i = 0; i < e.userNames.length; i++) {
                                if (userNames != '') {
                                    userNames += ',';
                                }
                                userNames += e.userNames[i];
                            }
                        }
                        else {
                            userNames = '-';
                        }
                        return userNames;
                    }
                },
                {
                    field: 'departmentNames', title: '承接部门', templet: function (e) {
                        var departmentNames = '';
                        if (e.departmentNames != null && e.departmentNames.length > 0) {
                            for (var i = 0; i < e.departmentNames.length; i++) {
                                if (departmentNames != '') {
                                    departmentNames += ',';
                                }
                                departmentNames += e.departmentNames[i];
                            }
                        }
                        else {
                            departmentNames = '-';
                        }
                        return departmentNames;
                    }
                },
                {
                    title: '标签', minWidth: 180, templet: function (e) {
                        var tags = '';
                        if (e.corpTags != null && e.corpTags.length > 0) {
                            for (var i = 0; i < e.corpTags.length; i++) {
                                tags += '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary">' + e.corpTags[i] + '</button>';
                            }
                        }
                        else {
                            tags = '-';
                        }
                        return tags;
                    }
                },
                {
                    field: 'invalidRate', title: '无效资源比例', templet: function (e) {
                        return (e.invalidRate * 100) + '%';
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 280, align: 'left', toolbar: '#ca-case-bar' }
            ], {}, '#topToolBar');
            //监听表格事件
            layui.caCase.tableEvent();
        },

        /**
        * 创建客服加粉方案
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/ca-case/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("ca-case-list");
                    parent.layui.common.alertAutoClose("加粉方案创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },

        /**
         * 编辑客服加粉方案
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/ca-case/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("ca-case-list");
                    parent.layui.common.alertAutoClose("加粉方案编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 编辑企微标签
        * */
        updateCorpTag: function (data) {
            var id = layui.common.getUrlParam('id') || '';
            layui.request({
                method: 'post',
                url: '/admin/workwx/ca-case/corp-tag/update',
                data: JSON.stringify({ id: id, tags: data.field.tags }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.tableRequest.reload("ca-case-list");
                    parent.layui.common.alertAutoClose("保存成功");
                    parent.layui.common.closeType('iframe');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除获客助手
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/ca-case/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("ca-case-list");
                    layui.common.alertAutoClose("加粉方案删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 编辑获客助手账号(系统生成活码方式)
        * */
        updateUserIds: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/ca-case/userids/batch-update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("ca-case-list");
                    if (res.result != null && res.result != "") {
                        parent.layui.common.alert(res.result, 0);
                    }
                    else {
                        parent.layui.common.alertAutoClose("员工配置成功");
                    }
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event == 'edit') {
                    layui.common.openIframe('获客助手加粉方案', 740, 600, 'update.html?id=' + data.id);
                }
                else if (obj.event == 'tag') {
                    layui.common.openIframe('编辑企业标签', 700, 600, 'update-tag.html?id=' + data.id);
                }
                else if (obj.event === 'url') {
                    layui.common.alert(data.linkUrl);
                }
                else if (obj.event === 'qrcode') {
                    location.href= layui.setter.request.resourceBaseUrl + '/admin/workwx/ca-case/qrcode/download?id=' + data.id;
                }
                else if (obj.event == 'del') {
                    var confirmIndex = layui.layer.confirm('删除加粉方案后，历史落地页加粉将失效，确定删除该方案吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.caCase.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        },
        /**
         * 绑定事件
         * */
        bindEvent: function (opType) {
            //企微应用选择后，加载微信客服账号列表
            layui.form.on('select(workwx-app)', function (data) {
                //渲染企微账号下拉选框
                layui.caCase.initWorkUser(data.value, []);
                layui.caCase.initWorkDepartment(data.value, []);
            });
            //提交数据
            layui.form.on('submit(ca-case-submit)', function (data) {
                var userIds = xmSelUser.getValue('value');
                data.field.userIds = [];
                if (userIds.length > 0) {
                    data.field.userIds = userIds;
                }
                var departmentIds = xmSelDepartment.getValue('value');
                data.field.departmentIds = [];
                if (departmentIds.length > 0) {
                    data.field.departmentIds = departmentIds;
                }
                if (opType == 'create') {
                    layui.caCase.create(data);
                }
                else if (opType == 'update') {
                    layui.caCase.update(data);
                }
                return false; //阻止表单跳转
            });
        }
    }

    exports('caCase', func);
});