﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <form id="uploadForm" method="post" enctype="multipart/form-data">
        <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
            <div class="layui-form-item">
                <label class="layui-form-label">账号</label>
                <div class="layui-input-inline">
                    <input id="file" name="file" type="file" class="layui-btn layui-btn-primary" />
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">&nbsp;</label>
                <div class="layui-input-inline">
                    <a href="//file.dnyx.cn/files/template/%E7%9B%B4%E6%92%AD%E8%B4%A6%E5%8F%B7%E6%A8%A1%E6%9D%BF.xlsx" target="_blank">>> 模板下载</a>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <div class="layui-input-inline">
                    <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="import-submit" value="确认导入">
                </div>
            </div>
        </div>
    </form>
    <script id="customer-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.alias}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'liveAccount'], function () {
            //监听提交事件
            layui.form.on('submit(import-submit)', function (data) {
                layui.liveAccount.import(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>