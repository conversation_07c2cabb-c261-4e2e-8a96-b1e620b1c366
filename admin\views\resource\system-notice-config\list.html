﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list config-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>
            <div class="layui-card-body">
                <table id="config-list" lay-filter="list"></table>
                <script type="text/html" id="config-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['common', 'systemNoticeConfig'], function () {
            layui.systemNoticeConfig.query();
            layui.$('.config-create').click(function () {
                layui.common.openIframe('创建站内信配置', 830, 600, 'create.html')
            });
        })
    </script>
</body>
</html>