﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label {
            width: 100px;
        }

        .layui-form-item .layui-input-inline {
            width: 520px;
        }

        .layui-upload-drag {
            width: 100%;
            box-sizing: border-box;
        }

        .img-item {
            display: inline-block;
            position: relative;
            margin-right: 5px;
        }

        .img-item span.remove {
            position: absolute;
            cursor: pointer;
            right: 0;
            top: 0;
            display: inline-block;
            width: 15px;
            height: 15px;
            text-align: center;
            line-height: 15px;
            background: #000;
            opacity: 0.5;
            color: #fff
        }

        .pay-info {
            margin-bottom: 10px;
            margin-bottom: 10px;
            float: left;
        }
    </style>
</head>

<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list"
        style="padding: 20px 30px 0 0;">
        <input id="id" name="id" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">所属组织</label>
            <div class="layui-input-inline">
                <select name="subsidiaryId" lay-verify="required" id="subsidiary-view" lay-search></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">直播账号</label>
            <div class="layui-input-inline" style="width:200px">
                <input type="text" name="account" lay-verify="required" placeholder="账号" autocomplete="off"
                    class="layui-input">
            </div>
            <div class="layui-input-inline" style="width:310px">
                <input type="text" name="name" lay-verify="required" placeholder="昵称" autocomplete="off"
                    class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">账号类型</label>
            <div class="layui-input-inline" style="width:200px">
                <select name="bigType" class="bigType" id="bigType" lay-verify="required" lay-search>
                    <option value="">请选择平台</option>
                    <option value="1">抖音</option>
                    <option value="2">视频号</option>
                    <option value="3">快手号</option>
                    <option value="4">小鹅通</option>
                    <option value="5">小红书</option>
                </select>
            </div>
            <div class="layui-input-inline" style="width:310px">
                <select name="smallType" class="smallType" id="smallType" lay-verify="required" lay-search>
                    <option value="">请选择账号类别</option>
                    <option value="1">企业员工号</option>
                    <option value="2">员工个人号授权</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">授权时间</label>
            <div class="layui-input-inline" style="width:200px">
                <input type="text" name="authTime" id="authTime" placeholder="授权时间" autocomplete="off"
                    class="layui-input">
            </div>
            <div class="layui-input-inline" style="width:310px">
                <input type="text" name="expirationTime" id="expirationTime" placeholder="过期时间" autocomplete="off"
                    class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">账号状态</label>
            <div class="layui-input-inline">
                <select name="status" class="status" id="status" lay-verify="required" lay-search>
                    <option value="">请选择账号状态</option>
                    <option value="1">使用</option>
                    <!-- <option value="2">撤销</option> -->
                    <option value="3">停用</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">姓名</label>
            <div class="layui-input-inline" style="width:200px">
                <input type="text" name="teacherName" placeholder="姓名" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-input-inline" style="width:310px">
                <input type="text" name="jobNumber" placeholder="执业编号" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item" style="display:none;">
            <label class="layui-form-label">绑定账号</label>
            <div class="layui-input-inline">
                <select name="userId" id="user-view" lay-search>
                    <option value="">请选择绑定账号</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">执业类别</label>
            <div class="layui-input-inline">
                <select name="jobType" id="jobType" class="jobType" lay-verify="required">
                    <option value="">请选择执业类别</option>
                    <option value="1">一般证券业务</option>
                    <option value="2">证券投资咨询(投资顾问)</option>
                    <option value="3">证券投资咨询(分析师)</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">账号备注</label>
            <div class="layui-input-inline">
                <textarea type="text" id="remark" name="remark" placeholder="请输入账号备注" autocomplete="off"
                    class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="account-create-submit"
                    value="确认保存">
            </div>
        </div>
    </div>

    <script id="user-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="subsidiary-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=2.2"></script>
    <script type="text/javascript">
        layui.use(['form', 'laydate', 'liveAccount'], function () {
            layui.laydate.render({
                elem: '#authTime',
                type: 'date'
            });
            layui.laydate.render({
                elem: '#expirationTime',
                type: 'date'
            });
            layui.liveAccount.get();
            layui.form.on('submit(account-create-submit)', function (data) {
                layui.liveAccount.update(data);
                return false; //阻止表单跳转
            });
        });
    </script>
</body>

</html>