﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=8">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">

    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 520px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
        .img-item { display: inline-block; position: relative; margin-right: 5px; }
        .img-item span.remove { position: absolute; cursor: pointer; right: 0; top: 0; display: inline-block; width: 15px; height: 15px; text-align: center; line-height: 15px; background: #000; opacity: 0.5; color: #fff }
        .pay-info { margin-bottom: 10px; margin-bottom: 10px; float: left; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <input id="id" name="id" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">客户</label>
            <div class="layui-input-inline" style="width: 200px">
                <input type="text" name="name" id="name" lay-verify="required" placeholder="客户姓名" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-input-inline" style="width:310px">
                <input type="text" name="mobile" id="mobile" lay-verify="required" placeholder="客户手机号" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">身份证号</label>
            <div class="layui-input-inline">
                <input type="text" name="certNo" id="certNo" placeholder="请输入客户身份证号" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">所属用户</label>
            <div class="layui-input-inline">
                <select name="userId" id="saler-user-view" lay-verify="required" lay-search>
                    <option value="">请选择订单所属用户</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">购买产品</label>
            <div class="layui-input-inline">
                <select name="productId" id="product-view" lay-verify="required" lay-filter="product-view" lay-search>
                    <option value="">请选择购买产品</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">投资顾问</label>
            <div class="layui-input-inline">
                <select name="teacherName" id="teacher-name-view" lay-search>
                    <option value="">请选择投资顾问</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">支付方式/金额</label>
            <div class="pay-items" style="width:520px;float:left;">
                <div class="pay-item-list">
                </div>
                <div class="pay-btn " style="width:100%;">
                    <div class="layui-input-inline" style="width:auto;">
                        <button type="button" class="layui-btn layui-btn-primary add-pay">
                            <i class="layui-icon"></i> 添加支付项
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">服务期限</label>
            <div class="layui-input-inline" style="width: 250px;">
                <input type="text" id="serviceStartTime" name="serviceStartTime" lay-verify="required" placeholder="请选择服务开始时间" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-input-inline" style="width: 260px;">
                <input type="text" id="serviceEndTime" name="serviceEndTime" lay-verify="required" placeholder="请选择服务结束时间" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item" style="display:none;">
            <label class="layui-form-label">客户来源</label>
            <div class="layui-input-inline">
                <select name="customerChannel" id="kf-case-view" lay-search>
                    <option value="">请选择客户来源渠道</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">所属企微</label>
            <div class="layui-input-inline">
                <select name="workWxAppId" id="workwx-app-view" lay-filter="workwx-app" lay-verify="required">
                    <option value="">请选择所属企微</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">企微联系人</label>
            <div class="layui-input-inline" id="external-contact">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="product-order-create-submit" value="确认修改">
            </div>
        </div>
    </div>

    <script id="saler-user-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="product-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        {{# if(item.id==''){}}
        <option value="" data-names="{{item.teacherNames}}">{{item.name}}</option>
        {{#}else{}}
        <option value="{{item.id}}" data-names="{{item.teacherNames}}">{{item.name}} - ¥{{item.amount}}</option>
        {{#}}}
        {{#  }); }}
    </script>
    <script id="kf-case-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="teacher-name-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=3.2"></script>
    <script type="text/javascript">
        var xmSel;
        var payTypeXmSel;
        layui.use(['form', 'laydate', 'common', 'uploadFile', 'productOrder'], function () {
            layui.laydate.render({
                elem: '#serviceStartTime',
                type: 'date'
            });
            layui.laydate.render({
                elem: '#serviceEndTime',
                type: 'date'
            });
            layui.laydate.render({
                elem: '#payTime',
                type: 'date'
            });

            layui.productOrder.getOffline();

            layui.form.on('submit(product-order-create-submit)', function (data) {
                data.field.externalContactId = '';
                data.field.customerChannel = layui.$('#kf-case-view').find('option:selected').text();
                var payList = layui.$('.pay-info');
                if (payList.length == 0) {
                    layui.common.alertAutoClose("请选择支付项");
                    return;
                }
                var payItems = [];
                for (var i = 0; i < payList.length; i++) {
                    var payType = payList.eq(i).find('.payType').val();
                    var payAmount = payList.eq(i).find('.payAmount').val();
                    var thirdPartyOrderNo = payList.eq(i).find('.thirdPartyOrderNo').val();
                    var imgList = payList.eq(i).find('.img-list .img-item');
                    var transferVoucher = '';
                    for (var j = 0; j < imgList.length; j++) {
                        if (transferVoucher != '') {
                            transferVoucher += ';';
                        }
                        transferVoucher += imgList.eq(j).find('img').attr('src');
                    }
                    if (transferVoucher == '') {
                        layui.common.alertAutoClose("未上传支付凭证");
                        return;
                    }

                    if (thirdPartyOrderNo.indexOf(' ') > -1 || thirdPartyOrderNo.indexOf("'") > -1) {
                        layui.common.alertAutoClose("支付单号有误，存在特殊字符");
                        return;
                    }
                    payItems.push({ payType: payType, payAmount: payAmount, transferVoucher: transferVoucher, thirdPartyOrderNo: thirdPartyOrderNo })
                }
                data.field.payItems = payItems;

                var externalContactIds = xmSel.getValue('value');
                if (externalContactIds.length > 0) {
                    data.field.externalContactId = externalContactIds[0];
                }

                if (data.field.externalContactId == '') {
                    layui.common.alertAutoClose("请选择对应的企微联系人");
                    return;
                }
                layui.productOrder.update(data);
                return false; //阻止表单跳转
            });
            //切换产品时加载投资顾问
            layui.form.on('select(product-view)', function (data) {
                var names = layui.$(data.elem).find('option:selected').data('names');
                var teacherArr = [{ id: '', name: '请选择投资顾问' }];
                if (names != '') {
                    var nameList = names.split(',');
                    if (nameList.length > 0) {
                        for (var i = 0; i < nameList.length; i++) {
                            teacherArr.push({ id: nameList[i], name: nameList[i] })
                        }
                    }
                }
                var getTpl = document.getElementById("teacher-name-tpl").innerHTML
                    , view = document.getElementById('teacher-name-view');
                layui.laytpl(getTpl).render(teacherArr, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('select');
            });

            //添加行
            layui.$(document).on("click", '.add-pay', function () {
                var uploadId = layui.common.generateGuid();
                if (layui.$('#uploadImg_' + uploadId).length > 0) {
                    uploadId = layui.common.generateGuid();
                }
                var payTypeArr = layui.$.grep(layui.setter.payTypeArr, function (item) {
                    return !item.isOnline && item.id != 300;
                });
                payTypeArr.unshift({ id: '', name: '支付方式' });
                var menuTpl = '<div class="pay-info pay-' + uploadId + '">';
                menuTpl += '<div class="layui-input-inline" style="width:100px;">';
                menuTpl += '<select name="payType" class="payType" lay-verify="required" lay-search>';
                for (var i = 0; i < payTypeArr.length; i++) {
                    menuTpl += '<option value="' + payTypeArr[i].id + '">' + payTypeArr[i].name + '</option>';
                }
                menuTpl += '</select>';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:90px;">';
                menuTpl += '<input type="text" name="thirdPartyOrderNo" lay-verify="required" placeholder="支付单号" autocomplete="off" class="layui-input thirdPartyOrderNo">';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:80px;">';
                menuTpl += '<input type="text" name="payAmount" lay-verify="required" placeholder="实付金额" autocomplete="off" class="layui-input payAmount">';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:auto;">';
                menuTpl += '<button type="button" class="layui-btn layui-btn-primary " id="uploadImg_' + uploadId + '"><i class="layui-icon"></i>凭证</button>';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline remove-pay-item" style="width:auto;">';
                menuTpl += '<button type="button" class="layui-btn layui-btn-primary remove-pay"><i class="layui-icon"></i></button>';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline img-list" style="width:auto;">';
                menuTpl += '</div>';
                menuTpl += '</div>';

                layui.$('.pay-item-list').append(menuTpl);
                layui.form.render('select');

                layui.uploadFile('resource', 'uploadImg_' + uploadId, '/common/file/public/put?folder=admin/order/transfer-voucher', function (res) {
                    if (res.isSuccess) {
                        layui.$('.pay-' + uploadId).find('.img-list').append('<div class="img-item"><span class="remove" onclick="removeImg(this)">x</span><a href="' + res.result + '" target="_blank"> <img src="' + res.result + '" style="max-height: 35px"></a></div>');
                        layui.common.alertAutoClose("上传成功");
                    }
                    else {
                        layui.common.alert(res.message, 2);
                    }
                });
            });

            //删除行
            layui.$(document).on("click", '.remove-pay', function () {
                layui.$(this).parent().parent('.pay-info').remove();
            });
        });
        var removeImg = function (obj) {
            obj.parentNode.parentNode.removeChild(obj.parentNode);
        }
    </script>
</body>
</html>