﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/lib/font-awesome/font-awesome.min.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属应用</label>
                        <div class="layui-input-inline">
                            <select name="appId" id="app-view" lay-filter="menu">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list menu-create" data-id=""><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="menu-list" lay-filter="list" class="layui-table">
                    <thead>
                        <tr>
                            <th style="width:180px;">编号</th>
                            <th>名称</th>
                            <th>链接地址</th>
                            <th>排序</th>
                            <th>显示</th>
                            <th>创建时间</th>
                            <th style="width:90px;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="menu-view">
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script id="app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="menu-tpl" type="text/html">
        {{# if(d.length==0){}}
        <tr>
            <td colspan="7" style="text-align:center">无数据</td>
        </tr>
        {{# }}}
        {{#  layui.each(d, function(index, item){ }}
        <tr>
            <td>{{item.id}}</td>
            <td>{{item.icon}}&nbsp;&nbsp;{{item.name}}</td>
            <td style="max-width: 300px; word-break: break-word;"><a style="color:#0094ff" href="javascript:;" class="menu-create" data-id="{{item.id}}"><i class="layui-icon">&#xe654;</i>添加子菜单</a>&nbsp;{{item.url}}</td>
            <td>{{item.sort}}</td>
            <td>{{# if(item.isShow){}}是{{# }else{}}否{{#}}}</td>
            <td>{{layui.common.timeFormat(item.createdAt)}}</td>
            <td>
                <a class="layui-btn layui-btn-xs menu-update" data-id="{{item.id}}">编辑</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs menu-del" data-id="{{item.id}}">删除</a>
            </td>
        </tr>
        {{#  layui.each(item.childs, function(indexChild, itemChild){ }}
        <tr>
            <td>{{itemChild.id}}</td>
            <td>&nbsp;└─&nbsp;&nbsp;{{itemChild.icon}}&nbsp;&nbsp;{{itemChild.name}}</td>
            <td style="max-width: 300px; word-break: break-word;">{{itemChild.url}}</td>
            <td>{{itemChild.sort}}</td>
            <td>{{# if(itemChild.isShow){}}是{{# }else{}}否{{#}}}</td>
            <td>{{layui.common.timeFormat(itemChild.createdAt)}}</td>
            <td>
                <a class="layui-btn layui-btn-xs menu-update" data-id="{{itemChild.id}}">编辑</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs menu-del" data-id="{{itemChild.id}}">删除</a>
            </td>
        </tr>
        {{#  }); }}
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['menu'], function () {
            layui.menu.initApp();
            layui.menu.bindEvent('');
        });
    </script>
</body>
</html>