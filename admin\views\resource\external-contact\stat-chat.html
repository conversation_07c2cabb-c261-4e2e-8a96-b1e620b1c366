﻿<!DOCTYPE html>
<html style="background-color: #f0f2f5;">
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <!--<style>
        body { -webkit-font-smoothing: antialiased; text-rendering: optimizeLegibility; font-family: Helvetica Neue,Helvetica,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Arial,sans-serif; }
        .con { float: left; width: 100%; }
        .card { width: 320px; padding-left: 20px; padding-bottom: 20px; float: left; box-sizing: border-box; }
        .card-panel { height: 108px; cursor: pointer; font-size: 12px; position: relative; overflow: hidden; color: #666; background: #fff; -webkit-box-shadow: 4px 4px 40px rgb(0 0 0 / 5%); box-shadow: 4px 4px 40px rgb(0 0 0 / 5%); border-color: rgba(0,0,0,.05); }
        .card-panel-icon-wrapper { float: left; margin: 14px 0 0 14px; padding: 16px; -webkit-transition: all .38s ease-out; transition: all .38s ease-out; border-radius: 6px; }
        .card-panel-description { text-align: right; float: right; font-weight: 700; margin: 26px; margin-left: 0; }
        .card-panel-text { line-height: 18px; color: rgba(0,0,0,.45); font-size: 16px; margin-bottom: 12px; }
        .card-panel-num { font-size: 20px; color: #666; font-weight: 700; }
    </style>-->
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">

                    <div class="layui-inline">
                        <button type="button" class="layui-btn  layui-btn-primary" style="margin:15px 0 15px 30px;" onclick="history.back(-1)">
                            <i class="layui-icon layui-icon-left"></i>返回上一级
                        </button>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">查询时间</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" readonly="readonly" class="layui-input" placeholder="开始时间" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" readonly="readonly" class="layui-input" placeholder="结束时间" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="stat-chat-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                        <button class="layui-btn  layuiadmin-btn-list layui-btn-primary" id="external-contact-export" style="display:none;" lay-submit lay-filter="external-contact-export">
                            <i class="layui-icon layui-icon-export layuiadmin-button-btn"></i>导出外部联系人
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <div id="main" style="width:100%;height:500px;padding:20px;box-sizing:border-box;"></div>
            </div>
            <div class="layui-card-body">
                <table id="external-contact-bykfcaseid-list" lay-filter="list"></table>
            </div>
        </div>
    </div>

    <div class="con">
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/lib/echarts/echarts.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['laydate', 'externalContact'], function () {
            var startTime = layui.common.getUrlParam('startTime') || '';
            var endTime = layui.common.getUrlParam('endTime') || '';
            var from = layui.common.getUrlParam('from') || '';
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime',
                value: startTime,
                min: from == 'customer' ? '2024-09-29' : '2022-01-01'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime',
                value: endTime,
                min: from == 'customer' ? '2024-09-29' : '2022-01-01'
            });
            if (layui.common.getPermission('63369ad46918e5623bad0f66', 'resource.external-contact.bykfcaseid.export')) {
                layui.$('#external-contact-export').show();
            }
            layui.externalContact.initChat();
            layui.externalContact.queryByKfCaseId();
            //监听查询按钮
            layui.form.on('submit(stat-chat-search)', function (data) {
                layui.externalContact.initChat();
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("external-contact-bykfcaseid-list", {
                    where: field
                });
            });
            layui.form.on('submit(external-contact-export)', function (data) {
                var confirmIndex = layui.layer.confirm('确定导出当前外部联系人吗？', {
                    icon: 3,
                    title: '提示',
                    btn: ['确定', '取消']
                }, function () {
                    layui.externalContact.exportByKfCaseId(data);
                    layui.layer.close(confirmIndex);
                }, function () {
                    layui.layer.close(confirmIndex);
                });
            });

        });
    </script>
</body>
</html>