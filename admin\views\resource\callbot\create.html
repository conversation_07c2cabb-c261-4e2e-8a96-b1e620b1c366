﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 80px; }
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">任务名称</label>
            <div class="layui-input-inline">
                <input type="text" name="taskName" lay-verify="required" placeholder="请输入任务名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">话术模板</label>
            <div class="layui-input-inline">
                <select name="templateId" id="template-view" lay-verify="required">
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">机器人数量</label>
            <div class="layui-input-inline">
                <input type="text" name="robotNum" lay-verify="required|number" placeholder="请输入机器人数量" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="task-create-submit" value="确认添加">
            </div>
        </div>
    </div>
    <script id="template-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.templateId}}">{{item.templateName}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'callbot'], function () {
            layui.callbot.getTemplate();
            layui.form.on('submit(task-create-submit)', function (data) {
                layui.callbot.create(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>