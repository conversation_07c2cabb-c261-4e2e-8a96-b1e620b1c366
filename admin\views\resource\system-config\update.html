﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <input id="id" name="id" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">代码</label>
            <div class="layui-input-inline">
                <input type="text" name="configCode" lay-verify="required" placeholder="请输入配置代码" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">名称</label>
            <div class="layui-input-inline">
                <input type="text" name="configName" lay-verify="required" placeholder="请输入配置名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">参数</label>
            <div class="layui-input-inline">
                <input type="text" name="configParam" lay-verify="required" placeholder="请输入配置参数" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">配置值</label>
            <div class="layui-input-inline">
                <input type="text" name="configValue" lay-verify="required" placeholder="请输入配置值" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="submit" value="确认添加">
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.1"></script>
    <script type="text/javascript">
        layui.use(['rsSystemConfig'], function () {
            layui.rsSystemConfig.get();
            //监听提交事件
            layui.form.on('submit(submit)', function (data) {
                layui.rsSystemConfig.update(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>