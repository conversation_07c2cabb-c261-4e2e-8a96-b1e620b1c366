﻿layui.define(['jquery', 'layer'], function (exports) {
    var $ = layui.jquery;
    var layer = layui.layer;
    var func = {
        /**
         * ajax请求
         * @param {any} url
         * @param {any} data
         * @param {any} requestType
         * @param {any} async
         * @param {any} successFunc
         * @param {any} errorFunc
         * @param {any} lodding
         */
        request: function (url, data, method, async, successFunc, errorFunc, lodding) {
            var loddingIndex;
            if (lodding) {
                loddingIndex = layer.load(1, { shade: [0.1, '#fff'] });
            }
            $.ajax({
                url: url,
                type: method,
                data: data,
                async: async,
                dataType: 'json',
                headers: { "content-type": "application/json" },
                success: function (res) {
                    successFunc(res);
                    if (lodding) {
                        layer.close(loddingIndex);
                    }
                },
                error: function (error) {
                    errorFunc(error);
                    if (lodding) {
                        layer.close(loddingIndex);
                    }
                }
            })
        },
        /**
         * 获取地址栏参数
         * @param {any} key
         */
        getUrlParam: function (key) {
            var url = window.location.search;
            var reg = new RegExp("(^|&)" + key + "=([^&]*)(&|$)");
            var result = url.substr(1).match(reg);
            return result ? decodeURIComponent(result[2]) : '';
        },
        /**
        * 日期格式化
        * @param {any} time
        */
        timeFormat: function (time) {
            if (time != '') {
                return layui.util.toDateString(time, 'yyyy-MM-dd HH:mm:ss')
            } else {
                return "";
            }
        },
        /**
         * 消息提示框（自动关闭）
         * @param {any} msg
         */
        alertAutoClose: function (msg) {
            layer.msg(msg);
        },
        /**
         * 消息提示框，不自动关闭
         * @param {any} msg
         * @param {any} icon 0=!；1=√；2=x；3=？；4=锁；5=哭脸；6=笑脸；
         */
        alert: function (msg, icon) {
            layer.alert(msg, { icon: icon });
        },
        /**
         * iframe框
         * @param {any} title
         * @param {any} width
         * @param {any} height
         * @param {any} pageUrl
         * @param {any} isFull
         */
        openIframe: function (title, width, height, pageUrl, isFull) {
            if (isFull) {
                layer.full(layer.open({
                    type: 2,
                    title: title,
                    shadeClose: false,
                    shade: 0.8,
                    area: [width + 'px', height + 'px'],
                    content: pageUrl,
                }));
            } else {
                layer.open({
                    type: 2,
                    title: title,
                    shadeClose: false,
                    shade: 0.8,
                    area: [width + 'px', height + 'px'],
                    content: pageUrl,
                })
            }
        },
        /**
         * 打开页面弹框
         * @param {any} title
         * @param {any} width
         * @param {any} height
         * @param {any} dom
         */
        openPage: function (title, width, height, dom, callback) {
            layer.open({
                type: 1,
                title: title,
                shadeClose: false,
                shade: 0.8,
                area: [width + 'px', height + 'px'],
                content: $(dom),
                end: callback && callback != null ? callback : null
            });
        },
        /**
         * 按类型关闭指定弹出层
         * @param {any} type ：需要关闭某个类型的层 dialog:信息框，page：关闭所有页面层,iframe:关闭所有的iframe层,loading:关闭加载层,tips:关闭所有的tips层
         */
        closeType: function (type) {
            layer.closeAll(type);
        },
        /**
         * 生成GUID
         * */
        generateGuid: function () {
            function S4() {
                return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
            }
            return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
        },
        /**
         * 写入cookie
         * @param {any} name
         * @param {any} value
         */
        setCookie: function (name, value) {
            var days = 30;
            var exp = new Date();
            exp.setTime(exp.getTime() + days * 24 * 60 * 60 * 1000); //换成毫秒
            document.cookie = name + "=" + escape(value) + ";expires=" + exp.toGMTString() + "; path=/";
        },
        /**
         * 写入cookie
         * @param {any} name
         * @param {any} value
         * @param {any} expires
         */
        setCookieExpires: function (name, value, expires) {
            var exp = new Date(expires);
            document.cookie = name + "=" + escape(value) + ";expires=" + exp.toGMTString() + "; path=/";
        },
        /**
         * 读取cookie
         * @param {any} name
         */
        getCookie: function (name) {
            var arr = document.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
            if (arr != null) {
                return unescape(arr[2]);
            } else {
                return null;
            }
        },
        /**
         * 删除cookie
         * @param {any} name
         */
        delCookie: function (name) {
            var exp = new Date(); //当前时间
            exp.setTime(exp.getTime() - 1);
            var cval = layui.common.getCookie(name);
            if (cval != null) document.cookie = name + "=" + cval + ";expires=" + exp.toGMTString() + "; path=/";
        },
        /**
         * 获取是否拥有指定权限
         * @param {any} appId
         * @param {any} permission
         */
        getPermission: function (appId, permission) {
            var res = false;
            var permissions = localStorage.getItem('ly-admin-p-' + appId);
            if (permissions == null || permissions == '') {
                return res;
            }
            var permissionArr = JSON.parse(permissions);
            if (permissionArr.length == 0) {
                return res;
            }
            return permissionArr.indexOf(permission) > -1;
        },
        /**
         * 生成guid
         */
        GUID: function () {
            function S4() {
                return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
            }
            return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
        }
    }

    //输出 common 接口
    exports('common', func);
});