layui.define(['laytpl', 'request', 'tableRequest', "uploadFile", 'common', "form"], function (exports) {
    let form = layui.form;
    var func = {
        /**
        * 通过id获取投资顾问
        * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/basis/teacher/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.lecturer.initLecturer(res.result.investmentAdvisorId)
                    layui.$("#type").val(res.result.type)
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=bgImg]').val(res.result.bgImg);
                    layui.$('input[name=transparentBgImg]').val(res.result.transparentBgImg);
                    layui.$('input[name=rank]').val(res.result.rank);
                    layui.$('textarea[name=desc]').val(res.result.desc);
                    layui.$('input[name=sort]').val(res.result.sort);
                    layui.$('.img-info').html('<img src="' + res.result.bgImg + '" class="" height="35" />');
                    layui.$('.transparentBgImg-info').html('<img src="' + res.result.transparentBgImg + '" class="" height="35" />');
                    layui.$("#isEnable").val(String(res.result.isEnable))
                    layui.form.render('select');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 渲染讲师名称
         * */
        initLecturer: function (id) {
            layui.lecturer.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择讲师名称' });
                var getTpl = document.getElementById("lecturer-name-tpl").innerHTML
                    , view = document.getElementById('lecturerNameView');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                    if (id != undefined) {
                        layui.$('#lecturerNameView').val(id);
                        layui.$("#certNo").val(res.result.find((item => item.id == id)).certNo)
                        layui.form.render('select');
                        console.log("更新成功", id)
                    }
                });
                layui.form.render('select');
            });
        },
        /**上传照片 dom 上传按钮 valdom 存储上传成功url的输入框 imagdom 预览dom option上传的配置项  w 文件宽 h文件高*/
        uploadImage(dom, valDom, imgDom, option, w, h) {
            layui.uploadFile('resource', dom, '/common/file/public/put?folder=admin/investment-advisor/cert', function (res) {
                if (res.isSuccess) {
                    layui.$(valDom).val(res.result);
                    layui.common.alertAutoClose("上传成功");
                    layui.$(imgDom).html('<img src="' + res.result + '" class="" height="35" />');
                }
                else {
                    layui.common.alert(res.message, 2);
                }
            }, function (res) {
                let flag = true;
                res.preview(function (index, file, result) {
                    let img = new Image();
                    img.src = result;
                    img.onload = function () { //初始化夹在完成后获取上传图片宽高，判断限制上传图片的大小。
                        if (img.width == w && img.height == h) {
                            res.upload(index, file); //满足条件调用上传方法
                        } else {
                            flag = false;
                            layer.msg("您上传的小图大小必须是像素" + w + "*" + h + "px尺寸（2寸照片）！");
                            return false;
                        }
                    }
                    return flag;
                });
            }, option);
        },
        /**获取讲师列表 */
        getAll(callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/basis/investment-advisor/by-subsidiaryid/qurey',
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 获取讲师列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'lecturer-list', '/admin/basis/teacher/query', 'application/json', [
                {
                    field: 'type', title: '讲师类型', fixed: 'left',
                    templet: function (e) {
                        return e.type == 1 ? "证券投资培训" : "证券投资顾问"
                    }
                },
                { field: 'name', title: '姓名', fixed: 'left' },
                { field: 'certNo', title: '职业编号' },
                { field: 'rank', title: '头衔' },
                { field: 'desc', title: '介绍' },
                {
                    title: '背景图', templet: function (e) {
                        if (e.bgImg != null && e.bgImg != '') {
                            return '<a href="preview.html?url=' + e.bgImg + '" target="_blank"><img src="' + e.bgImg + '" class="" height="35" /></a>';
                        }
                        return '-';
                    }
                },
                {
                    title: '透明背景头像', templet: function (e) {
                        if (e.transparentBgImg != null && e.transparentBgImg != '') {
                            return '<a href="preview.html?url=' + e.transparentBgImg + '" target="_blank"><img src="' + e.transparentBgImg + '" class="" height="35" /></a>';
                        }
                        return '-';
                    }
                },
                { field: 'sort', title: '排序' },
                {
                    field: 'isEnable', title: '是否可用', templet: function (e) {
                        return e.isEnable ? "可用" : "不可用"
                    }
                },
                { fixed: 'right', title: '操作', width: 170, align: 'left', toolbar: '#role-bar' }
            ]);
            //监听表格事件
            layui.lecturer.tableEvent();
        },
        /**
        * 创建单个角色
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/basis/teacher/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("lecturer-list");
                    parent.layui.common.alertAutoClose("讲师创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑角色
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/basis/teacher/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("lecturer-list");
                    parent.layui.common.alertAutoClose("角色编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除角色
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/basis/teacher/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("lecturer-list");
                    layui.common.alertAutoClose("讲师删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑角色', 650, 480, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该讲师吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.lecturer.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'permission') {
                    location.href = '../permission/settab.html?id=' + data.id + '&type=role&name=' + data.name;
                }
            });
        }
    }

    exports('lecturer', func);
});