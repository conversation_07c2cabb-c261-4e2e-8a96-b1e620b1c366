﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 80px; }
        .layui-form-item .layui-input-inline { width: 400px; }
        #orderInfo .layui-form-mid { padding-bottom: 0 !important; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 10px 30px 0 0;">
        <input id="orderId" name="orderId" type="hidden" />
        <div class="layui-form-item" style="margin-bottom:0">
            <label class="layui-form-label">订单信息</label>
            <div class="layui-input-inline" id="orderInfo">
                <label class="layui-form-mid layui-word-aux" id="productInfo"></label>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">支付总金额</label>
            <div class="layui-input-inline">
                <label class="layui-form-mid layui-word-aux" id="totalPayAmount"></label>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">退款金额</label>
            <div class="layui-input-inline">
                <input type="text" id="amount" name="amount" autocomplete="off" placeholder="请输入总退款金额" lay-verify="required" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">银行卡号</label>
            <div class="layui-input-inline">
                <input type="text" id="bankCardNo" name="bankCardNo" placeholder="退款银行卡号" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">开户行</label>
            <div class="layui-input-inline">
                <input type="text" id="openBankBranch" name="openBankBranch" placeholder="退款银行开户行" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item" style="display:none">
            <label class="layui-form-label">退款原因</label>
            <div class="layui-input-inline">
                <input type="text" id="reason" name="reason" placeholder="退款原因，用户账单中可查看" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">退款备注</label>
            <div class="layui-input-inline">
                <textarea type="text" name="remark" placeholder="退款备注信息，仅内部可见" autocomplete="off" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="order-refund-submit" value="申请退款">
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['productOrder'], function () {
            layui.productOrder.getByIds();
            //监听提交事件
            layui.form.on('submit(order-refund-submit)', function (data) {
                layui.productOrder.batchApplyRefund(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>