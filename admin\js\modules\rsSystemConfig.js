﻿layui.define(['laytpl', 'form', 'request', 'tableRequest', 'common', 'uploadFile'], function (exports) {
    var func = {
        /**
         * 通过id获取单个系统配置信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                setTimeout(function () {
                    parent.location.reload();
                }, 3000);
                return;
            }
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/config/system-config/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=configCode]').val(res.result.configCode);
                    layui.$('input[name=configName]').val(res.result.configName);
                    layui.$('input[name=configParam]').val(res.result.configParam);
                    layui.$('input[name=configValue]').val(res.result.configValue);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取系统配置列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'system-config-list', '/admin/config/system-config/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'configCode', title: '代码', width: '10%' },
                { field: 'configName', title: '名称', },
                { field: 'configParam', title: '参数', },
                {
                    field: 'createdAt', title: '创建时间', templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', width: 120, title: '操作', align: 'left', toolbar: '#system-config-bar' }
            ]);
            //监听表格事件
            layui.rsSystemConfig.tableEvent();
        },
        /**
        * 创建单个系统配置
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/config/system-config/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑单个系统配置
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/config/system-config/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除单个系统配置
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/config/system-config/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("system-config-list");
                    layui.common.alertAutoClose("删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑系统配置', 500, 350, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该系统配置吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.rsSystemConfig.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('rsSystemConfig', func);
});