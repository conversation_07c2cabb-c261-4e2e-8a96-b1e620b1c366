﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">

                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="txb_keywords" name="keywords" placeholder="微信openid/昵称" type="text" class="layui-input" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">推送状态</label>
                        <div class="layui-input-inline">
                            <select name="notificationStatus">
                                <option value="">请选择消息推送状态</option>
                                <option value="1">启用</option>
                                <option value="2">禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">订阅状态</label>
                        <div class="layui-input-inline">
                            <select name="subscribeStatus">
                                <option value="">请选择消息订阅状态</option>
                                <option value="1">未订阅</option>
                                <option value="2">已订阅</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="mp-user-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="mp-user-list" lay-filter="list"></table>
                <p>微信绑定地址：<a href="javascript:;">https://dy5.zqt888.cn/pages/notification/index.html</a> </p>
            </div>
        </div>
    </div>
    <script type="text/html" id="switchTpl">
        <input type="checkbox" name="notificationCbk" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="notificationCbk" {{ d.notificationStatus == 1 ? 'checked' : '' }}>
    </script>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'mpUser'], function () {
            layui.mpUser.query();
            //监听查询按钮
            layui.form.on('submit(mp-user-search)', function (data) {
                var field = data.field;
                if (field.notificationStatus == '') {
                    field.notificationStatus = -1;
                }
                if (field.subscribeStatus == '') {
                    field.subscribeStatus = -1;
                }
                //执行重载
                layui.tableRequest.reload("mp-user-list", {
                    where: field
                });
            });
            //监听状态切换按钮
            layui.form.on('switch(notificationCbk)', function (obj) {
                var confirmIndex = layui.layer.confirm('确定' + (obj.elem.checked ? '启用' : '禁用') + '微信用户消息通知吗？', {
                    icon: 3,
                    title: '提示',
                    btn: ['确定', '取消']
                }, function () {
                    layui.mpUser.updateStatus(obj);
                }, function () {
                    obj.elem.checked = !obj.elem.checked;
                    layui.layer.close(confirmIndex);
                    layui.form.render('checkbox');
                });

            });
        });
    </script>
</body>
</html>