﻿layui.define(['laytpl', 'request', 'tableRequest', 'common', 'xmSelect', 'workWxApp'], function (exports) {
    var func = {
        /**
        * 获取所有企微应用列表
        * */
        queryByWorkWxAppId: function (workWxAppId, callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/qrcode/by-workwxappid/query?workWxAppId=' + workWxAppId,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 渲染企微应用下拉选框
         * */
        initApp: function (id) {
            layui.workWxApp.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企微' });
                var getTpl = document.getElementById("workwx-app-tpl").innerHTML
                    , view = document.getElementById('workwx-app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('workwx-app-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 加载企微活码分组
         * */
        initGroup: function () {
            layui.$('#groupId').val('');
            layui.request({
                method: 'post',
                url: '/admin/workwx/qrcode-group/get-all',
            }).then(function (res) {
                if (res.isSuccess) {
                    res.result.unshift({ id: '', name: '全部' });
                    var getTpl = document.getElementById("group-tpl").innerHTML
                        , view = document.getElementById('group-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.$('.group-view li span').click(function () {
                        layui.$('.group-view li').removeClass('cur');
                        layui.$(this).parent('li').addClass('cur');
                        layui.$('#groupId').val(layui.$(this).parent('li').data('id'));
                        layui.tableRequest.reload("qrcode-list", {
                            where: {
                                groupId: layui.$(this).parent('li').data('id'),
                                workWxAppId: layui.$('#workwx-app-view').val(),
                                startTime: layui.$('#startTime').val(),
                                endTime: layui.$('#endTime').val(),
                                keywords: layui.$('#keywords').val()
                            }
                        });
                    });
                    layui.$('.group-view li a.edit').click(function () {
                        var id = layui.$(this).parent('li').data('id');
                        layui.common.openIframe('企微活码分组', 450, 200, 'update-group.html?id=' + id);
                    });
                    layui.$('.group-view li a.del').click(function () {
                        var id = layui.$(this).parent('li').data('id');
                        var confirmIndex = layui.layer.confirm('确定删除该分组吗？', {
                            icon: 3,
                            title: '提示',
                            btn: ['确定', '取消']
                        }, function () {
                            layui.qrcode.deleteGroup(id);
                        }, function () {
                            layui.layer.close(confirmIndex);
                        });
                    })
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 加载企微活码分组(新增/编辑页)
         * */
        initGroupForOp: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/qrcode-group/get-all',
            }).then(function (res) {
                if (res.isSuccess) {
                    res.result.unshift({ id: '', name: '请选择分组' });
                    var getTpl = document.getElementById("group-tpl").innerHTML
                        , view = document.getElementById('group-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    if (id != undefined) {
                        document.getElementById('group-view').value = id;
                    }
                    layui.form.render('select');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 加载企微标签
         * */
        initCorpTag: function (type) {
            var workWxAppId = parent.layui.$('#workwx-app-view').val();
            if (workWxAppId == '') {
                layui.common.alertAutoClose("请选择所属企微");
                return;
            }
            layui.workWxApp.getCorpTags(workWxAppId, function (res) {
                if (res.isSuccess) {
                    //判断是否选中
                    if (res.result != null && res.result.length > 0) {
                        var existTagIds = [];
                        var dom = type == 1 ? '#corp-tag' : type == 2 ? '#look-corp-tag' : '-';
                        var corptagList = parent.layui.$(dom + ' a');
                        if (corptagList.length > 0) {
                            for (var i = 0; i < corptagList.length; i++) {
                                existTagIds.push(corptagList.eq(i).data('id'));
                            }
                        }
                        for (var i = 0; i < res.result.length; i++) {
                            if (res.result[i].tag != null && res.result[i].tag.length > 0) {
                                for (var j = 0; j < res.result[i].tag.length; j++) {
                                    res.result[i].tag[j].isChecked = existTagIds.indexOf(res.result[i].tag[j].id) > -1;
                                }
                            }
                        }
                    }
                    //模板渲染
                    var getTpl = document.getElementById("tag-tpl").innerHTML
                        , view = document.getElementById('tag-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    //加载完成绑定事件
                    layui.$(document).on('click', '.tag-btn', function () {
                        if (type == 1) {
                            var that = layui.$(this);
                            if (that.hasClass('cur')) {
                                that.removeClass('cur');
                            }
                            else {
                                that.addClass('cur');
                            }
                        }
                        else if (type == 2) {
                            layui.$('.tag-btn').removeClass('cur');
                            var that = layui.$(this);
                            that.addClass('cur');
                        }
                    });
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            });
        },
        /**
         * 通过企微获取企微用户列表
         * @param {any} workWxAppId
         */
        initWorkWxUser: function (workWxAppId, callbackFunc) {
            layui.workWxApp.getWorkWxUser(workWxAppId, function (res) {
                if (res.isSuccess) {
                    workWxUsers = res.result;
                    if (workWxUsers.length > 0) {
                        var selectList = layui.$('.workwxuser');
                        if (selectList.length > 0) {
                            for (var i = 0; i < selectList.length; i++) {
                                var option = '<option value="">请选择员工</option>';
                                for (var j = 0; j < workWxUsers.length; j++) {
                                    option += '<option value="' + workWxUsers[j].userId + '">' + workWxUsers[j].name + '</option>'
                                }
                                selectList.eq(i).html(option);
                            }
                        }
                        var xmSelUserData = [];
                        for (var i = 0; i < workWxUsers.length; i++) {
                            var item = { name: workWxUsers[i].name, value: workWxUsers[i].userId };
                            xmSelUserData.push(item);
                        }
                        xmSelUser = layui.xmSelect.render({
                            el: '#spareServices',
                            language: 'zn',
                            filterable: true,
                            tips: '请选择备用员工',
                            theme: { color: '#0081ff ' },
                            data: xmSelUserData,
                            toolbar: { show: true },
                            autoRow: true,
                        });
                        if (callbackFunc != undefined && callbackFunc != null) {
                            callbackFunc(res);
                        }
                    }
                    layui.form.render('select');
                }
            });
        },
        /**
         * 通过id获取企微活码分组
         * */
        getGroup: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/qrcode-group/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=sort]').val(res.result.sort);
                    layui.$('input[name=name]').val(res.result.name);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过id获取企微活码
         * */
        getQrCode: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/qrcode/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.qrcode.initApp(res.result.workWxAppId);
                    layui.qrcode.initGroupForOp(res.result.groupId);
                    layui.$("input[name='type'][value='" + res.result.type + "']").prop('checked', true);
                    layui.$("input[name='statisticsMethods'][value='" + res.result.statisticsMethods + "']").prop('checked', true);
                    layui.qrcode.initWorkWxUser(res.result.workWxAppId, function (userRes) {
                        var serviceItem = '';
                        for (var i = 0; i < res.result.services.length; i++) {
                            serviceItem += ' <tr><td> <select name="workwxuser" disabled="disabled" class="item workwxuser" lay-verify="required" lay-search>';
                            serviceItem += '<option value="">请选择员工</option>';
                            if (workWxUsers.length > 0) {
                                for (var j = 0; j < workWxUsers.length; j++) {
                                    serviceItem += '<option value="' + workWxUsers[j].userId + '" ' + (workWxUsers[j].userId == res.result.services[i].userId ? "selected" : "") + '>' + workWxUsers[j].name + '</option>'
                                }
                            }
                            serviceItem += '</select></td>';
                            serviceItem += '<td> <input type="text" name="maxcount" lay-verify="required|number" placeholder="人数上限" value="' + res.result.services[i].maxCount + '" autocomplete="off" class="layui-input item maxcount"></td>';
                            serviceItem += '<td><a href="javascript:;" class="remove-item"><i class="layui-icon">&#xe640;</i></a></td></tr>';
                        }
                        layui.$('.tb tbody').html(serviceItem);
                        if (res.result.spareServices != null && res.result.spareServices.length > 0) {
                            var spareServiceItem = [];
                            for (var i = 0; i < res.result.spareServices.length; i++) {
                                spareServiceItem.push({ name: res.result.spareServices[i].userName, value: res.result.spareServices[i].userId });
                            }
                            xmSelUser.setValue(spareServiceItem);
                        }
                    });
                    if (res.result.corpTags != null && res.result.corpTags.length > 0) {
                        var tagItem = '';
                        for (var i = 0; i < res.result.corpTags.length; i++) {
                            tagItem += '<a href="javascript:;" data-groupname="' + res.result.corpTags[i].groupName + '" data-id="' + res.result.corpTags[i].tagId + '" data-name="' + res.result.corpTags[i].tagName + '"><span>' + res.result.corpTags[i].tagName + '</span><i class="layui-icon remove-tag">&#x1006;</i></a>';
                        }
                        layui.$('#corp-tag').html(tagItem);
                    }
                    if (res.result.lookCorpTags != null) {
                        var tagItem = '<a href="javascript:;" data-groupname="' + res.result.lookCorpTags.groupName + '" data-id="' + res.result.lookCorpTags.tagId + '" data-name="' + res.result.lookCorpTags.tagName + '"><span>' + res.result.lookCorpTags.tagName + '</span><i class="layui-icon remove-tag">&#x1006;</i></a>';
                        layui.$('#look-corp-tag').html(tagItem);
                    }
                    layui.form.render('radio');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取企微活码列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'qrcode-list', '/admin/workwx/qrcode/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left', width: 50 },
                { field: 'workWxAppName', title: '企微名称', width: 110 },
                {
                    field: 'groupName', title: '分组', width: 150, templet: function (e) {
                        return e.groupName == '' ? '-' : e.groupName;
                    }
                },
                {
                    field: 'qrCodeUrl', title: '活码', width: 60, align: 'center', templet: function (e) {
                        return e.type == 1 && e.qrCodeUrl != '' ? '<a target="_blank" href="' + e.qrCodeUrl + '"><img src="' + e.qrCodeUrl + '" style="height:25px"/></a>' : '-';
                    }
                },
                { field: 'name', title: '活码名称' },
                {
                    title: '员工', templet: function (e) {
                        var user = '';
                        if (e.serviceNames.length > 0) {
                            for (var i = 0; i < e.serviceNames.length; i++) {
                                user += '<button type="button" class="layui-btn layui-btn-xs  layui-btn-normal">' + e.serviceNames[i] + '</button>';
                            }
                        }
                        return user;
                    }
                },
                {
                    title: '标签', templet: function (e) {
                        var tags = '';
                        if (e.corpTagNames.length > 0) {
                            for (var i = 0; i < e.corpTagNames.length; i++) {
                                tags += '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary">' + e.corpTagNames[i] + '</button>';
                            }
                        }
                        return tags;
                    }
                },
                /*  { field: 'createUserName', title: '创建人' },*/
                {
                    field: 'createdAt', title: '创建时间', width: 160, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 120, align: 'left', toolbar: '#qrcode-bar' }
            ]);
            //监听表格事件
            layui.qrcode.tableEvent();
        },

        /**
        * 创建企微活码分组
        * */
        createGroup: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/qrcode-group/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.qrcode.initGroup();
                    parent.layui.common.alertAutoClose("企微活码分组创建成功");
                    layui.tableRequest.reload("qrcode-list");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 创建企微活码
        * */
        createQrCode: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/qrcode/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("企微活码创建成功");
                    setTimeout(function () { location.href = 'list.html'; }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },

        /**
        * 编辑企微活码分组
        * @param {any} data
        */
        updateGroup: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/qrcode-group/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.qrcode.initGroup();
                    parent.layui.common.alertAutoClose("企微活码分组编辑成功");
                    layui.tableRequest.reload("qrcode-list");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 编辑企微活码
        * @param {any} data
        */
        updateQrCode: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/qrcode/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("企微活码编辑成功");
                    setTimeout(function () { location.href = 'list.html'; }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除企微活码分组
         * @param {any} id
         */
        deleteGroup: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/qrcode-group/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.qrcode.initGroup();
                    layui.common.alertAutoClose("分组删除成功");
                    layui.tableRequest.reload("qrcode-list");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 删除企微活码
        * @param {any} data
        */
        deleteQrCode: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/qrcode/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("qrcode-list");
                    layui.common.alertAutoClose("企微活码删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event == 'edit') {
                    location.href = 'update-qrcode.html?id=' + data.id;
                }
                else if (obj.event == 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该企微活码吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.qrcode.deleteQrCode(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('qrcode', func);
});