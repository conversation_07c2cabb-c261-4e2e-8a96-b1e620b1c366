﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 800px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 20px 0;">
                <input id="id" name="id" type="hidden" />
                <div class="layui-form-item">
                    <label class="layui-form-label">课程名称</label>
                    <div class="layui-input-inline">
                        <input type="text" name="name" lay-verify="required" placeholder="请输入课程名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">课程金额</label>
                    <div class="layui-input-inline">
                        <input type="text" name="amount" lay-verify="required|number" placeholder="请输入课程金额" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">投资顾问</label>
                    <div class="layui-input-inline">
                        <input type="text" name="teacherName" lay-verify="required" placeholder="请输入投资顾问名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">课程类型</label>
                    <div class="layui-input-inline">
                        <select name="type" id="type" lay-verify="required">
                            <option value="">请选择课程类型</option>
                            <option value="课程">课程</option>
                            <option value="投顾">投顾</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-inline">
                        <select name="status" id="status" lay-verify="required">
                            <option value="">请选择产品状态</option>
                            <option value="1">正常销售</option>
                            <option value="2">暂停销售</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">课程封面</label>
                    <div class="layui-input-inline">
                        <input id="hid_coverUrl" name="coverUrl" type="hidden" />
                        <div class="layui-upload-drag" id="uploadImg">
                            <i class="layui-icon"></i>
                            <p>点击上传，或将文件拖拽到此处</p>
                            <div class="layui-hide" id="uploadDemoView">
                                <hr>
                                <img src="" alt="上传成功后渲染" style="max-width: 196px">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">H5落地页</label>
                    <div class="layui-input-inline">
                        <input type="text" name="h5Url" placeholder="自定义落地页时页面地址" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">课程描述</label>
                    <div class="layui-input-inline">
                        <textarea type="text" name="desc" id="desc" lay-verify="required" placeholder="请输入课程描述" autocomplete="off" class="layui-textarea"></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">课程详情</label>
                    <div class="layui-input-inline">
                        <textarea type="text" name="detail" id="detail" placeholder="请输入课程详情" autocomplete="off" class="layui-textarea"></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-inline">
                        <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="course-submit" value="确认修改">
                        <input type="button" class="layui-btn layui-btn-primary" onclick="location.href ='list.html'" value="返回上一页">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="/lib/tinymce/tinymce.min.js"></script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['course'], function () {
            layui.course.get();
            layui.course.bindEvent('update');
        });
    </script>
</body>
</html>