﻿layui.define(['laytpl', 'form', 'laydate', 'request', 'tableRequest', 'common', 'customer', 'domainConfig', 'pageConfig', 'formAdminUser'], function (exports) {
    var func = {
        /**
       * 渲染分配用户列表
       * @param {any} id
       */
        initSaleUser: function () {
            layui.formAdminUser.initSaleUser(function (res) {
                if (res.isSuccess) {
                    res.result.unshift({ id: '', name: '请选择分配用户', account: '' });
                    var getTpl = document.getElementById("sale-user-tpl").innerHTML
                        , view = document.getElementById('sale-user-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    var getTpl2 = document.getElementById("sale-user-2-tpl").innerHTML
                        , view2 = document.getElementById('sale-user-2-view');
                    layui.laytpl(getTpl2).render(res.result, function (html) {
                        view2.innerHTML = html;
                    });
                    layui.form.render('select');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            });
        },
        /**
        * 渲染客户列表
        * @param {any} companyId
        * @param {any} selectedId
        */
        initCustomer: function (selectedId) {
            layui.customer.getAll(function (res) {
                res.result.unshift({ id: '', id: '', alias: '请选择客户' });
                var getTpl = document.getElementById("customer-tpl").innerHTML
                    , view = document.getElementById('customer-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (selectedId != undefined) {
                    view.value = selectedId;
                }
                layui.form.render('select');
            }, true);
        },
        /**
         * 渲染弹框客户列表
         */
        initCustomer2: function () {
            layui.customer.getAll(function (res) {
                res.result.unshift({ id: '', id: '', alias: '请选择客户' });
                var getTpl = document.getElementById("customer-tpl").innerHTML
                    , view = document.getElementById('customer-view-2')
                    , view2 = document.getElementById('customer-view-3');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view2.innerHTML = html;
                });
                layui.form.render('select');
            }, true);
        },
        /**
        * 渲染客户列表
        * @param {any} selectedId
        */
        initCustomerForCheckBox: function () {
            layui.customer.getAll(function (res) {
                var getTpl = document.getElementById("customer-tpl").innerHTML
                    , view = document.getElementById('customer-list-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('checkbox');
            }, true);
        },
        /**
        * 渲染域名列表
        * */
        initDomain: function () {
            layui.domainConfig.getAll(function (res) {
                res.result.unshift({ id: '', domainName: '' });
                var getTpl = document.getElementById("domain-tpl").innerHTML
                    , view = document.getElementById('domain-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('select');
            });
        },
        /**
         * 渲染落地页列表
         * */
        initPage: function () {
            layui.pageConfig.getAll(function (res) {
                res.result.unshift({ id: '', title: '请选择落地页', path: '' });
                var getTpl = document.getElementById("page-tpl").innerHTML
                    , view = document.getElementById('page-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('select');
            });
        },
        /**
       * 通过id获取资源简要信息
       * */
        getBrief: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                parent.layui.common.closeType('iframe');
                return;
            }
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/res/form-resource/brief/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('select[name=callType]').val(res.result.callType);
                    layui.$('select[name=addWeChat]').val(res.result.addWeChat);
                    layui.$('select[name=reply]').val(res.result.reply);
                    layui.$('select[name=enteringClass]').val(res.result.enteringClass);
                    layui.form.render('select');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取未读资源条数
         */
        getUnReadCount: function () {
            layui.request({
                lodding: false,
                requestBase: 'form',
                method: 'post',
                url: '/admin/res/form-resource/unread/count',
            }).then(function (res) {
                if (res.isSuccess) {
                    if (res.result > 0) {
                        //var audio = document.getElementById("bgMusic");
                        //audio.play();
                        //new Notification('您有一条新通知', {
                        //    body: '您有' + res.result + '资源更新，请注意查看！',
                        //});
                        //var tipsIndex = layui.layer.alert('您有“' + res.result + '”条新资源更新，请注意查看！', {
                        //    offset: 'rb', shade: 0, btn: ''
                        //});
                        //setTimeout(function () { layui.layer.close(tipsIndex); }, 5000)
                        layui.formResource.customerQuery();
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 获取表单资源列表
        * */
        query: function () {
            layui.tableRequest.request('form', true, 'form-resource-list', '/admin/res/form-resource/query', 'application/json', [
                { type: 'checkbox', fixed: 'left' },
                { field: 'id', title: 'ID', width: 80 },
                {
                    field: 'customerName', title: '所属客户', templet: function (e) {
                        if (e.customerName != '') {
                            return e.customerName;
                        }
                        else {
                            return '<a class="layui-btn layui-btn-primary layui-btn-xs" onclick="layui.formResource.openUpdateCustomerIdDialog(\'' + e.id + '\')">分配</a>';
                        }
                    }
                },
                { field: 'realName', title: '姓名/代码' },
                {
                    field: 'mobile', title: '手机号', templet: function (e) {
                        if (e.isTest || e.isDeleted) {
                            return '<span style="color:#f00">' + e.mobile + (e.isTest ? ' [测试]' : '') + (e.isDeleted ? ' [删除-' + e.deletedOpUser + ']' : '') + '</span>';
                        }
                        else {
                            return e.mobile;
                        }
                    }
                },
                { field: 'keywords', title: '系统关键词' },
                {
                    title: '搜索关键词', templet: function (e) {
                        return e.searchKeywords != '' ? e.searchKeywords : '-';
                    }
                },
                { field: 'pageUrl', title: '落地页' },
                {
                    title: '分配用户', templet: function (e) {
                        return e.saleUserName != '' ? e.saleUserName : '-';
                    }
                },
                { field: 'clientIP', title: '客户端IP' },
                {
                    field: 'createdAt', title: '创建时间', width: 160, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                {
                    title: '渠道', templet: function (e) {
                        return e.channel != '' ? e.channel : '-';
                    }
                },
                { fixed: 'right', title: '操作', width: 70, align: 'left', toolbar: '#form-resource-bar' }
            ], {}, '#topToolBar');
            layui.formResource.tableEvent();
        },
        /**
         * 获取表单资源列表
         * */
        customerQuery: function () {
            var beforeFields = [
                { type: 'checkbox', fixed: 'left' },
                { field: 'realName', title: '姓名/代码' },
                {
                    field: 'mobile', title: '手机号', templet: function (e) {
                        if (e.isTest || e.isDeleted) {
                            return '<span style="color:#f00">' + e.mobile + (e.isTest ? ' [测试]' : '') + '</span>';
                        }
                        else {
                            return e.mobile;
                        }
                    }
                },
                {
                    title: '关键词', templet: function (e) {
                        var keywords = e.searchKeywords;
                        if (keywords == null || keywords == '') {
                            keywords = e.keywords;
                        }
                        return keywords;
                    }
                },
                {
                    title: '分配用户', templet: function (e) {
                        return e.saleUserName != '' ? e.saleUserName : '-';
                    }
                },
                {
                    title: '拨打情况', templet: function (e) {
                        return e.callType != null && e.callType != '' ? e.callType : '-';
                    }
                },
                {
                    title: '是否加V', templet: function (e) {
                        return e.addWeChat != null && e.addWeChat != '' ? e.addWeChat : '-';
                    }
                },
                {
                    title: '回复情况', templet: function (e) {
                        return e.reply != null && e.reply != '' ? e.reply : '-';
                    }
                },
                {
                    title: '进课情况', templet: function (e) {
                        return e.enteringClass != null && e.enteringClass != '' ? e.enteringClass : '-';
                    }
                },
                { field: 'clientIP', title: '客户端IP' }
            ];
            var cid = layui.common.getCookie('admin_cid');
            var isShowChannel = false;
            if (cid == '666fb199af50e671d9d2a5e6') {
                //显示渠道
                isShowChannel = true;
            }
            if (isShowChannel) {
                beforeFields.push({
                    title: '渠道', templet: function (e) {
                        return e.channel != '' ? e.channel : '-';
                    }
                })
            }
            var afterFields = [{
                field: 'createdAt', title: '创建时间', templet: function (e) {
                    return layui.common.timeFormat(e.createdAt);
                }
            },
            { fixed: 'right', title: '操作', width: 140, align: 'left', toolbar: '#form-resource-bar' }];
            var fields = beforeFields.concat(afterFields);
            layui.tableRequest.request('form', true, 'form-resource-list', '/admin/res/form-resource/query', 'application/json', fields, { isUpdateLastId: true }, '#topToolBar');
            layui.formResource.tableEvent();
        },
        /**
         * 打开修改所属客户弹框
         * @param {any} id
         */
        openUpdateCustomerIdDialog: function (id) {
            layui.formResource.initCustomer2();
            layui.$('#id').val(id);
            layui.common.openPage('分配资源', 400, 300, '#distribution-dialog2');
        },
        /**
       * 创建单个资源
       * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/res/form-resource/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                if (res.isSuccess) {
                    layui.common.alertAutoClose("创建成功");
                    setTimeout(function () {
                        location.reload();
                    }, 2000);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 修改单个资源所属客户
         * @param {any} data
         */
        updateCustomerId: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/res/form-resource/customerid/update',
                data: JSON.stringify({
                    customerId: data.field.customerId3,
                    id: data.field.id
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled", "disabled");
                if (res.isSuccess) {
                    layui.tableRequest.reload("form-resource-list");
                    layui.$('#id').val('');
                    layui.$('#customer-view-3').val('');
                    layui.layer.closeAll('page');
                    layui.form.render('select');
                    layui.common.alertAutoClose("资源分配成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 修改多个资源所属用户
        * @param {any} data
        */
        batchUpdatUserId: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/res/form-resource/userid/update',
                data: JSON.stringify({
                    ids: data.field.resourceIds.split(','),
                    userId: data.field.userId
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled", "disabled");
                if (res.isSuccess) {
                    layui.tableRequest.reload("form-resource-list");
                    layui.$('#resourceIds').val('');
                    layui.$('#sale-user-2-view').val('');
                    layui.layer.closeAll('page');
                    layui.form.render('select');
                    layui.common.alertAutoClose("资源分配成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 修改单个跟进情况
         * @param {any} data
         */
        updateFollowUp: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/res/form-resource/follow-up/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled", "disabled");
                if (res.isSuccess) {
                    parent.layui.tableRequest.reload("form-resource-list");
                    parent.layui.common.alertAutoClose("编辑成功");
                    parent.layui.common.closeType('iframe');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 删除表单资源
        * @param {any} data
        */
        delete: function (id) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/res/form-resource/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("form-resource-list");
                    layui.common.alertAutoClose("资源删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 批量删除表单资源
        * @param {any} ids
        */
        batchDelete: function (ids) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/res/form-resource/batch-delete',
                data: JSON.stringify({
                    ids: ids
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("form-resource-list");
                    layui.common.alertAutoClose("资源删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 批量分配资源
         * @param {any} data
         */
        batchDistribution: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            var ids = data.field.ids.split(',');
            var customerId = data.field.customerId2;
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/res/form-resource/batch-distribution',
                data: JSON.stringify({
                    customerId: customerId,
                    ids: ids
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled", "disabled");
                if (res.isSuccess) {
                    layui.tableRequest.reload("form-resource-list");
                    layui.$('#ids').val('');
                    layui.$('#customer-view-2').val('');
                    layui.layer.closeAll('page');
                    layui.common.alertAutoClose("资源分配成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 导出资源
         * @param {any} url
         */
        export: function (url) {
            var status = -1;
            if (layui.$('#status-view').val() != '') {
                status = layui.$('#status-view').val();
            }
            var channel = '';
            if (layui.$('#channel').length > 0) {
                channel = layui.$('#channel').val().trim();
            }
            var data = {
                channel: channel,
                keywords: layui.$('#keywords').val().trim(),
                customerId: layui.$('#customer-view').val(),
                domainName: layui.$('#domain-view').val(),
                pagePath: layui.$('#page-view').val(),
                startTime: layui.$('#startTime').val(),
                endTime: layui.$('#endTime').val(),
                status: status
            };

            layui.request({
                requestBase: 'form',
                method: 'post',
                url: url,
                data: JSON.stringify(data),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    location.href = res.result;
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 清空资源
         * @param {any} url
         */
        clear: function (url) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: url,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("form-resource-list");
                    layui.common.alertAutoClose("表单资源记录清除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 清除选中客户表单资源
         * */
        clearByCustomerIds: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/res/form-resource/by-customerids/physical-clear',
                data: JSON.stringify({
                    customerIds: data.field.customerIds
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                if (res.isSuccess) {
                    layui.common.alertAutoClose("表单资源记录清除成功");
                    setTimeout(function () { location.reload() }, 2000)
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 清空所有表单资源
         * */
        clearAll: function () {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/res/form-resource/physical-clear-all',
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("表单资源记录清除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },

        /**
         * 绑定工具栏按钮事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该记录吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.formResource.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event == 'edit-followup') {
                    layui.common.openIframe('编辑跟进情况', 500, 380, 'update-followup.html?id=' + data.id);
                }
            });
            layui.table.on('toolbar(list)', function (obj) {
                if (obj.event === 'batch-del') {
                    var checkedList = layui.table.checkStatus(obj.config.id);
                    if (checkedList.data.length == 0) {
                        layui.common.alertAutoClose("请选择需要操作的记录");
                        return;
                    }
                    var names = '';
                    var ids = [];
                    for (var i = 0; i < checkedList.data.length; i++) {
                        if (names != '')
                            names += '，';
                        names += checkedList.data[i].realName;
                        ids.push(checkedList.data[i].id);
                    }
                    var confirmIndex = layui.layer.confirm('确定删除【' + names + '】资源信息吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.formResource.batchDelete(ids);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'batch-distribution') {
                    var checkedList = layui.table.checkStatus(obj.config.id);
                    if (checkedList.data.length == 0) {
                        layui.common.alertAutoClose("请选择需要操作的记录");
                        return;
                    }
                    var names = '';
                    var ids = [];
                    for (var i = 0; i < checkedList.data.length; i++) {
                        if (names != '')
                            names += '，';
                        names += checkedList.data[i].realName;
                        ids.push(checkedList.data[i].id);
                    }
                    layui.$('#ids').val(ids.toString());
                    layui.formResource.initCustomer2();
                    layui.common.openPage('分配资源', 400, 300, '#distribution-dialog');
                }
                else if (obj.event === 'batch-updateuserid') {
                    var checkedList = layui.table.checkStatus(obj.config.id);
                    if (checkedList.data.length == 0) {
                        layui.common.alertAutoClose("请选择需要操作的记录");
                        return;
                    }
                    var ids = [];
                    for (var i = 0; i < checkedList.data.length; i++) {
                        ids.push(checkedList.data[i].id);
                    }
                    layui.$('#resourceIds').val(ids.toString());
                    layui.common.openPage('重新分配资源', 450, 300, '#update-user-dialog');
                }
                else if (obj.event === 'export-form') {
                    var confirmIndex = layui.layer.confirm('确定按当前条件导出表单资源记录吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.layer.close(confirmIndex);
                        layui.formResource.export('/admin/res/form-resource/export');
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'export-form-clue') {
                    var confirmIndex = layui.layer.confirm('确定按当前条件导出表单资源线索吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.layer.close(confirmIndex);
                        layui.formResource.export('/admin/res/form-resource/clue/export');
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'clear') {
                    var customerId = layui.$('#customer-view').val();
                    if (customerId == '') {
                        layui.common.alertAutoClose("请选择需要清空的客户系统");
                        return;
                    }
                    var confirmIndex = layui.layer.confirm('确定清空当前选中客户资源记录吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.layer.close(confirmIndex);
                        layui.formResource.clear('/admin/res/form-resource/clear?customerId=' + customerId);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'physical-clear') {
                    var customerId = layui.$('#customer-view').val();
                    if (customerId == '') {
                        layui.common.alertAutoClose("请选择需要清空的客户系统");
                        return;
                    }
                    var confirmIndex = layui.layer.confirm('确定清空当前选中客户资源记录吗,该操作执行后数据将不可恢复，请谨慎操作！', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.layer.close(confirmIndex);
                        layui.formResource.clear('/admin/res/form-resource/physical-clear?customerId=' + customerId);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        },
        /**
         * 绑定事件
         * */
        bindEvent: function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            //监听查询按钮
            layui.form.on('submit(form-resource-search)', function (data) {
                var field = data.field;
                if (field.status == '') {
                    field.status = -1;
                }
                field.isUpdateLastId = false;
                //执行重载
                layui.tableRequest.reload("form-resource-list", {
                    where: field
                });
            });
            var time1;
            //绑定自动刷新数据事件
            var showTime1;
            var showTime2;
            //监听自动刷新按钮
            layui.form.on('checkbox(auto-refresh)', function (data) {
                var defaultSec = 30;
                if (data.elem.checked) {
                    layui.$('.tips').text('数据将在' + defaultSec + '秒后更新...');
                    time1 = window.setInterval(function () {
                        layui.tableRequest.reload("form-resource-list", {
                            where: data.field
                        });
                        layui.$('.tips').text('数据刷新完成');
                    }, defaultSec * 1000);
                }
                else {
                    layui.$('.tips').text('');
                    window.clearInterval(time1);
                    if (showTime1) {
                        window.clearInterval(showTime1);
                    }
                }
            });
            //分配资源
            layui.form.on('submit(form-distribution-submit)', function (data) {
                layui.formResource.batchDistribution(data);
            });
            layui.form.on('submit(form-distribution-submit-3)', function (data) {
                layui.formResource.updateCustomerId(data);
            });
            layui.form.on('submit(update-saleruserid-submit)', function (data) {
                layui.formResource.batchUpdatUserId(data);
            });
        }
    };
    exports('formResource', func);
});