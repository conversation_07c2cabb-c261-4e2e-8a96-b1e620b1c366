﻿layui.define(['request', 'setter', 'common'], function (exports) {
    var func = {
        initPage: function () {
            if (window.location.host == layui.setter.request.formCustomerHost) {
                var cid = layui.common.getUrlParam('cid') || '';
                if (cid != '') {
                    layui.common.setCookie('admin_cid', cid);
                }
                else {
                    cid = layui.common.getCookie('admin_cid');
                    if (cid == null || cid == '') {
                        location.href = '404.html'
                        return;
                    }
                }
                layui.formAuth.get(cid);
            }
        },
        get: function (cid) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/basis/customer/status/get?id=' + cid,
            }).then(function (res) {
                if (res.isSuccess) {
                    if (res.result.status != 1) {
                        location.href = '404.html'
                    }
                }
                else {
                    location.href = '404.html'
                }
            })
        }
    }

    exports('formAuth', func);
});