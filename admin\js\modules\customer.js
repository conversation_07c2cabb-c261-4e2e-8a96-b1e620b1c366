﻿layui.define(['laytpl', 'setter', 'form', 'request', 'tableRequest', 'common', 'role'], function (exports) {
    var func = {
        /**
        * 获取所有客户列表
        * */
        getAll: function (callbackFunc, isValidate) {
            if (!isValidate) {
                isValidate = false;
            }
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/customer/get-all?isValidate=' + isValidate,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
        * 渲染角色列表
        * @param {any} companyId
        * @param {any} checkedIds
        */
        initRole: function (companyId, selectedId) {
            layui.role.queryByCompanyId(companyId, true, function (res) {
                res.result.unshift({ id: '', name: '请选择分配角色' });
                var getTpl = document.getElementById("role-tpl").innerHTML
                    , view = document.getElementById('role-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (selectedId != undefined) {
                    view.value = selectedId;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过id获取客户信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                setTimeout(function () {
                    parent.location.reload();
                }, 3000);
                return;
            }
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/customer/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=alias]').val(res.result.alias);
                    layui.$('input[name=realName]').val(res.result.realName);
                    layui.$('#isAutoDistribution').val(res.result.isAutoDistribution.toString());
                    layui.$('#status').val(res.result.status);
                    layui.customer.initRole(layui.setter.companyId, res.result.distributionRoleId);
                    layui.form.render('select');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取客户列表
         * */
        query: function () {
            layui.tableRequest.request('form', true, 'customer-list', '/admin/basis/customer/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left', },
                { field: 'alias', title: '系统名称' },
                { field: 'realName', title: '客户姓名' },
                {
                    field: 'status', title: '状态', templet: function (e) {
                        return e.status == 1 ? '正常' : '停用';
                    }
                },
                {
                    field: 'isAutoDistribution', title: '自动分配资源', templet: function (e) {
                        if (e.isAutoDistribution) {
                            return '是【' + e.distributionRoleName + '】';
                        }
                        else {
                            return '否';
                        }
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 180, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', align: 'left', width: 200, toolbar: '#customer-bar' }
            ]);
            //监听表格事件
            layui.customer.tableEvent();
        },
        /**
        * 创建客户
        * */
        create: function (data) {
            if (data.field.isAutoDistribution == "true" && data.field.distributionRoleId == "") {
                layui.common.alertAutoClose("请选择分配的用户角色");
                return false;
            }
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/customer/create',
                data: JSON.stringify({
                    alias: data.field.alias,
                    realName: data.field.realName,
                    isAutoDistribution: data.field.isAutoDistribution,
                    distributionRoleId: data.field.distributionRoleId
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("客户创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑客户信息
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/customer/update',
                data: JSON.stringify({
                    id: data.field.id,
                    alias: data.field.alias,
                    realName: data.field.realName,
                    isAutoDistribution: data.field.isAutoDistribution,
                    distributionRoleId: data.field.distributionRoleId,
                    status: data.field.status
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("客户编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除客户恓
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/customer/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("客户删除成功");
                    layui.company.query();
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑客户信息', 500, 400, 'update.html?companyId=' + layui.setter.companyId + '&id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该客户吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.customer.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'url') {
                    var url = 'http://' + layui.setter.request.formCustomerHost + '/admin/login.html?cid=' + data.id;
                    layui.common.alert('<a target="_blank" href="' + url + '">' + url + '</a> ');
                }
            });
        }
    }

    exports('customer', func);
});