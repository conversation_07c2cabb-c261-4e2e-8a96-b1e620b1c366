﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 60px; }
        .layui-form-item .layui-input-inline { width: 700px; }
        .file-list { width: 100%; margin-top: 5px; }
        .file-list li { width: 100%; line-height: 24px; padding: 3px 10px; box-sizing: border-box; float: left; }
        .file-list li:hover { background: #eee; }
        .file-list li a { font-size: 14px; color: #0094ff; display: inline-block; width: 300px; }
        .file-list li i.layui-icon-link { font-size: 14px; }
        .file-list li i.remove { float: right; cursor: pointer; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">配置名称</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入配置名称（通知组）" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">通知类型</label>
            <div class="layui-input-inline" id="types-view">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">数据范围</label>
            <div class="layui-input-inline">
                <div class="layui-inline">
                    <input type="checkbox" name="isAllSubsidiary" lay-filter="isAllSubsidiary" title="是否接收全部数据消息推送" lay-skin="primary">
                </div>
                <div id="subsidiary-item">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">通知用户</label>
            <div class="layui-input-inline">
                <div id="user-item">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn" lay-filter="create-submit" value="保存">
            </div>
        </div>
    </div>
    <script id="types-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <input type="checkbox" name="type" value="{{item.id}}" title="{{item.name}}" lay-skin="primary">
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.1"></script>
    <script type="text/javascript">
        var xmSel, userXmSel;
        layui.use(['form', 'systemNoticeConfig'], function () {
            layui.systemNoticeConfig.initTypes();
            layui.systemNoticeConfig.initSubsidiary();
            layui.systemNoticeConfig.initUser();

            xmSel = layui.xmSelect.render({
                el: '#subsidiary-item',
                language: 'zn',
                filterable: true,
                tips: '请选择所属组织',
                theme: { color: '#0081ff ' },
                data: [],
                toolbar: { show: true },
                autoRow: true,
            });
            userXmSel = layui.xmSelect.render({
                el: '#user-item',
                language: 'zn',
                filterable: true,
                tips: '请选择推送用户',
                theme: { color: '#0081ff ' },
                data: [],
                toolbar: { show: true },
                autoRow: true,
            });
            layui.form.on('checkbox(isAllSubsidiary)', function (data) {
                if (data.elem.checked) {
                    layui.$('#subsidiary-item').hide();
                }
                else {
                    layui.$('#subsidiary-item').show();
                }
            });
            //监听提交事件
            layui.form.on('submit(create-submit)', function (data) {
                var types = [];
                layui.$(":checkbox[name=type]:checked").each(function () {
                    types.push(layui.$(this).val());
                });
                data.field.types = types;
                if (types.length == 0) {
                    layui.common.alertAutoClose("请选择通知类型");
                    return;
                }
                data.field.isAllSubsidiary = layui.$('input[name=isAllSubsidiary]').is(':checked');
                data.field.subsidiaryIds = [];
                var subsidiaryIds = xmSel.getValue('value');
                if (subsidiaryIds.length > 0) {
                    data.field.subsidiaryIds = subsidiaryIds;
                }
                if (!data.field.isAllSubsidiary && subsidiaryIds.length == 0) {
                    layui.common.alertAutoClose("请选择数据范围");
                    return;
                }
                data.field.userIds = [];
                var userIds = userXmSel.getValue('value');
                if (userIds.length > 0) {
                    data.field.userIds = userIds;
                }
                if (userIds.length == 0) {
                    layui.common.alertAutoClose("请选择推送用户");
                    return;
                }
                layui.systemNoticeConfig.create(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>