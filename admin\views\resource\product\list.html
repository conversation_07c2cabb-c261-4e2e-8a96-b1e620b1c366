﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 200px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属组织</label>
                        <div class="layui-input-inline">
                            <select name="subsidiaryId" id="subsidiary-view" lay-search></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="请输入产品名称" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">类型</label>
                        <div class="layui-input-inline">
                            <select name="type" id="type">
                                <option value="">请选择产品类型</option>
                                <option value="1">证券投资培训课程</option>
                                <option value="2">证券投资顾问产品</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="status" id="status">
                                <option value="">请选择产品状态</option>
                                <option value="1">正常</option>
                                <option value="2">下架</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="product-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list product-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="product-list" lay-filter="list"></table>
                <script type="text/html" id="product-bar">
                    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="gen">生成链接</a>
                    <button class="layui-btn layui-btn-xs" id="layuidropdown_{{d.id}}" lay-filter="ft{{d.id}}"
                            lay-dropdown="{align:'center',menus: [{layIcon:'layui-icon-survey',txt: '审核', event:'audit'}, {layIcon: 'layui-icon-edit',txt: '编辑', event:'edit'},{layIcon:'layui-icon-unlink',txt: '赠送', event:'gen-give'},  {layIcon: 'layui-icon-delete', txt: '删除', event:'del'}, {layIcon: 'layui-icon-username',txt: '投顾', event:'tg'}]}">
                        <span>更多</span>
                        <i class="layui-icon layui-icon-triangle-d"></i>
                    </button>
                </script>
            </div>
        </div>
    </div>
    <script id="subsidiary-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=2.9"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'product'], function () {
            layui.product.initSubsidiary();
            layui.product.query();
            //监听查询按钮
            layui.form.on('submit(product-search)', function (data) {
                var field = data.field;
                if (field.type == '') {
                    field.type = -1;
                }
                if (field.status == '') {
                    field.status = -1;
                }
                //执行重载
                layui.tableRequest.reload("product-list", {
                    where: field
                });
            });
            //打开添加弹框
            layui.$('.product-create').click(function () {
                location.href = 'create.html';
            });
        });
    </script>
</body>
</html>