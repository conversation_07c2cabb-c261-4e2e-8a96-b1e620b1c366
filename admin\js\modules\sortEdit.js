layui.define(['laytpl', 'laypage', 'request', 'jquery', 'common', 'liveAccount'], function (exports) {
    var func = {
        /**
         * 获取直播巡查记录列表
         * */
        query: function (query, isLimit, formData) {
            if (!isLimit) {
                formData = {
                    pageIndex: 1,
                    pageSize: 10,
                }
            }
            formData = Object.assign(formData, query)
            layui.request({
                method: 'post',
                url: '/admin/workwx/ad-page/query',
                headers: { 'Content-Type': 'application/json' },
                data: JSON.stringify(
                    formData
                )
            }).then(function (res) {
                let getTpl = document.getElementById('sort-table-tpl').innerHTML; // 获取模板字符
                let elemView = document.getElementById('sort-table-view'); // 视图对象
                if (res.isSuccess) {
                    if (res.result.items.length == 0) {
                        elemView.innerHTML = `<div class="emty">暂无落地页</div>`;
                        layui.jquery(".emty").css("display", "block")
                        layui.jquery("#pagetotal").css("display", "none")
                    } else {
                        let laytpl = layui.laytpl;
                        // 渲染
                        let data = res.result.items;
                        layui.jquery("#pagetotal").css("display", "block")
                        // 渲染并输出结果
                        laytpl(getTpl).render(data, function (str) {
                            elemView.innerHTML = str;
                        });
                        if (!isLimit) {
                            layui.laypage.render({
                                elem: 'pagetotal' //注意，这里的 test1 是 ID，不用加 # 号
                                , count: res.result.totalCount //数据总数，从服务端得到
                                , layout: ['count', 'limit', 'prev', 'page', 'next', 'skip'], // 功能布局
                                jump: function (obj, first) {
                                    if (!first) {
                                        layui.sortEdit.query(keywords, true, { pageIndex: obj.curr, pageSize: obj.limit })
                                    }

                                }
                            });
                        }
                        layui.jquery(".item").hover(
                            function () {
                                layui.jquery(this).addClass("item_hover")
                                layui.jquery(this).siblings().removeClass("item_hover")
                            },
                            function () {
                                layui.jquery(".item").removeClass("item_hover")
                            }
                        )
                        layui.jquery(".menu_item").hover(
                            function () {
                                layui.jquery(this).addClass("menu_item_hover")
                                layui.jquery(this).siblings().removeClass("menu_item_hover")
                            },
                            function () {
                                layui.jquery(".menu_item").removeClass("menu_item_hover")
                            }
                        )
                        layui.jquery(".preItem").hover(
                            function () {
                                layui.jquery(this).addClass("active")
                                layui.jquery(this).siblings().removeClass("active")
                            },
                            function () {
                                layui.jquery(".preItem").removeClass("active")
                            }
                        )
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 关联加粉方案
         * @param {*} data 
         */
        updateState: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            var id = layui.common.getUrlParam('id');
            data.field.id = id;
            layui.request({
                method: 'post',
                url: '/admin/workwx/ad-page/caseid/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.sortEdit.query();
                    parent.layui.common.alertAutoClose("关联成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 审核
         * @param {} data 
         */
        examine: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            var id = layui.common.getUrlParam('id');
            data.field.id = id;
            layui.request({
                method: 'post',
                url: '/admin/workwx/ad-page/audit-status/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.sortEdit.query();
                    parent.layui.common.alertAutoClose("审核状态更改成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
    }
    exports('sortEdit', func);
});