﻿layui.define(['laytpl', 'request', 'tableRequest', 'common'], function (exports) {
    var func = {
        /**
         * 获取用户收货地址列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'useraddress-list', '/admin/marketing/user-address/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left', width: 80 },
                { field: 'productServiceTime', title: '购买产品', width: 200 },
                { field: 'name', title: '姓名', width: 100 },
                { field: 'mobile', title: '手机号', width: 150 },
                {
                    field: 'isSign', title: '是否签名', width: 100, templet: function (e) {
                        return e.isSign ? '是' : '否';
                    }
                },
                {
                    title: '地址', templet: function (e) {
                        return e.province + e.city + e.address;
                    }
                },
                {
                    field: 'createdAt', title: '提交时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                }
            ]);
        },
        /**
         * 导出用户收货地址
         * */
        export: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/marketing/user-address/export',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                if (res.isSuccess) {
                    location.href = res.result;
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
    }

    exports('userAddress', func);
});