﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        /* .layui-form-item .layui-input-inline { width: 300px; }*/
        .layui-form-label { width: 180px; }
        .layui-code { width: 800px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-tab-title" style="height: auto">
                <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" style="margin:15px 0 15px 30px;" onclick="history.back(-1)">
                    <i class="layui-icon layui-icon-left"></i>返回用户列表
                </button>
            </div>
            <div class="layui-card-body">

                <div class="layui-form-item">
                    <label class="layui-form-label">数据ID:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="id"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">所属企微:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="workWxApp"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">用户OpenID:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="openId"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">用户UnionID:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="unionId"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">使用小程序:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="miniApp"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">字节账户ID:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="clueAccountId"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">字节微信线索ID:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="wechatClueId"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">字节线索ID:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="clueId"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">字节小程序ID:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="appletId"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">是否已添加微信:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="isAddWx"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">外部联系人:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="externalUser"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">企业微信回调自定义参数:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="workState"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">企业微信回调时间:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="callbackTime"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">企业微信回调内容:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid">
                            <pre class="layui-code" id="callbackContent"></pre>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">是否已回传:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="isReport"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">回传结果:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid">
                            <pre class="layui-code" id="reportEvent"></pre>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">创建时间:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="createdAt"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['code', 'miniUser'], function () {
            layui.miniUser.get();
        });
    </script>
</body>
</html>