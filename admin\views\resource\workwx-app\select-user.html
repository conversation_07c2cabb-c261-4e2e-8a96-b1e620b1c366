﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        ::-webkit-scrollbar { width: 0px; }
        .layui-fluid { padding: 0; }
        .mid-c { display: flex; padding: 15px; box-sizing: border-box; }
        .mid-c .left { box-sizing: border-box; border-right: 1px solid #eee; width: 350px; overflow-x: auto; height: 500px; }
        .mid-c .right { box-sizing: border-box; width: 100%; }
        .layui-tree-set-active > .layui-tree-entry { background: #f2f2f2; }
        .layui-tree-set-active > .layui-tree-entry:hover { background: #f2f2f2; }
        .link { color: #50b1fb; }
        .layui-tree-entry { height: auto; width: fit-content; }
        .mid-c .right .user-box { float: left; width: 280px; border: 1px solid #eee; height: 500px; margin-left: 20px; }
        .mid-c .right .user-box .top { padding: 10px; box-sizing: border-box; border-bottom: 1px solid #eee }
        .mid-c .right .user-box .bottom { float: left; padding: 6px 10px; box-sizing: border-box; height: 460px; overflow-x: auto; }
        .mid-c .right .user-box .bottom p { width: 100%; margin: 6px 0; float: left; }
        .btn-list { width: 100%; text-align: center; background: #fff; border-top: 1px solid #eee; padding: 10px 0; position: fixed; left: 0; bottom: 0 }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="mid-c">
                <div class="left">
                    <div id="department-view"></div>
                </div>
                <div class="right layui-form">
                    <div class="user-box center-user">
                        <div class="top">
                            <input type="checkbox" class="left_user_all" name="left_user_all" lay-filter="left_user_all" lay-skin="primary" title="全选">
                        </div>
                        <div class="bottom" id="workwx-user-view">
                        </div>
                    </div>
                    <div class="user-box right-user" style="margin-right:0;">
                        <div class="top">
                            已选择员工<span class="num"></span>：
                        </div>
                        <div class="bottom" id="workwx-user-select-view">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="btn-list">
        <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="wework-user-select" value="确认选择">
    </div>
    <script id="workwx-user-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <p><input type="checkbox" class="left_user_item" data-name="{{item.name}}" data-useridformat="{{item.userIdFormat}}" name="left_user_item" lay-filter="left_user_item" value="{{item.userId}}" lay-skin="primary" title="{{item.name}}（{{item.userIdFormat}}）" {{item.isChecked ? 'checked="checked"':''}}></p>
        {{#  }); }}
    </script>
    <script id="workwx-user-select-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <p class="right_user_item_{{item.userId}}" data-userid="{{item.userId}}" data-name="{{item.name}}">{{item.name}}（{{item.userIdFormat}}）</p>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        var weWorkUsers = [];
        layui.use(['workWxApp'], function () {
            var workWxAppId = layui.common.getUrlParam('workWxAppId') || '';
            if (workWxAppId == '') {
                layui.common.alertAutoClose("企微应用id有误");
                return;
            }
            layui.workWxApp.getDepartmentTree(workWxAppId);
            layui.workWxApp.getWeWorkUser(workWxAppId);
            //全选
            layui.form.on('checkbox(left_user_all)', function (data) {
                var isChecked = data.elem.checked;
                layui.$(".left_user_item").prop("checked", isChecked);
                var itemLength = layui.$(".left_user_item");
                var ids = [];
                for (var i = 0; i < itemLength.length; i++) {
                    var value = layui.$(".left_user_item").eq(i).val();
                    ids.push(value);
                    if (isChecked) {
                        var name = layui.$(".left_user_item").eq(i).data('name');
                        var userIdFormat = layui.$(".left_user_item").eq(i).data('useridformat');
                        if (layui.$('.right_user_item_' + value).length == 0) {
                            var option = '<p class="right_user_item_' + value + '" data-userid="' + value + '" data-name="' + name + '">' + name + '（' + userIdFormat + '）</p>';
                            layui.$('#workwx-user-select-view').append(option);
                        }
                    }
                    else {
                        layui.$('.right_user_item_' + value).remove();
                    }
                }
                for (var i = 0; i < weWorkUsers.length; i++) {
                    var exist = ids.indexOf(weWorkUsers[i].userId);
                    if (exist > -1) {
                        weWorkUsers[i].isChecked = isChecked;
                    }
                }
                layui.form.render('checkbox');
                layui.$('.right-user .top .num').text('(' + layui.$('.right-user .bottom p').length + ')');
            });
            //单选
            layui.form.on('checkbox(left_user_item)', function (data) {
                var isChecked = data.elem.checked;
                if (isChecked) {
                    var name = layui.$(data.elem).data('name');
                    var userIdFormat = layui.$(data.elem).data('useridformat');
                    if (layui.$('.right_user_item_' + data.value).length == 0) {
                        var option = '<p class="right_user_item_' + data.value + '" data-userid="' + data.value + '" data-name="' + name + '">' + name + '（' + userIdFormat + '）</p>';
                        layui.$('#workwx-user-select-view').append(option);
                    }

                } else {
                    layui.$('.right_user_item_' + data.value).remove();
                }
                for (var i = 0; i < weWorkUsers.length; i++) {
                    if (weWorkUsers[i].userId == data.value) {
                        weWorkUsers[i].isChecked = isChecked;
                    }
                }
                var allIsChecked = layui.$('.left_user_item:checked').length == layui.$('.left_user_item').length;
                layui.$(".left_user_all").prop("checked", allIsChecked);
                layui.$('.right-user .top .num').text('(' + layui.$('.right-user .bottom p').length + ')');
                layui.form.render('checkbox');
            });
            //提交数据
            layui.form.on('submit(wework-user-select)', function (data) {
                var res = [];
                var items = layui.$('.right-user .bottom p');
                for (var i = 0; i < items.length; i++) {
                    var item = layui.$('.right-user .bottom p').eq(i);
                    res.push({ name: item.data('name'), value: item.data('userid') });
                }
                window.top.postMessage(res, '*');
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>