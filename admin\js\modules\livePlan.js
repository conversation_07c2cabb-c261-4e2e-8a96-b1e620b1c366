﻿layui.define(['laytpl', 'request', "jquery", 'tableRequest', 'uploadFile', 'common', 'liveAccount', "uploadSsoFile", "media"], function (exports) {
    var func = {
        /**
        * 渲染账号列表
        * */
        initAccount: function (id) {
            layui.liveAccount.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择直播账号' });
                for (var i = 0; i < res.result.length; i++) {
                    var bigType = '';
                    if (res.result[i].bigType == 1) {
                        bigType = '【抖音】';
                    }
                    else if (res.result[i].bigType == 2) {
                        bigType = '【视频号】';
                    }
                    else if (res.result[i].bigType == 3) {
                        bigType = '【快手】';
                    }
                    res.result[i].name = bigType + res.result[i].name;
                }
                var getTpl = document.getElementById("account-tpl").innerHTML
                    , view = document.getElementById('account-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    view.value = id;
                }
                layui.form.render('select');
            });
        },
        /**
       * 上传附件
       * */
        initFile: function () {
            var files = [];
            layui.uploadFile('resource', 'uploadAnnex', '/common/file/public/put?folder=files/live/annex', function (res, index, upload) {
                if (res.isSuccess) {
                    for (var i = 0; i < files.length; i++) {
                        if (files[i].index == index) {
                            files[i].url = res.result;
                            layui.$('.file-list').append('<li id="li_' + index + '"><a href="' + files[i].url + '" target="_blank"><i class="layui-icon layui-icon-link"></i> ' + files[i].name + '</a><span data-size="' + files[i].size + '">' + (files[i].size / 1024).toFixed(1) + 'KB</span> <i class="layui-icon layui-icon-delete remove" onclick="layui.$(this).parent(\'li\').remove()"></i></li>');
                        }
                    }
                    console.log(files);
                }
                else {
                    layui.common.alert(res.message, 2);
                }
            }, function (obj) {
                obj.preview(function (index, file, result) {
                    files.push({ index: index, name: file.name, size: file.size, url: '' })
                });
            });
        },
        /**
         * 通过id获取单个直播计划
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/live/plan/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=title]').val(res.result.title);
                    layui.$('input[name=address]').val(res.result.address);
                    layui.$('#content').val(res.result.content);
                    layui.$('#startTime').val(layui.common.timeFormat(res.result.startTime));
                    layui.$('#endTime').val(layui.common.timeFormat(res.result.endTime));
                    if (res.result.annex != null && res.result.annex.length > 0) {
                        for (var i = 0; i < res.result.annex.length; i++) {
                            layui.$('.file-list').append('<li id="li_' + i + '"><a href="' + res.result.annex[i].url + '" target="_blank"><i class="layui-icon layui-icon-link"></i> ' + res.result.annex[i].name + '</a><span data-size="' + res.result.annex[i].size + '">' + (res.result.annex[i].size / 1024).toFixed(1) + 'KB</span> <i class="layui-icon layui-icon-delete remove" onclick="layui.$(this).parent(\'li\').remove()"></i></li>');
                        }
                    }
                    layui.livePlan.initFile();
                    layui.livePlan.initAccount(res.result.accountId);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取直播计划列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'plan-list', '/admin/live/plan/query', 'application/json', [
                { field: 'subsidiaryName', title: '所属组织' },
                { field: 'accountName', title: '直播账号', width: 200 },
                { field: 'title', title: '直播主题' },
                {
                    field: 'startTime', title: '直播时间', width: 310, templet: function (e) {
                        return layui.common.timeFormat(e.startTime) + ' 至 ' + layui.common.timeFormat(e.endTime);
                    }
                },
                { field: 'address', title: '直播地点' },
                { field: 'content', title: '直播内容' },
                {
                    field: 'content', title: '上传状态', templet: function (e) {
                        return e.videoUrl ? "已上传" : "未上传";
                    }
                },
                { field: 'videoUrl', title: '视频地址', width: 150, templet: '<div><a Target="_blank" href="{{= d.videoUrl }}" class="layui-table-link">{{= d.videoUrl }}</a></div>' },
                { field: 'userName', title: '提交人' },
                { fixed: 'right', title: '操作', width: 240, align: 'left', toolbar: '#plan-bar' }
            ], {}, '#topToolBar');
            //监听表格事件
            layui.livePlan.tableEvent();
        },
        /**
        * 创建单个直播计划
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/live/plan/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("plan-list");
                    parent.layui.common.alertAutoClose("直播计划创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑单个直播计划
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/live/plan/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("plan-list");
                    parent.layui.common.alertAutoClose("直播计划编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除单个直播账号
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/live/plan/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("plan-list");
                    layui.common.alertAutoClose("直播计划删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 导入直播计划
        * */
        import: function (data) {
            if (data.field.file == '') {
                layui.common.alertAutoClose("请选择文件");
                return;
            }
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            var formData = new FormData(layui.$('#uploadForm')[0]);
            layui.request({
                method: 'post',
                url: '/admin/live/plan/import',
                data: formData
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose('导入完成');
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 导出直播计划
         * */
        export: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/live/plan/export',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                if (res.isSuccess) {
                    location.href = res.result;
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑直播计划', 720, 620, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'add') {
                    layui.common.openIframe('新增巡查记录', 720, 450, '../live-patrol/create.html?planId=' + data.id);
                }

                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该直播计划吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.livePlan.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'update') {
                    localStorage.setItem("tableId", obj.data.id)
                    layui.jquery("#uploadVideo").click()
                }
                else if (obj.event === 'play') {
                    window.open(obj.data.videoUrl);
                }
            });
            layui.table.on('toolbar(list)', function (obj) {
                if (obj.event == 'import') {
                    layui.common.openIframe('导入直播计划', 600, 350, 'import.html');
                }
                else if (obj.event == 'create') {
                    layui.common.openIframe('创建直播计划', 720, 610, 'create.html');
                }
            });
        }
    }
    exports('livePlan', func);
});