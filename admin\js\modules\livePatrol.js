﻿layui.define(['laytpl', 'request', 'tableRequest', 'common', 'liveAccount'], function (exports) {
    var func = {
        /**
       * 渲染账号列表
       * */
        initAccount: function (id) {
            layui.liveAccount.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择直播账号' });
                for (var i = 0; i < res.result.length; i++) {
                    var bigType = '';
                    if (res.result[i].bigType == 1) {
                        bigType = '【抖音】';
                    }
                    else if (res.result[i].bigType == 2) {
                        bigType = '【视频号】';
                    }
                    else if (res.result[i].bigType == 3) {
                        bigType = '【快手】';
                    }
                    res.result[i].name = bigType + res.result[i].name;
                }
                var getTpl = document.getElementById("account-tpl").innerHTML
                    , view = document.getElementById('account-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    view.value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过id获取单个直播巡查记录
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/live/patrol/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=opName]').val(res.result.opName);
                    layui.$('input[name=archiveUrl]').val(res.result.archiveUrl);
                    layui.$('#isViolation').val(res.result.isViolation ? 'true' : 'false');
                    layui.$('#startTime').val(layui.common.timeFormat(res.result.startTime));
                    layui.$('#endTime').val(layui.common.timeFormat(res.result.endTime));
                    layui.$('#status').val(res.result.status);
                    layui.$('#violationContent').val(res.result.violationContent);
                    // layui.$('#remark').val(res.result.remark);
                    layui.form.render('select');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取直播巡查记录列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'patrol-list', '/admin/live/patrol/query', 'application/json', [
                { field: 'subsidiaryName', title: '所属组织' },
                { field: 'accountName', title: '直播账号', width: 200 },
                { field: 'planTitle', title: '直播主题' },
                {
                    field: 'startTime', title: '巡查时间', width: 310, templet: function (e) {
                        return layui.common.timeFormat(e.startTime) + ' 至 ' + layui.common.timeFormat(e.endTime);
                    }
                },
                { field: 'opName', title: '巡查人' },
                {
                    field: 'status', title: '处理结果', templet: function (e) {
                        return e.status == 2 ? "已处理" : "未处理";
                    }
                },
                { field: 'archiveUrl', title: '留痕存档地址' },
                { fixed: 'right', title: '操作', width: 180, align: 'left', toolbar: '#patrol-bar' }
            ]);
            //监听表格事件
            layui.livePatrol.tableEvent();
        },
        /**
        * 创建单个直播巡查记录
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/live/patrol/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("patrol-list");
                    parent.layui.common.alertAutoClose("直播巡查记录创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑单个直播巡查记录
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/live/patrol/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("patrol-list");
                    parent.layui.common.alertAutoClose("直播巡查记录编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 处理单个直播巡查记录
         * @param {any} data
         */
        updateState: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            var id = layui.common.getUrlParam('id');
            data.field.id = id;
            layui.request({
                method: 'post',
                url: '/admin/live/patrol/status/update ',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("patrol-list");
                    parent.layui.common.alertAutoClose("处理成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除单个直播巡查记录
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/live/patrol/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("patrol-list");
                    layui.common.alertAutoClose("直播巡查记录删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
          * 导出巡查记录
          * */
        export: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/live/patrol/export',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                if (res.isSuccess) {
                    location.href = res.result;
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑直播巡查记录', 720, 450, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该直播巡查记录吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.livePatrol.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                } else if (obj.event === 'editStatus') {
                    layui.common.openIframe('处理直播巡查记录', 520, 320, 'editStatus.html?id=' + data.id);
                }
            });
        }
    }
    exports('livePatrol', func);
});