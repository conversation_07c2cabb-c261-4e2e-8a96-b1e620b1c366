﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        /* .layui-form-item .layui-input-inline { width: 300px; }*/
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属企微</label>
                        <div class="layui-input-inline">
                            <select name="workWxAppId" id="workwx-app-view" lay-filter="workwx-app-id">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">加粉方案</label>
                        <div class="layui-input-inline">
                            <select name="kfCaseId" id="kf-case-view" lay-search>
                                <option value="">请选择加粉方案</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">添加来源</label>
                        <div class="layui-input-inline">
                            <select name="source" id="source">
                                <option value="">请选择添加来源</option>
                                <option value="kf">客服链路</option>
                                <option value="mini">小程序链路</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">开口状态</label>
                        <div class="layui-input-inline">
                            <select name="workWxOpend" id="workWxOpend">
                                <option value="">请选择开口状态</option>
                                <option value="1">未开口</option>
                                <option value="2">已开口</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">删除状态</label>
                        <div class="layui-input-inline">
                            <select name="workWxDeleted" id="workWxDeleted">
                                <option value="">请选择删除状态</option>
                                <option value="1">未删除</option>
                                <option value="2">已删除</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="名称/ID/分配用户" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">创建日期</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" />
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="external-contact-search">
                                <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="external-contact-list" lay-filter="list"></table>
            </div>
        </div>
    </div>

    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="kf-case-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['externalContact'], function () {
            layui.externalContact.initApp();
            layui.externalContact.query();
            layui.externalContact.bindEvent();
        });
    </script>
</body>
</html>