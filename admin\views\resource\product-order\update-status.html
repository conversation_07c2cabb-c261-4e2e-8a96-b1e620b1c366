﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 80px; }
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-inline">
                <select name="status" id="status" lay-verify="required">
                    <option value="">请选择订单状态</option>
                    <option value="1">正常</option>
                    <option value="2">作废</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-inline">
                <textarea type="text" name="remark" id="remark" placeholder="请输入订单备注信息" autocomplete="off" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="order-status-submit" value="确认保存">
            </div>
        </div>
    </div>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['productOrder'], function () {
            layui.productOrder.getBrief();
            //监听提交事件
            layui.form.on('submit(order-status-submit)', function (data) {
                layui.productOrder.updateStatus(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>