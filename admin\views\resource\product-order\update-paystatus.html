﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 520px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
        .img-item { display: inline-block; position: relative; margin-right: 5px; }
        .img-item span.remove { position: absolute; cursor: pointer; right: 0; top: 0; display: inline-block; width: 15px; height: 15px; text-align: center; line-height: 15px; background: #000; opacity: 0.5; color: #fff }
        .pay-info { margin-bottom: 10px; margin-bottom: 10px; float: left; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">订单编号</label>
            <div class="layui-input-inline">
                <input type="text" id="orderNo" name="orderNo" lay-verify="required" placeholder="请输入订单编号" readonly="readonly" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">审核状态</label>
            <div class="layui-input-inline">
                <select name="payStatus" id="payStatus" lay-verify="required">
                    <option value="">请选择审核状态</option>
                    <option value="2">通过</option>
                    <option value="11">驳回</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">支付时间</label>
            <div class="layui-input-inline">
                <input type="datetime" id="payTime" name="payTime" lay-verify="required" placeholder="请选择支付时间" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">支付方式/金额</label>
            <div class="pay-items" style="width:520px;float:left;">
                <div class="pay-item-list">
                </div>

                <div class="pay-btn " style="width:100%;">
                    <div class="layui-input-inline" style="width:auto;">
                        <button type="button" class="layui-btn layui-btn-primary add-pay">
                            <i class="layui-icon"></i> 添加支付项
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">驳回原因</label>
            <div class="layui-input-inline">
                <textarea type="text" id="financialRemark" name="financialRemark" placeholder="驳回原因，审核驳回时填写" autocomplete="off" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="order-pay-status-submit" value="确认修改">
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=3.2"></script>
    <script type="text/javascript">
        var payTypeXmSel;
        layui.use(['laydate', 'productOrder'], function () {
            layui.productOrder.getBrief('update-pay');
            //监听提交事件
            layui.form.on('submit(order-pay-status-submit)', function (data) {
                //var payTypes = payTypeXmSel.getValue('value');
                //if (payTypes.length > 0) {
                //    data.field.payTypes = payTypes;
                //}
                var payList = layui.$('.pay-info');
                if (payList.length == 0) {
                    layui.common.alertAutoClose("请选择支付项");
                    return;
                }
                var payItems = [];
                for (var i = 0; i < payList.length; i++) {
                    var payType = payList.eq(i).find('.payType').val();
                    var payAmount = payList.eq(i).find('.payAmount').val();
                    var thirdPartyOrderNo = payList.eq(i).find('.thirdPartyOrderNo').val();
                    var imgList = payList.eq(i).find('.img-list .img-item');
                    var transferVoucher = '';
                    for (var j = 0; j < imgList.length; j++) {
                        if (transferVoucher != '') {
                            transferVoucher += ';';
                        }
                        transferVoucher += imgList.eq(j).find('img').attr('src');
                    }
                    payItems.push({ payType: payType, payAmount: payAmount, transferVoucher: transferVoucher, thirdPartyOrderNo: thirdPartyOrderNo })
                }
                data.field.payItems = payItems;
                layui.productOrder.updatePayStatus(data);
                return false; //阻止表单跳转
            });
            layui.laydate.render({
                elem: '#payTime',
                type: 'datetime'
            });

            //添加行
            layui.$(document).on("click", '.add-pay', function () {
                var uploadId = layui.common.generateGuid();
                if (layui.$('#uploadImg_' + uploadId).length > 0) {
                    uploadId = layui.common.generateGuid();
                }
                var payTypeArr = layui.$.grep(layui.setter.payTypeArr, function (item) {
                    return !item.isOnline && item.id != 300;
                });
                payTypeArr.unshift({ id: '', name: '支付方式' });
                var menuTpl = '<div class="pay-info pay-' + uploadId + '">';
                menuTpl += '<div class="layui-input-inline" style="width:100px;">';
                menuTpl += '<select name="payType" class="payType" lay-verify="required" lay-search>';
                for (var i = 0; i < payTypeArr.length; i++) {
                    menuTpl += '<option value="' + payTypeArr[i].id + '">' + payTypeArr[i].name + '</option>';
                }
                menuTpl += '</select>';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:90px;">';
                menuTpl += '<input type="text" name="thirdPartyOrderNo" lay-verify="required" placeholder="支付单号" autocomplete="off" class="layui-input thirdPartyOrderNo">';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:80px;">';
                menuTpl += '<input type="text" name="payAmount" lay-verify="required" placeholder="实付金额" autocomplete="off" class="layui-input payAmount">';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:auto;">';
                menuTpl += '<button type="button" class="layui-btn layui-btn-primary " id="uploadImg_' + uploadId + '"><i class="layui-icon"></i>转账凭证</button>';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline remove-pay-item" style="width:auto;">';
                menuTpl += '<button type="button" class="layui-btn layui-btn-primary remove-pay"><i class="layui-icon"></i></button>';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline img-list" style="width:auto;">';
                menuTpl += '</div>';
                menuTpl += '</div>';

                layui.$('.pay-item-list').append(menuTpl);
                layui.form.render('select');

                layui.uploadFile('resource', 'uploadImg_' + uploadId, '/common/file/public/put?folder=admin/order/transfer-voucher', function (res) {
                    if (res.isSuccess) {
                        layui.$('.pay-' + uploadId).find('.img-list').append('<div class="img-item"><span class="remove" onclick="removeImg(this)">x</span><a href="' + res.result + '" target="_blank"> <img src="' + res.result + '" style="max-height: 35px"></a></div>');
                        layui.common.alertAutoClose("上传成功");
                    }
                    else {
                        layui.common.alert(res.message, 2);
                    }
                });
            });

            //删除行
            layui.$(document).on("click", '.remove-pay', function () {
                layui.$(this).parent().parent('.pay-info').remove();
            });
        });
        var removeImg = function (obj) {
            obj.parentNode.parentNode.removeChild(obj.parentNode);
        }
    </script>
</body>
</html>