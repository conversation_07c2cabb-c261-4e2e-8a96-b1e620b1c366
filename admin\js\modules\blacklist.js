﻿layui.define(['laytpl', 'form', 'request', 'tableRequest', 'common', 'customer'], function (exports) {
    var func = {
        /**
         * 渲染客户列表
         * @param {any} page
         * @param {any} selectedId
         */
        initCustomer: function (page, selectedId) {
            layui.customer.getAll(function (res) {
                res.result.unshift({ id: '', alias: (page == 'list' ? '请选择客户' : '全部') });
                var getTpl = document.getElementById("customer-tpl").innerHTML
                    , view = document.getElementById('customer-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (selectedId != undefined) {
                    view.value = selectedId;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过id获取黑名单信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                setTimeout(function () {
                    parent.location.reload();
                }, 3000);
                return;
            }
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/blacklist/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=val]').val(res.result.val);
                    layui.$('#type').val(res.result.type);
                    layui.$('#tips').val(res.result.tips);
                    layui.blacklist.initCustomer('update', res.result.customerId);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取黑名单列表
         * */
        query: function () {
            layui.tableRequest.request('form', true, 'blacklist-list', '/admin/basis/blacklist/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left', width: 80 },
                { field: 'customerName', title: '绑定客户' },
                {
                    field: 'type', title: '类型', templet: function (e) {
                        return e.type == 1 ? 'IP' : '手机号'
                    }
                },
                { field: 'val', title: 'IP/手机号' },
                { field: 'tips', title: '拦截提示' },
                { field: 'opUserName', title: '最后操作人' },
                {
                    field: 'createdAt', title: '创建时间', templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', align: 'left', toolbar: '#blacklist-bar' }
            ]);
            //监听表格事件
            layui.blacklist.tableEvent();
        },
        /**
        * 创建单个黑名单信息
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/blacklist/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑黑名单信息
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/blacklist/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除黑名单信息
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/blacklist/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("删除成功");
                    layui.tableRequest.reload("blacklist-list");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑黑名单信息', 600, 450, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该记录吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.blacklist.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('blacklist', func);
});