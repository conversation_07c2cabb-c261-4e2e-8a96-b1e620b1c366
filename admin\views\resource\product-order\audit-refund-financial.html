﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 400px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 10px 30px 0 0;">
        <input id="orderId" name="orderId" type="hidden" />
        <div class="layui-form-item" style="margin-bottom:0">
            <label class="layui-form-label">客户信息</label>
            <div class="layui-input-inline">
                <label class="layui-form-mid layui-word-aux" id="customerInfo"></label>
            </div>
        </div>
        <div class="layui-form-item" style="margin-bottom:0">
            <label class="layui-form-label">产品信息</label>
            <div class="layui-input-inline">
                <label class="layui-form-mid layui-word-aux" id="productInfo"></label>
            </div>
        </div>
        <div class="layui-form-item" style="margin-bottom:0">
            <label class="layui-form-label">订单号</label>
            <div class="layui-input-inline">
                <label class="layui-form-mid layui-word-aux" id="orderInfo" style="width:400px;word-break:break-word;"></label>
            </div>
        </div>
        <div class="layui-form-item" style="margin-bottom:0">
            <label class="layui-form-label">退款金额</label>
            <div class="layui-input-inline">
                <label class="layui-form-mid layui-word-aux" id="amount"></label>
            </div>
        </div>

        <div class="layui-form-item" style="margin-bottom:0">
            <label class="layui-form-label">银行卡号</label>
            <div class="layui-input-inline">
                <label class="layui-form-mid layui-word-aux" id="bankCardNo"></label>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">开户行</label>
            <div class="layui-input-inline">
                <label class="layui-form-mid layui-word-aux" id="openBankBranch"></label>
            </div>
        </div>
        <div class="layui-form-item sg-refund" style="display:none;">
            <label class="layui-form-label" style="color:red">提示：</label>
            <div class="layui-input-inline">
                <label class="layui-form-mid layui-word-aux" style="color:red!important">当前订单为线上支付，点击确认退款按钮将直接退款至用户账户，请谨慎操作！</label>
            </div>
        </div>
        <div class="layui-form-item auto-refund">
            <label class="layui-form-label">退款方式</label>
            <div class="layui-input-inline">
                <select name="refundType" id="refundType" lay-verify="required">
                    <option value="">请选择退款方式</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item auto-refund">
            <label class="layui-form-label">第三方订单号</label>
            <div class="layui-input-inline">
                <input type="datetime" name="thirdPartyOrderNo" placeholder="退款微信/支付宝/银行转账单号" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item auto-refund">
            <label class="layui-form-label">退款状态</label>
            <div class="layui-input-inline">
                <select name="status" id="status" lay-verify="required">
                    <option value="">请选择退款状态</option>
                    <option value="3">退款成功</option>
                    <option value="4">退款失败</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item auto-refund">
            <label class="layui-form-label">退款备注</label>
            <div class="layui-input-inline">
                <textarea type="text" name="remark" placeholder="请输入退款备注信息" autocomplete="off" class="layui-textarea" style="min-height:60px"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="order-refund-submit" value="确认退款">
            </div>
        </div>
    </div>
    <script id="refund-type-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['productOrder'], function () {
            var arr = layui.$.grep(layui.setter.payTypeArr, function (item) {
                return !item.isOnline && item.id != 300;
            });
            arr.unshift({ id: '', name: '请选择退款方式' });
            var getTpl = document.getElementById("refund-type-tpl").innerHTML
                , view = document.getElementById('refundType');
            layui.laytpl(getTpl).render(arr, function (html) {
                view.innerHTML = html;
            });

            layui.form.render('select');
            layui.productOrder.getRefundOrder();
            //监听提交事件
            layui.form.on('submit(order-refund-submit)', function (data) {
                if (data.field.refundType == '') {
                    data.field.refundType = -1;
                }
                layui.productOrder.auditRefundForFinancial(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>