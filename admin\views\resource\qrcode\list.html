﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 60px; }
        .layui-table, .layui-table-view { margin-bottom: 0; }
        .mid-c { display: flex; min-height: 600px; }
        .mid-c .left { padding: 15px; box-sizing: border-box; border-right: 1px solid #ccc; width: 250px; min-width: 250px; }
        .mid-c .right { padding: 15px; box-sizing: border-box; width: 100%; }
        .layui-tree-set-active > .layui-tree-entry { background: #f2f2f2; }
        .layui-tree-set-active > .layui-tree-entry:hover { background: #f2f2f2; }
        .link { color: #50b1fb; }
        .group-view { margin-top: 15px; }
        .group-view ul li { box-sizing: border-box; cursor: pointer; float: left; width: 100%; }
        .group-view ul li span { padding: 7px 0 7px 5px; box-sizing: border-box; width: 82%; display: inline-block; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; float: left; }
        .group-view ul li a { padding: 7px 0; box-sizing: border-box; float: right; margin-top: 2px; margin-right: 2px; }
        .group-view ul li.cur { background: #e9e8e8 }
        .group-view ul li:hover { background: #eee }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="mid-c">
                <div class="left">
                    <div style="text-align:center;margin-bottom:10px;width:100%;">
                        <button type="button" class="layui-btn" style="width:100%" lay-submit lay-filter="add-group">添加企微活码分组</button>
                    </div>
                    <div class="group-view">
                        <ul id="group-view">
                        </ul>
                    </div>
                </div>
                <div class="right layui-form">
                    <div style="width:100%;">
                        <input id="groupId" name="groupId" value="" type="hidden" />
                        <div class="layui-form-item" style="margin-bottom:0">
                            <div class="layui-inline">
                                <label class="layui-form-label">所属企微</label>
                                <div class="layui-input-inline">
                                    <select name="workWxAppId" id="workwx-app-view" lay-search>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">关键词</label>
                                <div class="layui-input-inline">
                                    <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="企微活码名称" />
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">创建时间</label>
                                <div class="layui-input-inline" style="width:155px">
                                    <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" />
                                </div>
                                <div class="layui-input-inline" style="width:155px">
                                    <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" />
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="qrcode-search">
                                    <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                                </button>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn layuiadmin-btn-list qrcode-create"><i class="layui-icon">&#xe654;</i>添加</button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body" style="flex: 0 0 auto;padding:0;">
                        <table id="qrcode-list" lay-filter="list"></table>
                        <script type="text/html" id="qrcode-bar">
                            <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="group-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <li {{item.id==""?"class='cur'":""}} data-id="{{item.id}}">
            <span>{{item.name}}</span>
            {{# if(item.id!=''){}}
            <a href="javascript:;" class="del"><i class="layui-icon">&#xe640;</i></a>
            <a href="javascript:;" class="edit"><i class="layui-icon">&#xe642;</i></a>
            {{# }}}
        </li>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'laydate', 'common', 'qrcode'], function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime',
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime',
            });
            layui.qrcode.initApp();
            layui.qrcode.initGroup();
            layui.qrcode.query();
            //监听查询按钮
            layui.form.on('submit(qrcode-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("qrcode-list", {
                    where: field
                });
            });
            //添加企微分组按钮
            layui.form.on('submit(add-group)', function (data) {
                layui.common.openIframe('企微活码分组', 450, 200, 'create-group.html');
            });
            //打开添加弹框
            layui.$('.qrcode-create').click(function () {
                location.href = 'create-qrcode.html';
            });
        })
    </script>
</body>
</html>