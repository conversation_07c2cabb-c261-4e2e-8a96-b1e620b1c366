﻿layui.define(['laytpl', 'form', 'request', 'tableRequest', 'common', 'customer'], function (exports) {
    var func = {
        /**
         * 渲染客户列表
         * @param {any} companyId
         * @param {any} selectedId
         */
        initCustomer: function (selectedId) {
            layui.customer.getAll(function (res) {
                res.result.unshift({ id: '', alias: '请选择客户' });
                var getTpl = document.getElementById("customer-tpl").innerHTML
                    , view = document.getElementById('customer-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (selectedId != undefined) {
                    view.value = selectedId;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过id获取配置信息
         * */
        get: function (callbackFunc) {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                setTimeout(function () {
                    parent.location.reload();
                }, 3000);
                return;
            }
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/system-config/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    if (callbackFunc != null) {
                        callbackFunc(res.result);
                    }
                    else {
                        layui.$('input[name=id]').val(res.result.id);
                        layui.$('input[name=name]').val(res.result.name);
                        layui.$('input[name=key]').val(res.result.key);
                        layui.$('#desc').val(res.result.desc);
                        layui.$('#configJson').val(res.result.configJson);
                        layui.systemConfig.initCustomer(res.result.customerId);
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取配置列表
         * */
        query: function () {
            layui.tableRequest.request('form', true, 'system-config-list', '/admin/basis/system-config/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '名称' },
                { field: 'key', title: '配置KEY' },
                { field: 'desc', title: '配置描述' },
                {
                    field: 'createdAt', title: '创建时间', templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', align: 'left', toolbar: '#system-config-bar' }
            ]);
            //监听表格事件
            layui.systemConfig.tableEvent();
        },
        /**
        * 创建单个系统配置
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/system-config/create',
                data: JSON.stringify({
                    customerId: data.field.customerId,
                    name: data.field.name,
                    desc: data.field.desc,
                    key: data.field.key,
                    configJson: data.field.configJson
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("配置创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑配置信息
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/system-config/update',
                data: JSON.stringify({
                    id: data.field.id,
                    customerId: data.field.customerId,
                    name: data.field.name,
                    desc: data.field.desc,
                    key: data.field.key,
                    configJson: data.field.configJson
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("配置编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除配置信息
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/basis/system-config/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("system-config-list");
                    layui.common.alertAutoClose("配置删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑配置信息', 600, 550, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'edit-qrcode') {
                    layui.common.openIframe('编辑二维码配置信息', 600, 550, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该配置吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.systemConfig.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('systemConfig', func);
});