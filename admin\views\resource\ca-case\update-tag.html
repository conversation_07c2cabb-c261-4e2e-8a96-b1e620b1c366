﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-card { margin-bottom: 0; }
        .tag-item { margin-bottom: 6px; margin-right: 6px; display: inline-block; }
        .cur { background: #1E9FFF; color: #fff; border-color: #1E9FFF }
        .cur:hover { color: #fff; }
    </style>
</head>
<body>
    <div class="layui-card layui-form">
        <input id="hid_tagIds" type="hidden" />
        <div class="tag-con" id="tag-view">
        </div>
        <div class="layui-form-item" style="padding:10px 0;text-align:center;">
            <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="tag-submit" value="保存修改">
        </div>
    </div>

    <script id="tag-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <div class="item">
            <div class="layui-card-header">{{item.groupName}}</div>
            <div class="layui-card-body">
                {{#  layui.each(item.tag, function(indexTag, itemTag){ }}
                <div class="tag-item">
                    <button type="button" class="layui-btn layui-btn-xs layui-btn-primary tag-btn {{# if(itemTag.isChecked){}} cur {{# }}}" data-groupname="{{item.groupName}}" data-id="{{itemTag.id}}">{{itemTag.name}}</button>
                </div>

                {{#  }); }}
            </div>
        </div>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'caCase'], function () {
            layui.caCase.getCorpTag();
            layui.form.on('submit(tag-submit)', function (data) {
                var tagItems = layui.$('.tag-con button.cur');
                var tags = [];
                for (var i = 0; i < tagItems.length; i++) {
                    tags.push({ groupName: tagItems.eq(i).data('groupname'), id: tagItems.eq(i).data('id'), name: tagItems.eq(i).text() });
                }
                data.field.tags = tags;
                layui.caCase.updateCorpTag(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>