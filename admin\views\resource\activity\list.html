﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 200px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="请输入活动名称" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">活动类型</label>
                        <div class="layui-input-inline">
                            <select name="type">
                                <option value="">请选择活动类型</option>
                                <option value="1">优惠券</option>
                                <option value="2">折扣</option>
                                <option value="3">赠送时长</option>
                                <option value="4">换购</option>
                                <option value="5">优惠金额</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">活动状态</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">请选择活动状态</option>
                                <option value="1">正常</option>
                                <option value="2">下线</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="activity-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list activity-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="activity-list" lay-filter="list"></table>
                <script type="text/html" id="activity-bar">
                    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="audit">审核</a>
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'activity'], function () {
            layui.activity.query();
            //监听查询按钮
            layui.form.on('submit(activity-search)', function (data) {
                var field = data.field;
                if (field.type == '') {
                    field.type = -1;
                }
                if (field.status == '') {
                    field.status = -1;
                }
                //执行重载
                layui.tableRequest.reload("activity-list", {
                    where: field
                });
            });
            //打开添加弹框
            layui.$('.activity-create').click(function () {
                location.href = 'create.html';
            });
        });
    </script>
</body>
</html>