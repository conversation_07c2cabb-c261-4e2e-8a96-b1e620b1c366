﻿layui.define(['laytpl', 'form', 'request', 'common', 'subsidiary'], function (exports) {
    var func = {
        /**
         * 查询组织列表
         */
        getAll: function () {
            var name = layui.common.getUrlParam('name') || '-';
            layui.$('.user-name').text(name);
            var permissionType = layui.common.getUrlParam('permissionType') || 0;
            var objId = layui.common.getUrlParam('id') || '';
            var type = layui.common.getUrlParam('type') || '';
            layui.subsidiary.getAll(function (res) {
                if (res.isSuccess) {
                    layui.request({
                        requestBase: 'resource',
                        method: 'post',
                        url: '/admin/users/data-permission/get',
                        data: JSON.stringify({
                            type: type,
                            permissionType: permissionType,
                            objId: objId
                        }),
                        headers: { 'Content-Type': 'application/json' },
                    }).then(function (perRes) {
                        if (perRes.isSuccess) {
                            for (var i = 0; i < res.result.length; i++) {
                                res.result[i].isChecked = false
                                if (perRes.result != null) {
                                    res.result[i].isChecked = perRes.result.subsidiaryIds.indexOf(res.result[i].id) > -1;
                                }
                            }
                            var getTpl = document.getElementById("subsidiary-tpl").innerHTML
                                , view = document.getElementById('subsidiary-view');
                            layui.laytpl(getTpl).render(res.result, function (html) {
                                view.innerHTML = html;
                            });
                            layui.form.render('checkbox');
                        }
                        else {
                            layui.common.alert(perRes.message, 0);
                        }
                    })
                }
            });
            layui.dataPermission.bindEvent();
        },

        /**
         * 保存用户权限信息
         * @param {any} data
         */
        save: function (data) {
            var permissionType = layui.common.getUrlParam('permissionType') || 0;
            var objId = layui.common.getUrlParam('id') || '';
            var type = layui.common.getUrlParam('type') || '';
            if (objId == '') {
                layui.common.alertAutoClose("对象id有误");
                return;
            }
            if (type == '') {
                layui.common.alertAutoClose("权限类型有误");
                return;
            }
            var subsidiaryIds = [];
            layui.$('input[name="subsidiaryItems"]').each(function () {
                if (layui.$(this).is(":checked")) {
                    subsidiaryIds.push(layui.$(this).val());
                }
            });
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/users/data-permission/save',
                data: JSON.stringify({
                    permissionType: permissionType,
                    objId: objId,
                    type: type,
                    subsidiaryIds: subsidiaryIds
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("权限编辑成功");
                    setTimeout(function () {
                        parent.historyBack();
                    }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 绑定事件
        * @param {any} opType
        */
        bindEvent: function () {
            //提交保存
            layui.form.on('submit(permission-save-submit)', function (data) {
                layui.dataPermission.save(data);
            });
            //返回上一页
            layui.form.on('submit(cancel)', function (data) {
                parent.historyBack();
            });
        },
    }

    exports('dataPermission', func);
});