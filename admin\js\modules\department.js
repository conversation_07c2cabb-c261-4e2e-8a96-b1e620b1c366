﻿layui.define(['laytpl', 'tree', 'request', 'tableRequest', 'common', 'company'], function (exports) {
    var func = {
        /**
       * 渲染部门下拉选框
       * @param {any} companyId
       * @param {any} selectId
       */
        initDepartment: function (companyId, selectId) {
            layui.department.queryByCompanyId(companyId, function (res) {
                var json = [{ id: '', name: '设置为一级部门' }];
                for (var i = 0; i < res.result.length; i++) {
                    var level1 = { id: res.result[i].id, name: res.result[i].name };
                    json.push(level1);
                    var child2 = res.result[i].childs;
                    if (child2 != null && child2.length > 0) {
                        for (var j = 0; j < child2.length; j++) {
                            var level2 = { id: child2[j].id, name: res.result[i].name + ' > ' + child2[j].name };
                            json.push(level2);
                            var child3 = child2[j].childs;
                            if (child3 != null && child3.length > 0) {
                                for (var k = 0; k < child3.length; k++) {
                                    var level3 = { id: child3[k].id, name: res.result[i].name + ' > ' + child2[j].name + ' > ' + child3[k].name };
                                    json.push(level3);
                                    var child4 = child3[k].childs;
                                    if (child4 != null && child4.length > 0) {
                                        for (var m = 0; m < child4.length; m++) {
                                            var level4 = { id: child4[m].id, name: res.result[i].name + ' > ' + child2[j].name + ' > ' + child3[k].name + ' > ' + child4[m].name };
                                            json.push(level4);
                                            var child5 = child4[m].childs;
                                            if (child5 != null && child5.length > 0) {
                                                for (var n = 0; n < child5.length; n++) {
                                                    var level5 = { id: child5[n].id, name: res.result[i].name + ' > ' + child2[j].name + ' > ' + child3[k].name + ' > ' + child4[l].name + ' > ' + child5[n].name };
                                                    json.push(level5);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                var getTpl = document.getElementById("department-tpl").innerHTML
                    , view = document.getElementById('department-view');
                layui.laytpl(getTpl).render(json, function (html) {
                    view.innerHTML = html;
                });
                if (selectId != '') {
                    view.value = selectId;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过企业id查询部门列表
         * @param {any} companyId
         * @param {any} callbackFun
         */
        queryByCompanyId: function (companyId, callbackFun) {
            layui.request({
                method: 'post',
                url: '/admin/sso/department/by-companyid/get?companyId=' + companyId,
            }).then(function (res) {
                callbackFun(res);
            });
        },
        /**
         * 渲染企业下拉选框
         * */
        initCompany: function (id) {
            layui.company.getAll(function (res) {
                //res.result.unshift({ id: '', name: '请选择企业' });
                var getTpl = document.getElementById("company-tpl").innerHTML
                    , view = document.getElementById('company-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('select');
                if (res.result != null && res.result.length > 0) {
                    layui.department.query(res.result[0].id);
                }

                //绑定搜索事件
                layui.form.on('select(department-search)', function (data) {
                    layui.department.query(data.value);
                });
            });
        },
        /**
         * 通过id获取部门信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/sso/department/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=sort]').val(res.result.sort);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取部门列表
         * */
        query: function (companyId) {
            layui.request({
                method: 'post',
                url: '/admin/sso/department/by-companyid/query?companyId=' + companyId,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    var list = [];
                    var data = res.result;
                    for (var i = 0; i < data.length; i++) {
                        var item = { title: data[i].name, id: data[i].id, spread: true, level: 1 };
                        if (data[i].childs != null && data[i].childs.length > 0) {
                            item.children = []
                            for (var j = 0; j < data[i].childs.length; j++) {
                                var item2 = { title: data[i].childs[j].name, id: data[i].childs[j].id, spread: true, level: 2 };
                                if (data[i].childs[j].childs != null && data[i].childs[j].childs.length > 0) {
                                    item2.children = [];
                                    for (var k = 0; k < data[i].childs[j].childs.length; k++) {
                                        var item3 = { title: data[i].childs[j].childs[k].name, id: data[i].childs[j].childs[k].id, spread: true, level: 3 };
                                        if (data[i].childs[j].childs[k].childs != null && data[i].childs[j].childs[k].childs.length > 0) {
                                            item3.children = [];
                                            for (var m = 0; m < data[i].childs[j].childs[k].childs.length; m++) {
                                                var item4 = { title: data[i].childs[j].childs[k].childs[m].name, id: data[i].childs[j].childs[k].childs[m].id, spread: true, level: 4 };
                                                if (data[i].childs[j].childs[k].childs[m].childs != null && data[i].childs[j].childs[k].childs[m].childs.length > 0) {
                                                    item4.children = [];
                                                    for (var f = 0; f < data[i].childs[j].childs[k].childs[m].childs.length; f++) {
                                                        var item5 = { title: data[i].childs[j].childs[k].childs[m].childs[f].name, id: data[i].childs[j].childs[k].childs[m].childs[f].id, spread: true, level: 5 };
                                                        item4.children.push(item5);
                                                    }
                                                }
                                                item3.children.push(item4);
                                            }
                                        }
                                        item2.children.push(item3);
                                    }
                                }
                                item.children.push(item2);
                            }
                        }
                        list.push(item);
                    }
                    var btns = [];
                    if (layui.common.getPermission('63369ab96918e5623bad0f65', 'sso.department.move')) {
                        btns.push('move');
                    }
                    if (layui.common.getPermission('63369ab96918e5623bad0f65', 'sso.department.create')) {
                        btns.push('add');
                    }
                    if (layui.common.getPermission('63369ab96918e5623bad0f65', 'sso.department.update')) {
                        btns.push('diyupdate');
                    }
                    if (layui.common.getPermission('63369ab96918e5623bad0f65', 'sso.department.delete')) {
                        btns.push('del');
                    }
                    if (layui.common.getPermission('63369ab96918e5623bad0f65', 'sso.department.permission')) {
                        btns.push('permission');
                    }
                    layui.tree.render({
                        elem: '#department-list'
                        , id: 'department-list'
                        , data: list
                        /*, showLine: false*/
                        , edit: btns //操作节点的图标
                        , click: function (obj) {
                            //console.log(JSON.stringify(obj.data));
                        }
                        , operate: function (obj) {
                            var type = obj.type; //得到操作类型：add、edit、del
                            var data = obj.data; //得到当前节点的数据
                            if (type === 'add') {
                                if (data.level >= 5) {
                                    layui.common.alertAutoClose("部门超过层级限制");
                                }
                                else {
                                    layui.common.openIframe('创建部门', 600, 320, 'create.html?companyId=' + layui.$('#company-view').val() + '&parentId=' + data.id);
                                }

                                return;
                            } else if (type === 'diyupdate') {
                                layui.common.openIframe('编辑部门', 600, 320, 'update.html?id=' + data.id);
                            } else if (type === 'del') {
                                layui.department.delete(data.id);
                            } else if (type === 'permission') {
                                location.href = '../permission/settab.html?id=' + data.id + '&type=dep&name=' + data.title;
                            } else if (type === 'move') {
                                layui.common.openIframe('移动部门', 600, 320, 'move.html?id=' + data.id + '&companyId=' + companyId);
                            }
                        }
                    });
                }
            })
        },
        /**
        * 创建单个部门
        * */
        create: function (data) {
            var parentId = layui.common.getUrlParam('parentId') || '';
            var companyId = layui.common.getUrlParam('companyId') || '';
            if (companyId == '') {
                layui.common.alertAutoClose("企业id有误");
                return;
            }
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/sso/department/create',
                data: JSON.stringify({
                    name: data.field.name,
                    companyId: companyId,
                    parentId: parentId,
                    sort: data.field.sort
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.department.query(companyId);
                    parent.layui.common.alertAutoClose("部门创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑角色
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/sso/department/update',
                data: JSON.stringify({
                    id: data.field.id,
                    name: data.field.name,
                    sort: data.field.sort
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.department.query(parent.layui.$('#company-view').val());
                    parent.layui.common.alertAutoClose("部门编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
      * 编辑角色父级部门
      * @param {any} data
      */
        updateParentId: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/sso/department/parentid/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.department.query(parent.layui.$('#company-view').val());
                    parent.layui.common.alertAutoClose("部门移动成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除部门
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/sso/department/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.department.query(layui.$('#company-view').val());
                    layui.common.alertAutoClose("部门删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        }
    }

    exports('department', func);
});