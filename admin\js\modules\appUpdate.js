﻿layui.define(['laytpl', 'form', 'request', 'tableRequest', 'common', 'uploadFile'], function (exports) {
    var func = {
        /**
         * 通过id获取app版本信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                setTimeout(function () {
                    parent.location.reload();
                }, 3000);
                return;
            }
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/config/app-update/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=version]').val(res.result.version);
                    layui.$("input:radio[name=paltform][value='" + res.result.paltform + "']").prop('checked', true)
                    layui.$('#url').val(res.result.url);
                    layui.$('#filename').text(res.result.url.split("/").pop());
                    layui.form.render('radio');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取app版本列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'app-update-list', '/admin/config/app-update/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'version', title: '版本', },
                {
                    field: 'paltform', title: '平台', templet: function (e) {
                        if (e.paltform == 1) {
                            return 'Android'
                        }
                        else if (e.paltform == 2) {
                            return 'Ios'
                        }
                        else if (e.paltform == 3) {
                            return '微信小程序'
                        }
                        return '-';
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', align: 'left', toolbar: '#app-update-bar' }
            ]);
            //监听表格事件
            layui.appUpdate.tableEvent();
        },
        /**
        * 创建单个app版本
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/config/app-update/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑单个app版本信息
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/config/app-update/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除单个app版本信息
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/config/app-update/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("app-update-list");
                    layui.common.alertAutoClose("删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑版本信息', 500, 350, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'download') {
                    location.href = data.url;
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该版本信息吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.appUpdate.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('appUpdate', func);
});