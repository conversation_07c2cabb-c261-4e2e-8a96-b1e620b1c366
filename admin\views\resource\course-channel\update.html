﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <input name="id" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">渠道KEY</label>
            <div class="layui-input-inline">
                <input type="text" name="key" lay-verify="required" placeholder="请输入渠道KEY" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">渠道名称</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入渠道名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="course-channel-update-submit" value="确认修改">
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'courseChannel'], function () {
            layui.courseChannel.get();
            layui.form.on('submit(course-channel-update-submit)', function (data) {
                layui.courseChannel.update(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>