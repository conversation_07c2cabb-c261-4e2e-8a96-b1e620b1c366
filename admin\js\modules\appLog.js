﻿layui.define(['laytpl', 'form', 'laydate', 'request', 'tableRequest', 'common', 'logApp'], function (exports) {
    var func = {
        /**
        * 渲染应用下拉选框
        * */
        initApp: function (id) {
            layui.logApp.getAll(function (res) {
                res.result.unshift({ appId: '', appName: '请选择应用' });
                var getTpl = document.getElementById("app-tpl").innerHTML
                    , view = document.getElementById('app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('app-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
        * 通过id获取应用日志信息
        * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.alertAutoClose("参数有误");
                setTimeout(function () { history.back(-1) }, 2000);
                return;
            }
            layui.request({
                method: 'post',
                requestBase: 'log',
                url: '/admin/app-error-log/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('#id').text(res.result.id);
                    layui.$('#appId').text(res.result.appId);
                    layui.$('#appName').text(res.result.appName);
                    layui.$('#title').text(res.result.title);
                    layui.$('#method').text(res.result.method);
                    layui.$('#url').text(res.result.url);
                    layui.$('#queryString').text(res.result.queryString);
                    layui.$('#clientIP').text(res.result.clientIP);
                    layui.$('#innerMessage').text(res.result.innerMessage);

                    if (res.result.headers != '') {
                        var headersJson = JSON.stringify(JSON.parse(res.result.headers), null, 2);
                        layui.$('#headers').html(headersJson);
                    }
                    if (res.result.requestBody != '') {
                        var requestBodyJson = JSON.stringify(JSON.parse(res.result.requestBody), null, 2);
                        layui.$('#requestBody').html(requestBodyJson);
                    }
                    if (res.result.stackTrace != '') {
                        layui.$('#stackTrace').html(res.result.stackTrace);
                    }
                    layui.$('#createdAt').text(layui.common.timeFormat(res.result.createdAt));
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 获取应用日志列表
        * */
        query: function () {
            layui.tableRequest.request('log', true, 'app-log-list', '/admin/app-error-log/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'appName', title: '应用' },
                { field: 'title', title: '标题' },
                { field: 'method', title: '请求方式' },
                { field: 'url', title: '请求地址' },
                { field: 'clientIP', title: '客户端IP' },
                { field: 'innerMessage', title: '详细信息' },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 80, align: 'left', toolbar: '#app-log-bar' }
            ]);
            //监听表格事件
            layui.appLog.tableEvent();
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    location.href = 'detail.html?id=' + data.id;
                }
            });
        },
        /**
         * 绑定事件
         * */
        bindEvent: function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            //监听查询按钮
            layui.form.on('submit(app-log-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("app-log-list", {
                    where: field
                });
            });
        }
    };
    exports('appLog', func);
});