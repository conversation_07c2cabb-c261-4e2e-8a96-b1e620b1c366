﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 90px; }
        .layui-form-item .layui-input-inline { width: 250px; }
    </style>
</head>
<body>
    <div class="layui-row layui-form" style="padding-top:20px;padding-bottom:70px;">
        <input id="id" name="id" type="hidden" />
        <div class="layui-col-xs6">
            <div class="layui-form-item">
                <label class="layui-form-label">姓名</label>
                <div class="layui-input-inline">
                    <input type="text" name="name" lay-verify="required" placeholder="请输入客户姓名" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">身份证号</label>
                <div class="layui-input-inline">
                    <input type="text" name="certNo" placeholder="请输入身份证号" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">性别</label>
                <div class="layui-input-inline">
                    <input type="radio" name="sexInfo" value="0" title="未知" checked>
                    <input type="radio" name="sexInfo" value="1" title="男">
                    <input type="radio" name="sexInfo" value="2" title="女">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">微信号</label>
                <div class="layui-input-inline">
                    <input type="text" name="weChatAccount" placeholder="请输入微信号" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">微信昵称</label>
                <div class="layui-input-inline">
                    <input type="text" name="weChatNickName" placeholder="请输入微信昵称" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">意向分类</label>
                <div class="layui-input-inline">
                    <select id="intention" name="intention">
                        <option value="">请选择意向分类</option>
                        <option value="1">高意向</option>
                        <option value="2">中意向</option>
                        <option value="3">低意向</option>
                        <option value="4">无效客户</option>
                        <option value="5">已成交</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">风险等级</label>
                <div class="layui-input-inline">
                    <select id="riskLevel" name="riskLevel">
                        <option value="">请选择风险等级</option>
                        <option value="1">C1保守型</option>
                        <option value="2">C2谨慎型</option>
                        <option value="3">C3稳健型</option>
                        <option value="4">C4积极型</option>
                        <option value="5">C5激进型</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">备用手机号</label>
                <div class="layui-input-inline">
                    <input type="text" name="spareMobile" placeholder="备用手机号" autocomplete="off" class="layui-input">
                </div>
            </div>
        </div>
        <div class="layui-col-xs6">
            <div class="layui-form-item">
                <label class="layui-form-label">证件有效期</label>
                <div class="layui-input-inline">
                    <input type="text" id="certValidity" name="certValidity" placeholder="请选择证件有效期" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">通讯地址</label>
                <div class="layui-input-inline">
                    <input type="text" name="address" placeholder="请输入通讯地址" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">职业</label>
                <div class="layui-input-inline">
                    <select id="profession" name="profession">
                        <option value="">请选择职业</option>
                        <option value="党政机关工作人员">党政机关工作人员</option>
                        <option value="企事业单位职工">企事业单位职工</option>
                        <option value="农民">农民</option>
                        <option value="个体工商户">个体工商户</option>
                        <option value="学生">学生</option>
                        <option value="金融机构从业人员">金融机构从业人员</option>
                        <option value="无业">无业</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">学历</label>
                <div class="layui-input-inline">
                    <select id="education" name="education">
                        <option value="">请选择学历</option>
                        <option value="博士">博士</option>
                        <option value="硕士">硕士</option>
                        <option value="本科">本科</option>
                        <option value="大专">大专</option>
                        <option value="中专">中专</option>
                        <option value="高中">高中</option>
                        <option value="初中及以下">初中及以下</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">不良诚信记录</label>
                <div class="layui-input-inline">
                    <select id="badIntegrityRecord" name="badIntegrityRecord">
                        <option value="">请选择不良诚信记录</option>
                        <option value="无">无</option>
                        <option value="中国人民银行征信中心">中国人民银行征信中心</option>
                        <option value="最高人民法院失信被执行人名单">最高人民法院失信被执行人名单</option>
                        <option value="工商行政管理机构">工商行政管理机构</option>
                        <option value="税务管理机构">税务管理机构</option>
                        <option value="监管机构、自律组织">监管机构、自律组织</option>
                        <option value="">投资者在证券经营机构的失信记录</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">工作单位</label>
                <div class="layui-input-inline">
                    <input type="text" name="company" placeholder="请输入工作单位" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">职务</label>
                <div class="layui-input-inline">
                    <input type="text" name="workplace" placeholder="请输入职务" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
            </div>
        </div>
        <div style="position:fixed;bottom:0;background:#fff;border-top:1px solid #eee;width:100%;padding:10px 0;text-align:center;">
            <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="customer-update-submit" value="确认修改">
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'laydate', 'productCustomer'], function () {
            layui.laydate.render({
                elem: '#certValidity',
                type: 'date'
            });
            layui.productCustomer.getForUpdate();
            //监听提交事件
            layui.form.on('submit(customer-update-submit)', function (data) {
                if (data.field.intention == '') {
                    data.field.intention = 0;
                }
                if (data.field.riskLevel == '') {
                    data.field.riskLevel = 0;
                }
                data.field.sex = data.field.sexInfo;
                layui.productCustomer.update(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>