<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        body {
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="layui-form" style="padding: 50px;" lay-filter="signForm">
        <div class="layui-form-item">
            <label class="layui-form-label">内容</label>
            <div class="layui-input-block">
                <input type="text" name="content" lay-verify="required" placeholder="请输入内容" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">时间</label>
            <div class="layui-input-block">
                <input id="datetime" name="datetime" type="text" lay-verify="required" class="layui-input" placeholder="请选择时间" autocomplete="off" />
            </div>
        </div>
        <!-- 添加结果链接区域，默认隐藏 -->
        <div class="layui-form-item" id="resultArea" style="display: none;">
            <label class="layui-form-label">签署链接</label>
            <div class="layui-input-block">
                <input type="text" id="resultLink" class="layui-input" readonly>
                <button type="button" class="layui-btn layui-btn-normal" id="copyBtn" style="margin-top: 10px;">复制链接</button>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn" lay-submit lay-filter="formSubmit" id="submitBtn">提交</button>
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script>
        layui.use(['form', 'laydate', 'productCustomer'], function(){
            var form = layui.form;
            var laydate = layui.laydate;
            
            // 获取URL参数函数
            function getUrlParam(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return decodeURIComponent(r[2]); return null;
            }
            
            // 获取URL中的id参数
            var id = getUrlParam('id');
            
            laydate.render({
                elem: '#datetime',
                type: 'datetime',
                position: 'abolute', //绝对定位
                btns: ['clear', 'now', 'confirm'],
                trigger: 'click', //采用click触发
                ready: function(date){
                    //弹出时定位到文本框下方
                    var datePickerMain = document.querySelector('.layui-laydate');
                    var inputElem = document.querySelector('#datetime');
                    var rect = inputElem.getBoundingClientRect();
                    datePickerMain.style.top = rect.bottom + 'px';
                    datePickerMain.style.left = rect.left + 'px';
                }
            });

            // 复制链接功能
            document.getElementById('copyBtn').onclick = function() {
                var linkInput = document.getElementById('resultLink');
                linkInput.select();
                document.execCommand('copy');
                layer.msg('复制成功');
            };

            //监听提交
            form.on('submit(formSubmit)', function(data){
                var formData = data.field;
                // 将id添加到表单数据中
                if (id) {
                    formData.id = id;
                }
                console.log('表单数据：', formData);

                layui.productCustomer.generatePetitionForm(formData).then(function(res) {
                    if (res.isSuccess) {
                        // 显示结果链接
                        document.getElementById('resultLink').value = res.result;
                        document.getElementById('resultArea').style.display = 'block';
                        // 隐藏提交按钮
                        document.getElementById('submitBtn').style.display = 'none';
                    } else {
                        layui.common.alert(res.message || '生成失败', 0);
                    }
                });
                
                return false; //阻止表单跳转
            });
        });
    </script>
</body>
</html>
