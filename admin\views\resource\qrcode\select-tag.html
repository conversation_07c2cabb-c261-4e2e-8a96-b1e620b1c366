﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-card { margin-bottom: 0; }
        .tag-item { margin-bottom: 6px; margin-right: 6px; display: inline-block; }
        .cur { background: #1E9FFF; color: #fff; border-color: #1E9FFF }
        .cur:hover { color: #fff; }
    </style>
</head>
<body>
    <div class="layui-card layui-form">
        <div class="tag-con" id="tag-view" style="margin-bottom:60px;">
        </div>
        <div style="padding:10px 0;text-align:center;position:fixed;bottom:0;left:0;width:100%;background:#fff;border-top:1px solid #eee">
            <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="tag-submit" value="选择完成">
        </div>
    </div>

    <script id="tag-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <div class="item">
            <div class="layui-card-header">{{item.group_name}}</div>
            <div class="layui-card-body">
                {{#  layui.each(item.tag, function(indexTag, itemTag){ }}
                <div class="tag-item">
                    <button type="button" class="layui-btn layui-btn-xs layui-btn-primary tag-btn {{# if(itemTag.isChecked){}} cur {{# }}}" data-groupname="{{item.group_name}}" data-id="{{itemTag.id}}">{{itemTag.name}}</button>
                </div>
                {{#  }); }}
            </div>
        </div>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'qrcode'], function () {
            var type = layui.common.getUrlParam('type');
            layui.qrcode.initCorpTag(type);
            layui.form.on('submit(tag-submit)', function (data) {
                var tagItems = layui.$('.tag-con button.cur');
                var tagHtml = '';
                for (var i = 0; i < tagItems.length; i++) {
                    tagHtml += '<a href="javascript:;" data-groupname="' + tagItems.eq(i).data('groupname') + '" data-id="' + tagItems.eq(i).data('id') + '" data-name="' + tagItems.eq(i).text() + '"><span>' + tagItems.eq(i).text() + '</span><i class="layui-icon remove-tag">&#x1006;</i></a>'
                }
                if (type == 1) {
                    parent.layui.$('#corp-tag').html(tagHtml);
                }
                else if (type == 2) {
                    parent.layui.$('#look-corp-tag').html(tagHtml);
                }
                parent.layui.common.closeType('iframe');
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>