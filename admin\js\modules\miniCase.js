﻿layui.define(['laytpl', 'request', 'tableRequest', 'common', 'uploadFile', 'dic', 'workWxApp', 'miniprogram', 'tencentAccount', 'qrcode'], function (exports) {
    var func = {
        /**
         * 渲染企微应用下拉选框
         * */
        initApp: function (id) {
            layui.workWxApp.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企微' });
                var getTpl = document.getElementById("workwx-app-tpl").innerHTML
                    , view = document.getElementById('workwx-app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('workwx-app-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过id获取客服加粉方案
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/mini-case/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=baiDuToken]').val(res.result.baiDuToken);
                    layui.$('input[name=qrCodeUrl]').val(res.result.qrCodeUrl);
                    layui.$('input[name=invalidRate]').val(res.result.invalidRate);
                    layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.result.qrCodeUrl);
                    //加载企微应用列表
                    layui.miniCase.initApp(res.result.workWxAppId);
                    //加载客服列表
                    layui.miniprogram.queryByWorkWxAppId(res.result.workWxAppId, function (resMini) {
                        var options = '<option value="">请选择微信小程序</option>'
                        if (resMini.isSuccess) {
                            for (var i = 0; i < resMini.result.length; i++) {
                                options += '<option ' + (res.result.miniAppId == resMini.result[i].id ? 'selected="true"' : '') + ' value="' + resMini.result[i].id + '">' + resMini.result[i].name + '</option>';
                            }
                        }
                        layui.$('#miniprogram-id').html(options);
                        layui.form.render('select');
                    });
                    //加载使用场景
                    layui.dic.query('workwx_scene', function (resScene) {
                        var options = '<option value="">请选择使用场景</option>'
                        if (resScene.isSuccess) {
                            for (var i = 0; i < resScene.result.length; i++) {
                                options += '<option ' + (res.result.scene == resScene.result[i].key ? 'selected="true"' : '') + ' value="' + resScene.result[i].key + '">' + resScene.result[i].value + '</option>';
                            }
                        }
                        if (res.result.scene == 'tenxun_sph') {
                            layui.$(".tencent-ad-account").show();
                            //加载腾讯推广账户
                            layui.tencentAccount.getAll(res.result.workWxAppId, function (resTencent) {
                                var optionsTencent = '<option value="">请选择推广账户</option>'
                                if (resTencent.isSuccess) {
                                    for (var i = 0; i < resTencent.result.length; i++) {
                                        optionsTencent += '<option ' + (res.result.tencentAdAccountId == resTencent.result[i].id ? 'selected="true"' : '') + ' value="' + resTencent.result[i].id + '">' + resTencent.result[i].name + '</option>';
                                    }
                                }
                                layui.$('#tencentAdAccountId').html(optionsTencent);
                                layui.form.render('select');
                            })
                        }
                        layui.$('#scene').html(options);
                        layui.form.render('select');
                    });
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 通过id获取客服加粉方案
        * */
        getV3: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/mini-case/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=baiDuToken]').val(res.result.baiDuToken);
                    //加载企微应用列表
                    layui.miniCase.initApp(res.result.workWxAppId);
                    //加载客服列表
                    layui.miniprogram.queryByWorkWxAppId(res.result.workWxAppId, function (resMini) {
                        var options = '<option value="">请选择微信小程序</option>'
                        if (resMini.isSuccess) {
                            for (var i = 0; i < resMini.result.length; i++) {
                                options += '<option ' + (res.result.miniAppId == resMini.result[i].id ? 'selected="true"' : '') + ' value="' + resMini.result[i].id + '">' + resMini.result[i].name + '</option>';
                            }
                        }
                        layui.$('#miniprogram-id').html(options);
                        layui.form.render('select');
                    });
                    //加载活码列表
                    layui.qrcode.queryByWorkWxAppId(res.result.workWxAppId, function (resQrCode) {
                        var options = '<option value="">请选择企微活码</option>'
                        if (resQrCode.isSuccess) {
                            for (var i = 0; i < resQrCode.result.length; i++) {
                                options += '<option ' + (res.result.workWxQrCodeId == resQrCode.result[i].id ? 'selected="true"' : '') + ' value="' + resQrCode.result[i].id + '">' + resQrCode.result[i].name + '</option>';
                            }
                        }
                        layui.$('#workWxQrCodeId').html(options);
                        layui.form.render('select');
                    });
                    //加载使用场景
                    layui.dic.query('workwx_scene', function (resScene) {
                        var options = '<option value="">请选择使用场景</option>'
                        if (resScene.isSuccess) {
                            for (var i = 0; i < resScene.result.length; i++) {
                                options += '<option ' + (res.result.scene == resScene.result[i].key ? 'selected="true"' : '') + ' value="' + resScene.result[i].key + '">' + resScene.result[i].value + '</option>';
                            }
                        }
                        if (res.result.scene == 'tenxun_sph') {
                            layui.$(".tencent-ad-account").show();
                            //加载腾讯推广账户
                            layui.tencentAccount.getAll(res.result.workWxAppId, function (resTencent) {
                                var optionsTencent = '<option value="">请选择推广账户</option>'
                                if (resTencent.isSuccess) {
                                    for (var i = 0; i < resTencent.result.length; i++) {
                                        optionsTencent += '<option ' + (res.result.tencentAdAccountId == resTencent.result[i].id ? 'selected="true"' : '') + ' value="' + resTencent.result[i].id + '">' + resTencent.result[i].name + '</option>';
                                    }
                                }
                                layui.$('#tencentAdAccountId').html(optionsTencent);
                                layui.form.render('select');
                            })
                        }
                        layui.$('#scene').html(options);
                        layui.form.render('select');
                    });
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
       * 获取企业标签
       * */
        getCorpTag: function () {
            var id = layui.common.getUrlParam('id') || '';
            layui.request({
                method: 'post',
                url: '/admin/workwx/mini-case/corp-tag/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    var getTpl = document.getElementById("tag-tpl").innerHTML
                        , view = document.getElementById('tag-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.$(document).on('click', '.tag-btn', function () {
                        var that = layui.$(this);
                        if (that.hasClass('cur')) {
                            that.removeClass('cur');
                        }
                        else {
                            that.addClass('cur');
                        }
                    });
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取小程序加粉方案列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'mini-case-list', '/admin/workwx/mini-case/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '方案名称' },
                { field: 'workWxAppName', title: '所属企微' },
                { field: 'miniAppName', title: '绑定小程序' },
                { field: 'scene', title: '使用场景' },
                {
                    field: 'invalidRate', title: '无效资源比例', templet: function (e) {
                        return (e.invalidRate * 100) + '%';
                    }
                },
                {
                    title: '标签', minWidth: 180, templet: function (e) {
                        var tags = '';
                        if (e.corpTags != null && e.corpTags.length > 0) {
                            for (var i = 0; i < e.corpTags.length; i++) {
                                tags += '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary">' + e.corpTags[i] + '</button>';
                            }
                        }
                        else {
                            tags = '-';
                        }
                        return tags;
                    }
                },
                {
                    field: 'qrCodeUrl', title: '加微二维码', templet: function (e) {
                        return '<a target="_blank" href="' + e.qrCodeUrl + '"><img src="' + e.qrCodeUrl + '" style="height:25px"/></a>';
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 180, align: 'left', toolbar: '#mini-case-bar' }
            ]);
            //监听表格事件
            layui.miniCase.tableEvent();
        },
        /**
         * 获取小程序加粉方案列表（v3）
         * */
        queryV3: function () {
            layui.tableRequest.request('resource', true, 'mini-case-list', '/admin/workwx/mini-case/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '方案名称' },
                { field: 'workWxAppName', title: '所属企微' },
                { field: 'miniAppName', title: '绑定小程序' },
                { field: 'scene', title: '使用场景' },

                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 130, align: 'left', toolbar: '#mini-case-bar' }
            ], { qrType: 3 });
            //监听表格事件
            layui.miniCase.tableEvent();
        },
        /**
        * 创建小程序加粉方案
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/mini-case/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("mini-case-list");
                    parent.layui.common.alertAutoClose("小程序加粉方案创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
       * 创建小程序加粉方案(v3)
       * */
        createV3: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/mini-case/v3/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("mini-case-list");
                    parent.layui.common.alertAutoClose("创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑小程序加粉方案
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/mini-case/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("mini-case-list");
                    parent.layui.common.alertAutoClose("小程序加粉方案编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 编辑小程序加粉方案(v3)
        * @param {any} data
        */
        updateV3: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/mini-case/v3/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("mini-case-list");
                    parent.layui.common.alertAutoClose("编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
       * 编辑企微标签
       * */
        updateCorpTag: function (data) {
            var id = layui.common.getUrlParam('id') || '';
            layui.request({
                method: 'post',
                url: '/admin/workwx/mini-case/corp-tag/update',
                data: JSON.stringify({ id: id, tags: data.field.tags }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.tableRequest.reload("mini-case-list");
                    parent.layui.common.alertAutoClose("保存成功");
                    parent.layui.common.closeType('iframe');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除小程序加粉方案
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/mini-case/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("mini-case-list");
                    layui.common.alertAutoClose("小程序加粉方案删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除小程序加粉方案(v3)
         * @param {any} data
         */
        deleteV3: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/mini-case/v3/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("mini-case-list");
                    layui.common.alertAutoClose("删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event == 'edit') {
                    layui.common.openIframe('小程序加粉方案', 740, 500, 'update.html?id=' + data.id);
                }
                else if (obj.event == 'tag') {
                    layui.common.openIframe('编辑企业标签', 700, 600, 'update-tag.html?id=' + data.id);
                }
                else if (obj.event == 'del') {
                    var confirmIndex = layui.layer.confirm('删除加粉方案后，历史落地页加粉将失效，确定删除该方案吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.miniCase.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event == 'edit-v3') {
                    layui.common.openIframe('小程序加粉方案', 740, 500, 'update-v3.html?id=' + data.id);
                }
                else if (obj.event == 'del-v3') {
                    var confirmIndex = layui.layer.confirm('删除加粉方案后，历史落地页加粉将失效，确定删除该方案吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.miniCase.deleteV3(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        },
        /**
         * 绑定事件
         * */
        bindEvent: function (opType) {
            //渲染使用场景
            layui.dic.query('workwx_scene', function (res) {
                var options = '<option value="">请选择使用场景</option>'
                if (res.isSuccess) {
                    for (var i = 0; i < res.result.length; i++) {
                        options += '<option value="' + res.result[i].key + '">' + res.result[i].value + '</option>';
                    }
                }
                layui.$('#scene').html(options);
                layui.form.render('select');
            });

            //企微应用选择后，加载微信客服账号列表
            layui.form.on('select(workwx-app)', function (data) {
                var workWxAppId = data.value;
                layui.miniprogram.queryByWorkWxAppId(workWxAppId, function (res) {
                    var options = '<option value="">请选择微信小程序</option>'
                    if (res.isSuccess) {
                        for (var i = 0; i < res.result.length; i++) {
                            options += '<option value="' + res.result[i].id + '">' + res.result[i].name + '</option>';
                        }
                    }
                    layui.$('#miniprogram-id').html(options);
                    layui.form.render('select');
                })
            });
            //使用场景选择腾讯视频号时，加载腾讯推广账户列表
            layui.form.on('select(scene)', function (data) {
                if (data.value == 'tenxun_sph') {
                    layui.$(".tencent-ad-account").show();
                    var workWxAppId = layui.$("#workwx-app-view").val();
                    layui.tencentAccount.getAll(workWxAppId, function (res) {
                        var options = '<option value="">请选择推广账户</option>'
                        if (res.isSuccess) {
                            for (var i = 0; i < res.result.length; i++) {
                                options += '<option value="' + res.result[i].id + '">' + res.result[i].name + '</option>';
                            }
                        }
                        layui.$('#tencentAdAccountId').html(options);
                        layui.form.render('select');
                    })
                }
                else {
                    layui.$(".tencent-ad-account").hide();
                }
            });
            //提交数据
            layui.form.on('submit(mini-case-submit)', function (data) {
                //腾讯视频号必须选择推广账户，否则数据无法回传
                if (data.field.scene == 'tenxun_sph' && data.field.tencentAdAccountId == '') {
                    layui.common.alertAutoClose('请选择腾讯推广账户');
                    return;
                }
                if (opType == 'create') {
                    layui.miniCase.create(data);
                }
                else if (opType == 'update') {
                    layui.miniCase.update(data);
                }
                return false; //阻止表单跳转
            });
        }
    }

    exports('miniCase', func);
});