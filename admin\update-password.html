﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 150px; }
        .layui-form-item .layui-input-inline { width: 400px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px;">
                <div class="layui-form-item">
                    <label class="layui-form-label">原始密码</label>
                    <div class="layui-input-inline">
                        <input type="password" name="oriPassword" lay-verify="required" placeholder="请输入原始密码" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">新密码</label>
                    <div class="layui-input-inline">
                        <input type="password" name="newPassword" lay-verify="required" placeholder="新密码，6-20个字母、数字、下划线" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">确认密码</label>
                    <div class="layui-input-inline">
                        <input type="password" name="confirmPassword" lay-verify="required" placeholder="确认密码，6-20个字母、数字、下划线" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-inline">
                        <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="update-submit" value="确认修改">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/lib/jsencrypt/jsencrypt.min.js"></script>
    <script src="/admin/js/global.js?v=2.4"></script>
    <script type="text/javascript">
        layui.use(['form', 'updatePassword'], function () {
            //监听提交事件
            layui.form.on('submit(update-submit)', function (data) {
                layui.updatePassword.update(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>