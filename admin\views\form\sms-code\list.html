﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        /* .layui-form-item .layui-input-inline { width: 300px; }*/
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属客户</label>
                        <div class="layui-input-inline">
                            <select name="customerId" id="customer-view">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">发送状态</label>
                        <div class="layui-input-inline">
                            <select name="sendStatus" id="sendStatus">
                                <option value="">请选择发送状态</option>
                                <option value="1">发送成功</option>
                                <option value="2">发送失败</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">验证状态</label>
                        <div class="layui-input-inline">
                            <select name="validateStatus" id="validateStatus">
                                <option value="">请选择短信验证状态</option>
                                <option value="1">待验证</option>
                                <option value="2">验证通过</option>
                                <option value="3">已失效</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline" style="float:left;">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="请输入关键词/手机号" />
                        </div>
                    </div>
                    <label class="layui-form-label">创建日期</label>
                    <div class="layui-input-inline">
                        <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" />
                    </div>
                    <div class="layui-input-inline">
                        <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" />
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="sms-code-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="sms-code-list" lay-filter="list"></table>
            </div>
        </div>
    </div>

    <script id="customer-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.alias}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['smsCode'], function () {
            layui.smsCode.initCustomer();
            layui.smsCode.query();
            layui.smsCode.bindEvent();
        });
    </script>
</body>
</html>