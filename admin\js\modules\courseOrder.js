﻿layui.define(['laytpl', 'form', 'request', 'tableRequest', 'common', 'courseChannel'], function (exports) {
    var func = {
        /**
        * 渲染课程渠道下拉选框
        * */
        initCourseChannel: function () {
            layui.courseChannel.getAll(function (res) {
                res.result.unshift({ key: '', name: '请选择渠道' });
                var getTpl = document.getElementById("course-channel-tpl").innerHTML
                    , view = document.getElementById('course-channel-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('select');
            });
        },
        /**
         * 获取课程订单列表
         * */
        query: function () {
            var companyId = layui.common.getUrlParam('companyId') || '';
            if (companyId == '') {
                layui.common.alertAutoClose("企业id有误");
            }
            layui.tableRequest.request('resource', true, 'course-order-list', '/admin/course/order/query', 'application/json', [
                { field: 'id', title: 'ID', width: 80 },
                {
                    field: 'orderNo', title: '订单号', width: 250, templet: function (e) {
                        var orderNo = '商户：' + e.orderNo;
                        if (e.thirdPartyOrderNo != '') {
                            orderNo += '<br/>第三方：' + e.thirdPartyOrderNo;
                        }
                        return orderNo
                    }
                },
                { field: 'userName', title: '姓名', width: 100 },
                { field: 'mobile', title: '手机号', width: 120 },
                { field: 'courseName', title: '购买产品' },
                { field: 'amount', title: '订单金额', width: 90 },
                { field: 'payTypeDesc', title: '支付方式', width: 100 },
                {
                    field: 'payStatus', title: '支付状态', width: 110, sort: true, templet: function (e) {
                        var payStatusDesc = e.payStatusDesc;
                        if (e.refundTime != null && e.refundTime != '') {
                            payStatusDesc += '<br/>' + layui.common.timeFormat(e.payTime);
                        }
                        return payStatusDesc;
                    }
                },
                {
                    field: 'refundStatus', title: '退款状态', width: 110, sort: true, templet: function (e) {
                        var refundStatusDesc = e.refundStatusDesc;
                        if (e.refundTime != null && e.refundTime != '') {
                            refundStatusDesc += '- ¥' + e.refundAmount + '<br/>' + layui.common.timeFormat(e.refundTime);
                        }
                        return refundStatusDesc;
                    }
                },
                { field: 'reviewStatusDesc', title: '审核状态', width: 100 },
                {
                    title: '来源渠道', width: 140, templet: function (e) {
                        var channelName = e.channelName;
                        if (e.salesUserName != '') {
                            channelName += '（' + e.salesUserName + '）'
                        }
                        return channelName;
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, sort: true, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { title: '操作', width: 180, align: 'left', toolbar: '#course-order-bar' }
            ], { companyId: companyId });
            //监听表格事件
            layui.courseOrder.tableEvent();
        },
        /**
        * 删除订单
        * @param {any} data
        */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/product/order/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("product-order-list");
                    layui.common.alertAutoClose("订单删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 审核订单
         * @param {any} data
         */
        updateAuditStatus: function (data) {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/order/audit-status/update',
                data: JSON.stringify({
                    id: id,
                    auditStatus: data.field.auditStatus,
                    auditRemark: data.field.auditRemark
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.productOrder.get();
                    parent.layui.common.alertAutoClose("审核订单成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 订单申请退款
         * @param {any} data
         */
        refund: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/course/order/refund',
                data: JSON.stringify({
                    orderId: data.field.orderId,
                    reason: data.field.reason,
                    remark: data.field.remark
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.productOrder.get();
                    parent.layui.common.alertAutoClose("订单退款申请提交成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该订单吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.productOrder.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        },
        /**
         * 绑定事件
         * */
        bindEvent: function () {
            layui.$('#order-audit').click(function () {
                var id = layui.$('#hid_id').val();
                layui.common.openIframe('审核订单', 460, 340, 'audit.html?id=' + id);
            });
            layui.$('#order-refund').click(function () {
                var id = layui.$('#hid_id').val();
                layui.common.openIframe('申请退款', 480, 480, 'refund.html?id=' + id);
            });
            //课程订单搜索
            layui.form.on('submit(course-order-search)', function (data) {
                var field = data.field;
                field.companyId = layui.setter.productCompanyId;
                if (data.field.reviewStatus == '') {
                    data.field.reviewStatus = -1;
                }
                if (data.field.payStatus == '') {
                    data.field.payStatus = -1;
                }
                if (data.field.payType == '') {
                    data.field.payType = -1;
                }
                if (data.field.refundStatus == '') {
                    data.field.refundStatus = -1;
                }
                //执行重载
                layui.tableRequest.reload("course-order-list", {
                    where: field
                });
            });
            //触发排序事件
            layui.table.on('sort(course-order-list)', function (obj) {
                if (obj.field == 'payStatus' && obj.type == 'desc') {
                    layui.$('#sort').val(2);
                }
                else if (obj.field == 'payStatus' && obj.type == 'asc') {
                    layui.$('#sort').val(3);
                }
                else if (obj.field == 'refundStatus' && obj.type == 'desc') {
                    layui.$('#sort').val(4);
                }
                else if (obj.field == 'refundStatus' && obj.type == 'asc') {
                    layui.$('#sort').val(5);
                }
                else if (obj.field == 'createdAt' && obj.type == 'asc') {
                    layui.$('#sort').val(6);
                }
                else {
                    layui.$('#sort').val(0);
                }
                var companyId = layui.common.getUrlParam('companyId') || '';
                if (companyId == '') {
                    layui.common.alertAutoClose("企业id有误");
                }
                var payType = layui.$('#payType').val();
                var payStatus = layui.$('#payStatus').val();
                var refundStatus = layui.$('#refundStatus').val();
                var reviewStatus = layui.$('#reviewStatus').val();
                var channelKey = layui.$('#course-channel-view').val();
                var field = {
                    companyId: companyId,
                    keywords: layui.$('#keywords').val(),
                    payType: payType == '' ? -1 : payType,
                    payStatus: payStatus == '' ? -1 : payStatus,
                    refundStatus: refundStatus == '' ? -1 : refundStatus,
                    reviewStatus: reviewStatus == '' ? -1 : reviewStatus,
                    channelKey: channelKey == '' ? -1 : channelKey,
                    sort: layui.$('#sort').val(),
                };
                //执行重载
                layui.tableRequest.reload("course-order-list", {
                    initSort: obj,
                    where: field
                });
            });
        }
    }

    exports('courseOrder', func);
});