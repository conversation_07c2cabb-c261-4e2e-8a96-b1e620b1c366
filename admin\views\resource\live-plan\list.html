﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline {
            width: 200px;
        }
    </style>
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="直播主题" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">直播账号</label>
                        <div class="layui-input-inline">
                            <select name="accountId" id="account-view" lay-search>
                                <option value="">请选择直播账号</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">直播时间</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" />
                        </div>
                    </div>

                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="plan-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                        <button class="layui-btn layuiadmin-btn-list layui-btn-primary" lay-submit
                            lay-filter="plan-export">
                            <i class="layui-icon layui-icon-export layuiadmin-button-btn"></i>导出
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="plan-list" lay-filter="list"></table>
                <script type="text/html" id="plan-bar">
                    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="add">新增巡查</a>
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="update">上传</a>
                    <!-- {{# if(d.videoUrl!=null&&d.videoUrl!=''){}}
                    <a class="layui-btn layui-btn-xs" lay-event="play">播放</a>
                    {{# } else{}}
                    {{# }}} -->
                </script>
            </div>
        </div>
    </div>
    <button type="button" class="layui-btn layui-btn-primary" id="uploadVideo"
        style="width: 0;height: 0;overflow: hidden;position: fixed;top: -100px;">
    </button>
    <script type="text/html" id="topToolBar">
        <div class="layui-btn-container">
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="create"><i class="layui-icon">&#xe654;</i>添加</button>
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="import"><i class="layui-icon layui-icon-export"></i>批量导入计划</button>
        </div>
    </script>
    <script id="account-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/aliyun/aliyun-oss-sdk-6.18.0.min.js"></script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=0823"></script>
    <script type="text/javascript">
        layui.use(['form', 'laydate', 'common', 'livePlan', "uploadSsoFile"], function () {
            layui.uploadSsoFile('sso', 'uploadVideo', function (obj) {
                obj.preview(function (index, file, result) {
                    let accessKeyId, accessKeySecret, securityToken, client;
                    layui.request({
                        method: 'get',
                        url: '/admin/common/get/assume',
                        headers: { 'Content-Type': 'application/json' },
                    }).then(function (res) {
                        if (res.isSuccess) {
                            accessKeyId = res.result.accessKeyId
                            accessKeySecret = res.result.accessKeySecret
                            securityToken = res.result.securityToken
                            let layerLoad = layer.msg('上传中', {
                                icon: 16,
                                shade: 0.01,
                                time: false
                            });
                            client = new OSS({
                                // yourRegion填写Bucket所在地域。以华东1（杭州）为例，Region填写为oss-cn-hangzhou。
                                region: "oss-cn-shanghai",
                                secure: true,
                                // 从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
                                accessKeyId,
                                accessKeySecret,
                                // 从STS服务获取的安全令牌（SecurityToken）。
                                stsToken: securityToken,
                                // 填写Bucket名称，例如examplebucket。
                                bucket: "dn-pub",
                            });
                            layui.media.ossFile(file, client, index, true, (data, filename) => {
                                let formData = {
                                    id: localStorage.getItem("tableId"),
                                    videoUrl: "https://file.dnyx.cn/" + filename
                                }
                                layui.request({
                                    method: 'post',
                                    url: '/admin/live/plan/videourl/update',
                                    data: formData
                                }).then(function (res) {
                                    if (res.isSuccess) {
                                        layui.common.alertAutoClose('上传成功');
                                        layer.close(layerLoad)
                                        layui.livePlan.query();
                                    }
                                })
                            }, "files/live/video/", layerLoad)
                        }
                    })
                });
            });
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            layui.livePlan.initAccount();
            layui.livePlan.query();
            //监听查询按钮
            layui.form.on('submit(plan-search)', function (data) {
                layui.tableRequest.reload("plan-list", {
                    where: data.field
                });
            });
            //监听导出按钮
            layui.form.on('submit(plan-export)', function (data) {
                var confirmIndex = layui.layer.confirm('确定导出直播计划吗？', {
                    icon: 3,
                    title: '提示',
                    btn: ['确定', '取消']
                }, function () {
                    layui.layer.close(confirmIndex);
                    layui.livePlan.export(data);
                }, function () {
                    layui.layer.close(confirmIndex);
                });
            });
        })
    </script>
</body>

</html>