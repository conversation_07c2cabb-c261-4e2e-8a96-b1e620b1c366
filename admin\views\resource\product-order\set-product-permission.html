﻿<!DOCTYPE html>
<html style="background:#fff">
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/lib/font-awesome/font-awesome.min.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class=" layui-form" style="margin-bottom:80px;">
        <div>
            <table id="kf-case-list" lay-filter="list" class="layui-table">
                <thead>
                    <tr>
                        <th style="width:180px;">产品名称</th>
                        <th>投资顾问</th>
                    </tr>
                </thead>
                <tbody id="package-view">
                </tbody>
            </table>
        </div>
        <div class="layui-form-item" style="margin-top:20px;">
            <div class="layui-form-item" style="margin-top: 20px; position: fixed; bottom: 0; text-align: center; background: #fff; width: 100%; margin: 0; padding: 15px 0; border-top: 1px solid #e6e6e6">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="permission-save-submit" value="保存设置">
                <input type="button" lay-submit="" class="layui-btn layui-btn-primary" lay-filter="cancel" value="返回上一页">
            </div>
        </div>
    </div>

    <script id="package-tpl" type="text/html">

        {{#  layui.each(d, function(index, item){ }}
        <tr class="package-item" data-id="{{item.id}}">
            <td>{{item.name}}</td>
            <td>
                {{#  layui.each(item.teachers, function(indexChild, itemChild){ }}
                <input type="checkbox" name="teacherName" class="teacherName" value="{{itemChild.id}}" title="{{itemChild.name}}" {{# if(itemChild.isChecked){}} checked {{# }}} lay-skin="primary">
                {{#  }); }}
            </td>
        </tr>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['userProductOrderPermission'], function () {
            layui.userProductOrderPermission.queryForProduct();
        });
    </script>
</body>
</html>