﻿<!DOCTYPE html>
<html style="background:#fff">
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/lib/font-awesome/font-awesome.min.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class=" layui-form" style="margin-bottom:80px;">
        <div>
            <blockquote class="layui-elem-quote" style="display:none;">
                <div class="layui-row">
                    <div class="layui-col-xs6">
                        用户：<span class="user-name">-</span>
                    </div>

                    <div class="layui-col-xs6" style="text-align:right;">
                        <input type="button" lay-submit="" class="layui-btn layui-btn-sm layui-btn-primary" lay-filter="cancel" value="< 返回上一页">
                        <input type="button" lay-submit="" class="layui-btn layui-btn-sm layui-btn-primary" lay-filter="product-permission" value="设置产订单数据权限">
                    </div>
                </div>
            </blockquote>
            <table id="kf-case-list" lay-filter="list" class="layui-table">
                <thead>
                    <tr>
                        <th style="width:100px;">应用名称</th>
                        <th>加粉方案</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td></td>
                        <td>
                            <input type="checkbox" name="all" class="all" title="全部" lay-skin="primary">
                        </td>
                    </tr>
                </tbody>
                <tbody id="kf-case-view">
                </tbody>
            </table>
        </div>
        <div class="layui-form-item" style="margin-top:20px;">
            <div class="layui-form-item" style="margin-top: 20px; position: fixed; bottom: 0; text-align: center; background: #fff; width: 100%; margin: 0; padding: 15px 0; border-top: 1px solid #e6e6e6">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="permission-save-submit" value="保存设置">
                <input type="button" lay-submit="" class="layui-btn layui-btn-primary" lay-filter="cancel" value="返回上一页">
            </div>
        </div>
    </div>

    <script id="kf-case-tpl" type="text/html">

        {{#  layui.each(d, function(index, item){ }}
        <tr>
            <td>{{item.workWxAppName}}</td>
            <td>
                {{#  layui.each(item.kfCases, function(indexChild, itemChild){ }}
                <span style="display:inline-block;min-width:300px;">
                    <input type="checkbox" name="kfcase" class="kfcase" value="{{itemChild.id}}" title="{{itemChild.name}}" {{# if(itemChild.isChecked){}} checked {{# }}} lay-skin="primary">
                </span>
                {{#  }); }}
            </td>
        </tr>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['userKfCasePermission'], function () {
            layui.userKfCasePermission.query();
        });
    </script>
</body>
</html>