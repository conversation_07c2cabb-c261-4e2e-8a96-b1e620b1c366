﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="background-color: #2d3a4b">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>
    </title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <style>
        .login-container { position: relative; max-width: 400px; padding: 200px 35px 0; margin: 0 auto; overflow: hidden; }
        .login-container h1 { width: 100%; text-align: center; margin: 40px 0; color: #fff; }
        .login-container input.inp { font-size: 14px; border: 1px solid rgba(255, 255, 255, 0.1); background: rgba(0, 0, 0, 0.1); border-radius: 5px; color: #FFFFFF; width: 100%; padding: 26px 10px; box-sizing: border-box; padding-left: 35px; }
        .login-container input.btn { color: #FFFFFF; background-color: #1890ff; border-color: #1890ff; padding: 12px 20px; font-size: 14px; border-radius: 4px; display: inline-block; line-height: 1; white-space: nowrap; cursor: pointer; width: 100%; border: none; }
        .login-container p { position: relative; margin-top: 15px; }
        .login-container p.tips { color: #ff0000 }
        .login-container i { position: absolute; top: 15px; left: 10px; font-size: 20px; color: #ffffff }
        .login-container .title-container { position: relative; }
        .login-container .title-container .title { font-size: 36px; color: #fff; margin: 0px auto 40px auto; text-align: center; font-weight: bold; }
        .login-container .title-container .login-logo { width: 80px; height: 80px; border-radius: 10px; vertical-align: middle; margin-right: 20px; }
        .login-container input.btn-code { background: none; border: 1px solid #fff; padding: 10px 20px; }
    </style>
    <script>
        if (window != window.top) top.location.href = self.location.href;
    </script>
</head>
<body>
    <div class="login-container">
        <div class="layui-form">
            <div class="title-container">
                <h3 class="title">
                    <img id="siteLogo" class="login-logo">
                    <span id="siteName"></span>
                </h3>
            </div>
            <div id="account">
                <p>
                    <i class="layui-icon layui-icon-username"></i>
                    <input name="txb_userName" type="text" id="txb_userName" class="layui-input inp" lay-verify="required" placeholder="请输入用户名" />
                </p>
                <p>
                    <i class="layui-icon layui-icon-password"></i>
                    <input name="txb_password" type="password" id="txb_password" class="layui-input inp" lay-verify="required" placeholder="请输入登录密码" />
                </p>
                <p id="tips" class="tips"></p>
                <p>
                    <input type="button" name="btn_login" value="登   录" id="btn_login" class="layui-btn layui-btn-primary btn" lay-submit="" lay-filter="login" />
                </p>
                <p style="margin-top:10px;display:none;">
                    <input type="button" name="btn_login_code" value="扫码登录" id="btn_login_code" class="layui-btn layui-btn-primary btn btn-code" lay-submit="" lay-filter="qrcode-login" />
                </p>
            </div>
            <div id="wxCode" style="text-align: center; display: none;">
                <div id="qrcode" class="qrcode" style="height:330px"></div>
                <p>
                    <input type="button" name="btn_login_account" value="账号密码登录" style="width:auto;" id="btn_login_account" class="layui-btn layui-btn-primary btn" lay-submit="" lay-filter="btn_login_account" />
                </p>
            </div>
        </div>
        <script src="https://wwcdn.weixin.qq.com/node/wework/wwopen/js/wwLogin-1.2.7.js"></script>
        <script src="/lib/layui/layui.js"></script>
        <script src="/admin/js/global.js?v1.2"></script>
        <script src="/lib/jsencrypt/jsencrypt.min.js"></script>
        <script>
            var wwLogin;
            layui.use(['form', 'login', 'formAuth'], function () {
                layui.formAuth.initPage();
                //监听提交事件
                document.onkeydown = function (e) {
                    // 兼容FF和IE和Opera
                    var theEvent = window.event || e;
                    var code = theEvent.keyCode || theEvent.which || theEvent.charCode;
                    if (code == 13) {
                        layui.$("#btn_login").click();
                    }
                }
                layui.form.on('submit(login)', function (data) {
                    layui.login.exec();
                    return false;
                });
                layui.$('#btn_login_code').click(function () {
                    wwLogin = new WwLogin({
                        "id": "qrcode",
                        "appid": "wwd8c383652444af60",
                        "agentid": "",
                        "redirect_uri": "https://console.dnyx.cn",
                        "state": "",
                        "href": "https://res-f.oss-cn-hangzhou.aliyuncs.com/admin/wwlogin.css",
                        "lang": "zh",
                    });
                    layui.$('#account').hide();
                    layui.$('#wxCode').show();
                });
                layui.$('#btn_login_account').click(function () {
                    wwLogin.destroyed();
                    layui.$('#account').show();
                    layui.$('#wxCode').hide();
                });
            });
        </script>
    </div>
</body>
</html>