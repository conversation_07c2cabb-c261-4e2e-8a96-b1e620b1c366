﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label {
            width: 100px;
        }

        .layui-form-item .layui-input-inline {
            width: 515px;
        }

        .layui-upload-drag {
            width: 100%;
            box-sizing: border-box;
        }

        .col2_left {
            width: 60px !important;
        }

        .col2_right {
            width: 200px !important;
        }

        .red {
            color: #f00
        }
    </style>
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list"
                style="padding: 20px 30px 20px 0;">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="red">* </span>所属组织</label>
                    <div class="layui-input-inline">
                        <select name="subsidiaryId" id="subsidiary-view" lay-verify="required" lay-search></select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="red">* </span>标题</label>
                    <div class="layui-input-inline">
                        <input type="text" name="title" lay-verify="required" placeholder="请输入标题" autocomplete="off"
                            class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span class="red">* </span>类型</label>
                        <div class="layui-input-inline col2_right">
                            <select name="type" lay-verify="required" lay-filter="type">
                                <option value="">请选择类型</option>
                                <option value="1">产品策略</option>
                                <!--<option value="2">福利资料</option>
                                <option value="3">服务资料</option>-->
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline inlineBox">
                        <label class="layui-form-label col2_left"><span class="red">* </span>状态</label>
                        <div class="layui-input-inline col2_right">
                            <select name="opStatus" lay-verify="required" lay-filter="opStatus">
                                <option value="">请选择状态</option>
                                <option value="1">买入</option>
                                <option value="2">止盈</option>
                                <option value="3">止损</option>
                                <option value="4">卖出</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline inlineHidden" style="display: none;">
                        <label class="layui-form-label col2_left"><span class="red">* </span>状态</label>
                        <div class="layui-input-inline col2_right">
                            <select name="opStatus" lay-verify="required" lay-filter="opStatus">
                                <option value="">请选择状态</option>
                                <option value="5">关注</option>
                                <option value="6">取关</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span class="red">* </span>股票代码</label>
                        <div class="layui-input-inline col2_right">
                            <input type="text" name="stockCode" lay-verify="required" placeholder="请输入股票代码"
                                autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label col2_left"><span class="red">* </span>股票名</label>
                        <div class="layui-input-inline col2_right">
                            <input type="text" name="stockName" lay-verify="required" placeholder="请输入股票名称"
                                autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="hidden">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label"><span class="red required_tips">* </span>买入区间</label>
                            <div class="layui-input-inline" style="width: 120px;">
                                <input type="text" name="buyMinAmount" lay-verify="required" placeholder="￥"
                                    autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-form-mid">-</div>
                            <div class="layui-input-inline" style="width: 120px;">
                                <input type="text" name="buyMaxAmount" lay-verify="required" placeholder="￥"
                                    autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label"><span class="red">* </span>目标区间</label>
                            <div class="layui-input-inline" style="width: 120px;">
                                <input type="text" name="sellMinAmount" lay-verify="required" placeholder="￥"
                                    autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-form-mid">-</div>
                            <div class="layui-input-inline" style="width: 120px;">
                                <input type="text" name="sellMaxAmount" lay-verify="required" placeholder="￥"
                                    autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label"><span class="red required_tips">* </span>止损区间</label>
                            <div class="layui-input-inline" style="width: 120px;">
                                <input type="text" name="stopLossPrice" placeholder="￥" autocomplete="off"
                                    lay-verify="required" class="layui-input">
                            </div>
                            <div class="layui-form-mid">-</div>
                            <div class="layui-input-inline" style="width: 120px;">
                                <input type="text" name="stopLossMaxPrice" placeholder="￥" autocomplete="off"
                                    lay-verify="required" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layRequired">
                    <label class="layui-form-label"><span class="red">* </span>仓位(%)</label>
                    <div class="layui-input-inline">
                        <input type="text" name="position" placeholder="请输入仓位（%）" autocomplete="off"
                            lay-verify="required" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item layNoRequired" style="display: none;">
                    <label class="layui-form-label">仓位(%)</label>
                    <div class="layui-input-inline">
                        <input type="text" name="position" placeholder="请输入仓位（%）" autocomplete="off"
                            class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="red">* </span>投资顾问</label>
                    <div class="layui-input-inline">
                        <select name="investmentAdvisorId" id="investmentadvisor-view" lay-verify="required">
                            <option value="">请选择投资顾问</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">资金限制</label>
                    <div class="layui-input-inline">
                        <select name="capitalLimit">
                            <option value="">请选择资金限制</option>
                            <option value="1">vip1以上</option>
                            <option value="2">vip2以上</option>
                            <option value="3">vip3以上</option>
                            <option value="4">vip4以上</option>
                            <option value="5">vip5以上</option>
                            <option value="6">vip6以上</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span class="red">* </span>发布时间</label>
                        <div class="layui-input-inline col2_right">
                            <input type="text" name="releaseAt" id="releaseAt" disabled="disabled" lay-verify="required"
                                placeholder="请选择发布时间" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label col2_left">买入日期</label>
                        <div class="layui-input-inline col2_right">
                            <input type="text" name="buyDate" id="buyDate" placeholder="请选择买入日期" autocomplete="off"
                                class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">卖出日期</label>
                        <div class="layui-input-inline col2_right">
                            <input type="text" name="sellDate" id="sellDate" placeholder="请输入卖出日期" autocomplete="off"
                                class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label col2_left">收益(%)</label>
                        <div class="layui-input-inline col2_right">
                            <input type="text" name="incomePercentage" placeholder="请输入收益（%）" autocomplete="off"
                                class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="red">* </span>内容</label>
                    <div class="layui-input-inline" style="width: 80%">
                        <textarea type="text" name="content" id="content" autocomplete="off"
                            class="layui-textarea"></textarea>
                    </div>
                </div>
                <div class="layui-form-item" style="margin-bottom:100px;">
                    <label class="layui-form-label"><span class="red required_tips">* </span>参考报告</label>
                    <div class="layui-input-inline" style="width: 80%">
                        <textarea type="text" name="referenceReport" id="referenceReport" autocomplete="off"
                            class="layui-textarea"></textarea>
                    </div>
                </div>
                <div
                    style="position: fixed; bottom: 0; left: 0; background: #fff; width: 100%; border-top: 1px solid #eee; padding: 15px 0; text-align: center; z-index: 999; box-shadow: 2px 1px 8px 1px rgba(0,0,0,.1) ">

                    <input type="button" lay-submit="" data-status="1" class="layui-btn layui-btn-normal"
                        lay-filter="recommendstock-submit" value="保存草稿">
                    <input type="button" lay-submit="" data-status="2" class="layui-btn layui-btn-danger"
                        lay-filter="recommendstock-submit" value="送审">
                </div>
            </div>
        </div>
    </div>
    <script id="subsidiary-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="investmentadvisor-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">
            {{item.name}}
            {{# if(item.certNo!=''){}}
            （{{item.certNo}}）
            {{# }}}
        </option>
        {{#  }); }}
    </script>
    <script src="/lib/tinymce/tinymce.min.js"></script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=3.2"></script>
    <script type="text/javascript">
        layui.use(['recommendStock', 'laydate', "jquery"], function () {
            var nowDate = formatTime(new Date(), 'yyyy-MM-dd');
            layui.laydate.render({
                elem: '#releaseAt',
                type: 'date',
                value: nowDate
            });
            layui.laydate.render({
                elem: '#buyDate',
                type: 'date',
                min: nowDate,
                value: nowDate
            });
            layui.laydate.render({
                elem: '#sellDate',
                type: 'date'
            });
            layui.recommendStock.initTinymce('#content');
            layui.recommendStock.initTinymce('#referenceReport');
            layui.recommendStock.initSubsidiary();
            layui.recommendStock.initInvestmentadvisor();
            layui.recommendStock.bindEvent();

            //提交数据
            layui.form.on('submit(recommendstock-submit)', function (data) {
                console.log(data)
                var buyMinAmount = layui.$('input[name=buyMinAmount]').val() || "";
                var buyMaxAmount = layui.$('input[name=buyMaxAmount]').val() || "";
                var sellMinAmount = layui.$('input[name=sellMinAmount]').val() || "";
                var sellMaxAmount = layui.$('input[name=sellMaxAmount]').val() || "";
                var stopLossPrice = layui.$('input[name=stopLossPrice]').val() || "";
                var stopLossMaxPrice = layui.$('input[name=stopLossMaxPrice]').val() || "";
                if (buyMinAmount.indexOf('--') > -1 || buyMinAmount == '') {
                    data.field.buyMinAmount = -1;
                }
                if (buyMaxAmount.indexOf('--') > -1 || buyMaxAmount == '') {
                    data.field.buyMaxAmount = -1;
                }
                if (sellMinAmount.indexOf('--') > -1 || sellMinAmount == '') {
                    data.field.sellMinAmount = -1;
                }
                if (sellMaxAmount.indexOf('--') > -1 || sellMaxAmount == '') {
                    data.field.sellMaxAmount = -1;
                }
                if (stopLossPrice.indexOf('--') > -1 || stopLossPrice == '') {
                    data.field.stopLossPrice = -1;
                }
                if (stopLossMaxPrice.indexOf('--') > -1 || stopLossMaxPrice == '') {
                    data.field.stopLossMaxPrice = -1;
                }
                if (layui.$('input[name=incomePercentage]').val() == '') {
                    data.field.incomePercentage = 0;
                }
                if (layui.$('select[name=capitalLimit]').val() == '') {
                    data.field.capitalLimit = 0;
                }
                if (data.field.position == '' || data.field.position.indexOf('--') > -1) {
                    data.field.position = -1;
                }
                data.field.companyId = layui.setter.companyId;
                data.field.hgAuditStatus = layui.$(data.elem).data('status');
                data.field.content = tinyMCE.get('content').getContent();
                //var formart = tinyMCE.get('content').getContent({ format: 'text' });
                if (data.field.type == 1 && data.field.opStatus == 1 && (data.field.stopLossPrice == -1 || data.field.stopLossMaxPrice == -1)) {
                    layui.$('input[name=stopLossPrice]').focus();
                    layui.common.alertAutoClose("请输入止损区间");
                    return;
                }
                if (data.field.type == 1 && data.field.position == -1) {
                    console.log(data.field.type)
                    layui.$('input[name=position]').focus();
                    layui.common.alertAutoClose("请输入仓位");
                    return;
                }

                data.field.referenceReport = tinyMCE.get('referenceReport').getContent();
                if (data.field.content == '') {
                    layui.common.alertAutoClose("内容不能为空");
                    return;
                }
                if (data.field.opStatus == 1 && data.field.referenceReport == '') {
                    layui.common.alertAutoClose("参考报告不能为空");
                    return;
                }
                layui.recommendStock.create(data);
                return false; //阻止表单跳转
            });
            layui.form.on('select(type)', function (data) {
                if (data.value != 1) {
                    layui.jquery(".hidden").css("display", "none")
                    layui.jquery(".hidden")[0].innerHTML = '';
                    layui.jquery(".layNoRequired").css("display", "block")
                    layui.jquery(".layNoRequired")[0].innerHTML =
                        `
                                <label class="layui-form-label">仓位(%)</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="position" placeholder="请输入仓位（%）" autocomplete="off"
                                        class="layui-input">
                                </div>
                            `
                    layui.jquery(".layRequired").css("display", "none")
                    layui.jquery(".layRequired")[0].innerHTML = '';
                    layui.jquery(".inlineBox").css("display", "none")
                    layui.jquery(".inlineBox")[0].innerHTML = '';
                    layui.jquery(".inlineHidden").css("display", "inline-block")
                    layui.jquery(".inlineHidden")[0].innerHTML =
                        `
                    <label class="layui-form-label col2_left"><span class="red">* </span>状态</label>
                        <div class="layui-input-inline col2_right">
                            <select name="opStatus" lay-verify="required" lay-filter="opStatus">
                                <option value="">请选择状态</option>
                                <option value="5">关注</option>
                                <option value="6">取关</option>
                            </select>
                        </div>
                    `
                } else {
                    layui.jquery(".layNoRequired").css("display", "none")
                    layui.jquery(".layNoRequired")[0].innerHTML = "";
                    layui.jquery(".layRequired").css("display", "block")
                    layui.jquery(".layRequired")[0].innerHTML =
                        `<label class="layui-form-label"><span class="red">* </span>仓位(%)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="position" placeholder="请输入仓位（%）" autocomplete="off"
                                    lay-verify="required" class="layui-input">
                            </div>`
                    layui.jquery(".hidden").css("display", "block")
                    layui.jquery(".hidden")[0].innerHTML =
                        `
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label"><span class="red required_tips">* </span>买入区间</label>
                                    <div class="layui-input-inline" style="width: 120px;">
                                        <input type="text" name="buyMinAmount" lay-verify="required" placeholder="￥"
                                            autocomplete="off" class="layui-input">
                                    </div>
                                    <div class="layui-form-mid">-</div>
                                    <div class="layui-input-inline" style="width: 120px;">
                                        <input type="text" name="buyMaxAmount" lay-verify="required" placeholder="￥"
                                            autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label"><span class="red">* </span>目标区间</label>
                                    <div class="layui-input-inline" style="width: 120px;">
                                        <input type="text" name="sellMinAmount" lay-verify="required" placeholder="￥"
                                            autocomplete="off" class="layui-input">
                                    </div>
                                    <div class="layui-form-mid">-</div>
                                    <div class="layui-input-inline" style="width: 120px;">
                                        <input type="text" name="sellMaxAmount" lay-verify="required" placeholder="￥"
                                            autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label"><span class="red required_tips">* </span>止损区间</label>
                                    <div class="layui-input-inline" style="width: 120px;">
                                        <input type="text" name="stopLossPrice" placeholder="￥" autocomplete="off" lay-verify="required"
                                            class="layui-input">
                                    </div>
                                    <div class="layui-form-mid">-</div>
                                    <div class="layui-input-inline" style="width: 120px;">
                                        <input type="text" name="stopLossMaxPrice" placeholder="￥" autocomplete="off" lay-verify="required"
                                            class="layui-input">
                                    </div>
                                </div>
                            </div>
                            `
                    layui.jquery(".inlineBox").css("display", "inline-block")
                    layui.jquery(".inlineBox")[0].innerHTML = `
                    <label class="layui-form-label col2_left"><span class="red">* </span>状态</label>
                        <div class="layui-input-inline col2_right">
                            <select name="opStatus" lay-verify="required" lay-filter="opStatus">
                                <option value="">请选择状态</option>
                                <option value="1">买入</option>
                                <option value="2">止盈</option>
                                <option value="3">止损</option>
                                <option value="4">卖出</option>
                            </select>
                        </div>
                    `
                    layui.jquery(".inlineHidden").css("display", "none")
                    layui.jquery(".inlineHidden")[0].innerHTML = ""
                }
                layui.form.render("select");
            })
            layui.form.on('select(opStatus)', function (data) {
                switch (data.value) {
                    case "1":
                        layui.jquery(".hidden")[0].innerHTML =
                            `
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label"><span class="red required_tips">* </span>买入区间</label>
                                        <div class="layui-input-inline" style="width: 120px;">
                                            <input type="text" name="buyMinAmount" lay-verify="required" placeholder="￥"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                        <div class="layui-form-mid">-</div>
                                        <div class="layui-input-inline" style="width: 120px;">
                                            <input type="text" name="buyMaxAmount" lay-verify="required" placeholder="￥"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label"><span class="red">* </span>目标区间</label>
                                        <div class="layui-input-inline" style="width: 120px;">
                                            <input type="text" name="sellMinAmount" lay-verify="required" placeholder="￥"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                        <div class="layui-form-mid">-</div>
                                        <div class="layui-input-inline" style="width: 120px;">
                                            <input type="text" name="sellMaxAmount" lay-verify="required" placeholder="￥"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label"><span class="red required_tips">* </span>止损区间</label>
                                        <div class="layui-input-inline" style="width: 120px;">
                                            <input type="text" name="stopLossPrice" placeholder="￥" autocomplete="off" lay-verify="required"
                                                class="layui-input">
                                        </div>
                                        <div class="layui-form-mid">-</div>
                                        <div class="layui-input-inline" style="width: 120px;">
                                            <input type="text" name="stopLossMaxPrice" placeholder="￥" autocomplete="off" lay-verify="required"
                                                class="layui-input">
                                        </div>
                                    </div>
                                </div>
                            `
                        break
                    case "2":
                    case "3":
                    case "4":
                        layui.jquery(".hidden")[0].innerHTML =
                            `
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label"><span class="red">* </span>目标区间</label>
                                        <div class="layui-input-inline" style="width: 120px;">
                                            <input type="text" name="sellMinAmount" lay-verify="required" placeholder="￥"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                        <div class="layui-form-mid">-</div>
                                        <div class="layui-input-inline" style="width: 120px;">
                                            <input type="text" name="sellMaxAmount" lay-verify="required" placeholder="￥"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                            `
                        break

                }
                layui.form.render("select");
            })

        });
        function formatTime(time, fmt) {
            var o = {
                "M+": time.getMonth() + 1, //月份
                "d+": time.getDate(), //日
                "H+": time.getHours(), //小时
                "m+": time.getMinutes(), //分
                "s+": time.getSeconds(), //秒
                "q+": Math.floor((time.getMonth() + 3) / 3), //季度
                "S": time.getMilliseconds() //毫秒
            };
            if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (time.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var k in o)
                if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            return fmt;
        }
    </script>
</body>

</html>