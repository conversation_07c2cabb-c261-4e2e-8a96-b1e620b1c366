﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline {
            width: 200px;
        }

        html,
        body,
        .layui-fluid,
        .layui-card {
            height: 100%;
        }

        .red {
            font-size: 18px;
        }

        .layui-fluid {
            padding: 0;
        }

        .layui-card-header {
            padding: 20px 0 10px 0 !important;
        }

        .layui-card-body {
            padding: 0
        }

        audio {
            background-color: #f2f2f2;
        }

        .total {
            display: none;
        }

        .totalCard {
            padding: 30px;
            margin-left: 30%;
        }

        .totalCard label {
            margin-top: 20px;
            margin-bottom: 20px;
            font-weight: 700;
            margin-right: 100px;
            color: #000;
        }

        .totalCard>div {
            width: 400px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <input id="sort" name="sort" type="hidden" value="0" />
                <div class="layui-form-item handleForm">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属组织</label>
                        <div class="layui-input-inline">
                            <select name="subsidiaryId" id="subsidiary-view" lay-search></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input"
                                placeholder="订单号/产品/手机号/身份证号" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">合规审核</label>
                        <div class="layui-input-inline" style="width:150px;">
                            <select name="auditStatus" id="auditStatus">
                                <option value="">请选择审核状态</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">订单质检</label>
                        <div class="layui-input-inline" style="width:150px;">
                            <select name="fkAuditStatus" id="fkAuditStatus">
                                <option value="">请选择质检状态</option>
                                <option value="10">风险可控</option>
                                <option value="11">有风险</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline handled">
                        <label class="layui-form-label">拨打状态</label>
                        <div class="layui-input-inline">
                            <select name="callStatus" id="callStatus">
                                <option value="">请选择拨打状态</option>
                                <option value="0">未拨打</option>
                                <option value="1">拨打中</option>
                                <option value="2">已接通</option>
                                <option value="3">无人接听</option>
                                <option value="4">关机</option>
                                <option value="5">停号</option>
                                <option value="6">空号</option>
                                <option value="7">拒接</option>
                                <option value="9">用户正忙</option>
                                <option value="10">系统拦截</option>
                                <option value="11">线路拦截</option>
                                <option value="12">超时失效</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline handled">
                        <label class="layui-form-label">拨打结果</label>
                        <div class="layui-input-inline">
                            <select name="intentionCategoryId" id="intentionCategoryId">
                                <option value="">请选择拨打结果</option>
                                <option value="99">无触达</option>
                                <option value="0">未接通</option>
                                <option value="1">通过</option>
                                <option value="2">不通过</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">购买产品</label>
                        <div class="layui-input-inline">
                            <select name="productId" id="product-view" lay-search>
                                <option value="">请选择购买产品</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">所属用户</label>
                        <div class="layui-input-inline">
                            <select name="saleUserId" id="admin-user-view" lay-search>
                                <option value="">请选择订单所属用户</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">支付时间</label>
                        <div class="layui-input-inline" style="width:150px">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间"
                                autocomplete="off" />
                        </div>
                        <div class="layui-input-inline" style="width:150px">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间"
                                autocomplete="off" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list " lay-submit lay-filter="product-order-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
                <div class="layui-form-item total">
                    <div class="layui-inline">
                        <label class="layui-form-label">统计日期</label>
                        <div class="layui-input-inline">
                            <input id="date" name="date" type="text" id="totalDate" class="layui-input"
                                placeholder="yyyy-MM-dd">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list " lay-submit lay-filter="product-total-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body handleForm">
                <table id="product-order-list" lay-filter="product-order-list"></table>
                <script type="text/html" id="product-order-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
                    {{# if(layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.compliance.audit')&&d.auditStatus==1){}}
                    <a class="layui-btn  layui-btn-xs" lay-event="compliance-audit">审核</a>
                    {{#}}}
                    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="chat">对话</a>
                    {{# if(d.callStatus!=1&&d.voiceUrl!=null&&d.voiceUrl!=''){}}
                    <a class="layui-btn layui-btn-xs layui-btn-primary player" lay-event="player">播放</a>
                    <a class="layui-btn layui-btn-xs layui-btn-primary download" lay-event="download">下载</a>
                    {{#}}}
                </script>
            </div>
            <div class="total layui-card-body totalCard ">
                <div>
                    <label>总拨打数:</label> <span id="totalJobNum">-</span>
                </div>
                <div>
                    <label>总接通数:</label> <span id="totalCompleted">-</span>
                </div>
                <div>
                    <label>完成率(总接通数/总拨打数):</label> <span id="completionRate">-</span>
                </div>
                <div>
                    <label>通过数:</label> <span id="passNum">-</span>
                </div>
                <div>
                    <label> 通过率(通过数/总接通数):</label> <span id="passRate">-</span>
                </div>
                <div>
                    <label> 失败通话数:</label> <span id="failed">-</span>
                </div>
                <div>
                    <label> 执行中的通话数:</label> <span id="executing">-</span>
                </div>
                <div>
                    <label> 调度中的通话数:</label> <span id="scheduling">-</span>
                </div>
            </div>
        </div>
    </div>
    <div id=" audio-dialog" style="display:none;">
        <audio id="audio_dom" style="width:100%;" autoplay="autoplay" controls>
            <source src="" type="audio/mpeg">
        </audio>
    </div>
    <div id="preview" class="preview" style="display:none;">
    </div>
    <script id="subsidiary-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="admin-user-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="product-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=2.3"></script>
    <script type="text/javascript">
        var salerUserList = [];
        layui.use(['productOrder', "jquery"], function () {
            var auditType = layui.common.getUrlParam('auditType');
            //var headerHeight = $('.layuiadmin-card-header-auto').height(); console.log(headerHeight);
            if (auditType != 3) {
                layui.productOrder.initSubsidiary();
                layui.productOrder.initProduct();
                layui.productOrder.initAdminUser('list');
                layui.productOrder.queryForCompliance();
                layui.laydate.render({
                    elem: '#startTime',
                    type: 'datetime'
                });
                layui.laydate.render({
                    elem: '#endTime',
                    type: 'datetime'
                });
                layui.form.on('submit(product-order-search)', function (data) {
                    if (data.field.signFlowStatus == '') {
                        data.field.signFlowStatus = -1;
                    }
                    if (data.field.scene == '') {
                        data.field.scene = -1;
                    }
                    if (data.field.auditStatus == '') {
                        data.field.auditStatus = -1;
                    }
                    if (data.field.fkAuditStatus == '') {
                        data.field.fkAuditStatus = -1;
                    }
                    if (data.field.callStatus == '') {
                        data.field.callStatus = -1;
                    }
                    if (data.field.intentionCategoryId == '') {
                        data.field.intentionCategoryId = -1;
                    }
                    layui.tableRequest.reload("product-order-list", {
                        where: data.field,
                        page: {
                            curr: 1
                        }
                    });
                });
                layui.table.on('tool(product-order-list)', function (obj) {
                    var data = obj.data;
                    if (obj.event === 'detail') {
                        location.href = 'detail.html?id=' + data.id;
                    }
                    else if (obj.event == 'compliance-audit') {
                        layui.common.openIframe('审核订单', 570, 500, 'audit-compliance.html?id=' + data.id);
                    }
                    else if (obj.event === 'chat') {
                        layui.common.openIframe('聊天对话', 600, 540, '../callbot/chat.html?id=' + data.id + '&callTime=' + data.callTime);
                    }
                    else if (obj.event == 'player') {
                        if (data.voiceUrl == null || data.voiceUrl == '') {
                            layui.common.alertAutoClose("当前无可播放的录音文件");
                            return;
                        }
                        document.getElementById('audio_dom').src = data.voiceUrl;
                        layui.common.openPage('播放录音', 500, 100, '#audio-dialog');
                    }
                    else if (obj.event == 'download') {
                        if (data.voiceUrl == null || data.voiceUrl == '') {
                            layui.common.alertAutoClose("当前无可下载的录音文件");
                            return;
                        }
                        window.open(data.voiceUrl);
                    }
                });
                if (!layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.copy')) {
                    layui.$(document).bind("contextmenu", function () { return false; });
                    layui.$(document).bind("selectstart", function () { return false; });
                }
            } else if (auditType == 3) {
                layui.laydate.render({
                    elem: '#date',
                    value: date = new Date().toLocaleDateString()
                        .split("/")
                        .join("-"),
                });
                layui.jquery(".handleForm").css("display", "none")
                layui.jquery(".total").css("display", "inline-block")
                layui.productOrder.getAliStatis()
                layui.form.on('submit(product-total-search)', function (data) {
                    let date = data.field.date || undefined
                    layui.productOrder.getAliStatis(date)
                })
            }
        });
    </script>
</body>

</html>