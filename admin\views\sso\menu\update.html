﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/lib/font-awesome/font-awesome.min.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 500px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 0 0 0;">
        <input id="id" name="id" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">菜单名称</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入系统菜单名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">页面地址</label>
            <div class="layui-input-inline">
                <input type="text" name="url" placeholder="请输入系统菜单页面地址" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">图标</label>
                <div class="layui-input-inline" style="width: 370px;">
                    <input type="text" name="icon" placeholder="请输入图标编码" id="txb_icon" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-input-inline" style="width: 15px; line-height: 36px; height: 36px" id="show_icon"></div>
                <div class="layui-input-inline" style="width: 100px;">
                    <button type="button" class="layui-btn layui-btn-primary" id="icon_btn">选择图标</button>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">排序ID</label>
                <div class="layui-input-inline">
                    <input type="text" name="sort" lay-verify="required|number" placeholder="请输入排序" autocomplete="off" class="layui-input">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">是否显示</label>
                <div class="layui-input-inline">
                    <input id="isShow" name="isShow" lay-skin="switch" lay-text="是|否" type="checkbox">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">功能权限</label>
            <div style="width: 560px; float: left; ">
                <div class="menu-input-list" style="float:left;">
                </div>
                <button type="button" class="layui-btn layui-btn-primary add-menu">
                    <i class="layui-icon">&#xe654;</i> 添加权限
                </button>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="menu-submit" value="确认修改">
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['menu'], function () {
            layui.menu.get();
            layui.menu.bindEvent('update');

            //打开图标列表
            layui.$('#icon_btn').click(function () {
                parent.layui.common.openIframe('请选择图标', 782, 530, 'icon.html');
            });
        });
    </script>
</body>
</html>