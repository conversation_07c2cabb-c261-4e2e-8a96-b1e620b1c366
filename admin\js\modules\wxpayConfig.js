﻿layui.define(['laytpl', 'form', 'request', 'tableRequest', 'common'], function (exports) {
    var func = {
        /**
         * 通过id获取微信支付配置信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                setTimeout(function () {
                    parent.location.reload();
                }, 3000);
                return;
            }
            layui.request({
                requestBase: 'log',
                method: 'post',
                url: '/admin/config/wxpay/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    var getTpl = document.getElementById("app-update-tpl").innerHTML
                        , view = document.getElementById('app-update-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    })
                    layui.form.render('select');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取微信支付配置列表
         * */
        query: function () {
            layui.tableRequest.request('pay', false, 'wxpay-config-list', '/admin/config/wxpay/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '配置名称' },
                { field: 'appId', title: '绑定公众号APPID' },
                { field: 'mchId', title: '微信商户ID' },
                { field: 'serialNo', title: '证书序列号' },
                {
                    field: 'createdAt', title: '创建时间', templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', align: 'left', toolbar: '#wxpay-config-bar' }
            ]);
            //监听表格事件
            layui.wxpayConfig.tableEvent();
        },
        /**
        * 创建应用
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'log',
                method: 'post',
                url: '/admin/config/wxpay/create',
                data: JSON.stringify({
                    appId: data.field.appId,
                    appName: data.field.appName,
                    appType: data.field.appType
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("应用创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑应用
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'log',
                method: 'post',
                url: '/admin/config/wxpay/update',
                data: JSON.stringify({
                    id: data.field.id,
                    appName: data.field.appName,
                    appType: data.field.appType
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("应用编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除应用
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                requestBase: 'log',
                method: 'post',
                url: '/admin/config/wxpay/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("应用删除成功");
                    layui.wxpayConfig.query();
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑应用', 550, 300, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该应用吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.wxpayConfig.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('wxpayConfig', func);
});