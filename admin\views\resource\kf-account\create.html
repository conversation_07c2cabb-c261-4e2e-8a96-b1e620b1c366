﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 400px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <input id="workWxAppId" name="workWxAppId" type="hidden" />
        <input id="mediaId" name="mediaId" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">客服名称</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入客服名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">客服头像</label>
            <div class="layui-input-inline">
                <div class="layui-upload-drag" id="uploadImg">
                    <i class="layui-icon"></i>
                    <p>点击上传，或将文件拖拽到此处</p>
                    <div class="layui-hide" id="uploadDemoView">
                        <hr>
                        <img src="" alt="上传成功后渲染" style="max-width: 196px">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="kf-account-create-submit" value="确认添加">
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'uploadFile', 'kfAccount'], function () {
            var workWxAppId = layui.common.getUrlParam('workWxAppId');
            if (workWxAppId == '') {
                layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.$("#workWxAppId").val(workWxAppId);
            //上传头像
            layui.uploadFile('Resource', 'uploadImg', '/admin/workwx/kf-account/avatar/upload?workWxAppId=' + workWxAppId, function (res) {
                if (res.isSuccess) {
                    layui.$("#mediaId").val(res.result.mediaId);
                    layui.common.alertAutoClose("上传成功");
                    layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.result.url);
                }
                else {
                    layui.common.alert(res.message, 2);
                }
            });
            layui.form.on('submit(kf-account-create-submit)', function (data) {
                console.log(data)
                layui.kfAccount.create(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>