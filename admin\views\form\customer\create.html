﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">系统名称</label>
            <div class="layui-input-inline">
                <input type="text" name="alias" lay-verify="required" placeholder="请输入系统名称（客户别名）" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">姓名</label>
            <div class="layui-input-inline">
                <input type="text" name="realName" lay-verify="required" placeholder="请输入客户姓名" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">自动分配</label>
            <div class="layui-input-inline">
                <select name="isAutoDistribution" lay-filter="auto-distribution-view" lay-verify="required">
                    <option value="false">不开启资源自动分配</option>
                    <option value="true">开启资源自动分配</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">分配用户角色</label>
            <div class="layui-input-inline">
                <select name="distributionRoleId" id="role-view" lay-search>
                    <option value="">请选择分配用户角色</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="customer-create-submit" value="确认添加">
            </div>
        </div>
    </div>
    <script id="role-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'setter', 'customer'], function () {
            layui.customer.initRole(layui.setter.companyId);
            //监听提交事件
            layui.form.on('submit(customer-create-submit)', function (data) {
                layui.customer.create(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>