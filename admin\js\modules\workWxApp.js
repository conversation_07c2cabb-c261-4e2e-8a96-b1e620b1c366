﻿layui.define(['laytpl', 'request', 'tableRequest', 'tree', 'common'], function (exports) {
    var func = {
        /**
         * 通过id获取企微应用信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                setTimeout(function () {
                    parent.location.reload();
                }, 3000);
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/app/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    var getTpl = document.getElementById("app-update-tpl").innerHTML
                        , view = document.getElementById('app-update-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    })
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取所有企微应用列表
         * */
        getAll: function (callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/app/get-all',
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 通过id获取企微用户列表
         * @param {any} workWxAppId
         * @param {any} callbackFunc
         */
        getWorkWxUser: function (workWxAppId, callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/department-user/get-all?workWxAppId=' + workWxAppId,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 获取企微标签列表
         * @param {any} workWxAppId
         * @param {any} callbackFunc
         */
        getCorpTags: function (workWxAppId, callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/app/corp-tag/get-all?id=' + workWxAppId,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
        * 获取部门列表
        * @param {any} workWxAppId
        */
        getDepartmentTree: function (workWxAppId) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/department/tree/get?workWxAppId=' + workWxAppId,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tree.render({
                        elem: '#department-view'
                        , data: res.result
                        , onlyIconControl: true
                        , click: function (obj) {
                            layui.$(".layui-tree-set").removeClass('layui-tree-set-active');
                            obj.elem.addClass('layui-tree-set-active');
                            var departmentUsers = [];
                            var isChecked = true;
                            for (var i = 0; i < weWorkUsers.length; i++) {
                                if (res.result[0].id == obj.data.id) {
                                    departmentUsers.push(weWorkUsers[i]);
                                    if (!weWorkUsers[i].isChecked) {
                                        isChecked = false;
                                    }
                                }
                                else {
                                    if (layui.$.inArray(obj.data.id, weWorkUsers[i].departmentIds) > -1) {
                                        departmentUsers.push(weWorkUsers[i]);
                                        if (!weWorkUsers[i].isChecked) {
                                            isChecked = false;
                                        }
                                    }
                                }
                            }
                            layui.$(".left_user_all").prop("checked", isChecked);
                            var getTpl = document.getElementById("workwx-user-tpl").innerHTML
                                , view = document.getElementById('workwx-user-view');
                            layui.laytpl(getTpl).render(departmentUsers, function (html) {
                                view.innerHTML = html;
                            });
                            layui.form.render('checkbox');
                        }
                    });
                    if (res.result.length > 0) {
                        layui.$('.layui-tree .layui-tree-set').eq(0).addClass('layui-tree-set-active');
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 通过应用id获取企微成员列表
        * @param {any} workWxAppId
        * @param {any} selectIds
        */
        getWeWorkUser: function (workWxAppId) {
            var ids = layui.common.getUrlParam('ids') || '';
            var arr = [];
            if (ids != '') {
                arr = ids.split(',');
            }
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/workwx/department-user/get-all?workWxAppId=' + workWxAppId,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    var data = [];
                    var selectData = [];
                    var allIsChecked = true;
                    if (res.result.length > 0) {
                        for (var i = 0; i < res.result.length; i++) {
                            var isChecked = false;
                            if (arr.length > 0) {
                                for (var j = 0; j < arr.length; j++) {
                                    if (res.result[i].userId == arr[j]) {
                                        selectData.push({ name: res.result[i].name, userId: res.result[i].userId, userIdFormat: res.result[i].userIdFormat, departmentIds: res.result[i].departmentIds, isChecked: true })
                                        isChecked = true; break;
                                    }
                                }
                            }
                            if (isChecked == false) {
                                allIsChecked = false;
                            }
                            var item = { name: res.result[i].name, userId: res.result[i].userId, userIdFormat: res.result[i].userIdFormat, departmentIds: res.result[i].departmentIds, isChecked: isChecked };
                            data.push(item);
                        }
                        layui.$(".left_user_all").prop("checked", allIsChecked);

                        //待选择员工渲染
                        var getTpl = document.getElementById("workwx-user-tpl").innerHTML
                            , view = document.getElementById('workwx-user-view');
                        layui.laytpl(getTpl).render(data, function (html) {
                            view.innerHTML = html;
                        });
                        //已选择员工渲染
                        var getTplSelect = document.getElementById("workwx-user-select-tpl").innerHTML
                            , viewSelect = document.getElementById('workwx-user-select-view');
                        layui.laytpl(getTplSelect).render(selectData, function (html) {
                            viewSelect.innerHTML = html;
                        });
                        layui.$('.right-user .top .num').text('(' + selectData.length + ')');
                        layui.form.render('checkbox');
                    }
                    weWorkUsers = data;
                }
            })
        },
        /**
         * 获取企微应用列表
         * */
        query: function () {
            layui.tableRequest.request('resource', false, 'app-list', '/admin/workwx/app/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '企微应用名称' },
                { field: 'corpId', title: '企业微信ID' },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 150, align: 'left', toolbar: '#app-bar' }
            ]);
            //监听表格事件
            layui.workWxApp.tableEvent();
        },
        /**
        * 创建企微应用
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/app/create',
                data: JSON.stringify({
                    name: data.field.name,
                    corpId: data.field.corpId,
                    corpSecret: data.field.corpSecret,
                    token: data.field.token,
                    encodingAESKey: data.field.encodingAESKey,
                    kfCorpSecret: data.field.kfCorpSecret,
                    kfToken: data.field.kfToken,
                    kfEncodingAESKey: data.field.kfEncodingAESKey,
                    msgCorpSecret: data.field.msgCorpSecret,
                    msgPrivateKey: data.field.msgPrivateKey,
                    mailListAgentId: data.field.mailListAgentId,
                    mailListSecret: data.field.mailListSecret,
                    kfAgentId: data.field.kfAgentId
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("企微应用创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑企微应用
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/app/update',
                data: JSON.stringify({
                    id: data.field.id,
                    name: data.field.name,
                    corpId: data.field.corpId,
                    corpSecret: data.field.corpSecret,
                    token: data.field.token,
                    encodingAESKey: data.field.encodingAESKey,
                    kfCorpSecret: data.field.kfCorpSecret,
                    kfToken: data.field.kfToken,
                    kfEncodingAESKey: data.field.kfEncodingAESKey,
                    msgCorpSecret: data.field.msgCorpSecret,
                    msgPrivateKey: data.field.msgPrivateKey,
                    mailListAgentId: data.field.mailListAgentId,
                    mailListSecret: data.field.mailListSecret,
                    kfAgentId: data.field.kfAgentId
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("企微应用编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除企微应用
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/app/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("企微应用删除成功");
                    layui.workWxApp.query();
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑企微应用', 750, 720, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该企微应用吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.workWxApp.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('workWxApp', func);
});