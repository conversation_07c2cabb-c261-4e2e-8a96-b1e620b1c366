﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .mid-c { display: flex; }
        .mid-c .left { padding: 15px; box-sizing: border-box; border-right: 1px solid #ccc; width: 400px; }
        .mid-c .right { padding: 15px; box-sizing: border-box; width: 100%; }
        .layui-tree-set-active > .layui-tree-entry { background: #f2f2f2; }
        .layui-tree-set-active > .layui-tree-entry:hover { background: #f2f2f2; }
        .link { color: #50b1fb; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="mid-c">
                <div class="left">
                    <div style="text-align:center;margin-bottom:10px;width:100%;">
                        <button type="button" class="layui-btn" lay-submit lay-filter="department-sync">同步企微部门</button>
                        <button type="button" class="layui-btn layui-btn-warm" lay-submit lay-filter="department-user-sync">同步部门成员</button>
                    </div>
                    <div id="department-view"></div>
                </div>
                <div class="right layui-form">
                    <div style="width:100%;">
                        <input id="departmentId" name="departmentId" type="hidden" />
                        <div class="layui-form-item" style="margin-bottom:0">
                            <div class="layui-inline">
                                <label class="layui-form-label">关键词</label>
                                <div class="layui-input-inline">
                                    <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="请输入企微成员姓名" />
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">添加时间</label>
                                <div class="layui-input-inline">
                                    <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" />
                                </div>
                                <div class="layui-input-inline">
                                    <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" />
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="stat-user-search">
                                    <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body" style="flex: 0 0 auto;">
                        <table id="stat-user-list" lay-filter="list"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="demo1"></div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'laydate', 'common', 'externalContact'], function () {

            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime',
                value: new Date(new Date(new Date().toLocaleDateString()).getTime())
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime',
                value: new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
            });
            var workWxAppId = layui.common.getUrlParam('workWxAppId') || '';
            if (workWxAppId == '') {
                layui.common.alertAutoClose("企微应用id有误");
                return;
            }
            layui.externalContact.getDepartmentTree(workWxAppId);

            //监听查询按钮
            layui.form.on('submit(stat-user-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("stat-user-list", {
                    where: field
                });
            });
            //同步部门按钮
            layui.form.on('submit(department-sync)', function (data) {
                var confirmIndex = layui.layer.confirm('确认同步企微部门数据吗？', {
                    icon: 3,
                    title: '提示',
                    btn: ['确定', '取消']
                }, function () {
                    layui.externalContact.syncDepartment(workWxAppId);
                    layui.layer.close(confirmIndex);
                }, function () {
                    layui.layer.close(confirmIndex);
                });
            });
            //同步部门用户按钮
            layui.form.on('submit(department-user-sync)', function (data) {
                var confirmIndex = layui.layer.confirm('确认同步企微部门成员数据吗？', {
                    icon: 3,
                    title: '提示',
                    btn: ['确定', '取消']
                }, function () {
                    layui.externalContact.syncDepartmentUser(workWxAppId);
                    layui.layer.close(confirmIndex);
                }, function () {
                    layui.layer.close(confirmIndex);
                });
            });
        })
    </script>
</body>
</html>