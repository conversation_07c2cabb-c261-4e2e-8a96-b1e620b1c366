﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 400px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">所属客户</label>
            <div class="layui-input-inline">
                <select name="customerId" id="customer-view" lay-search></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">类型</label>
            <div class="layui-input-inline">
                <select name="type" lay-verify="required">
                    <option value="">请选择类型</option>
                    <option value="1">客户端IP</option>
                    <option value="2">手机号</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">IP/手机号</label>
            <div class="layui-input-inline">
                <input type="text" name="val" lay-verify="required" placeholder="请输入IP/手机号" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">拦截提示</label>
            <div class="layui-input-inline">
                <textarea name="tips" id="tips" placeholder="请输入拦截提示，不填则返回“拒绝访问”提示" autocomplete="off" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="blacklist-create-submit" value="确认添加">
            </div>
        </div>
    </div>
    <script id="customer-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.alias}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'blacklist'], function () {
            layui.blacklist.initCustomer('create');
            //监听提交事件
            layui.form.on('submit(blacklist-create-submit)', function (data) {
                layui.blacklist.create(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>