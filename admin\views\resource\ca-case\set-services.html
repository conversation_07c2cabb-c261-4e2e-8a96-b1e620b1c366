﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 400px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">所属企微</label>
            <div class="layui-input-inline">
                <select name="workWxAppId" id="workwx-app-view" lay-filter="workwx-app" lay-verify="required">
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-inline">
                <input type="radio" name="opType" value="1" title="新增员工" checked="checked">
                <input type="radio" name="opType" value="2" title="移除员工">
                <input type="radio" name="opType" value="3" title="重置员工">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">承接员工</label>
            <div style="display:inline-block;width:400px;">
                <div class="layui-input-inline" id="workwx-user">
                </div>
                <div><a href="javascript:;" class="open-user-select" style="color:#0689b4;font-size:14px;">+按组织架构添加</a></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="kf-case-submit" value="批量设置">
            </div>
        </div>
    </div>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        var xmSelUser;
        layui.use(['caCase'], function () {
            xmSelUser = layui.xmSelect.render({
                el: '#workwx-user',
                language: 'zn',
                filterable: true,
                tips: '请选择承接用户',
                theme: { color: '#0081ff ' },
                data: [],
                toolbar: { show: true },
                autoRow: true,
            });
            layui.caCase.initApp();
            //企微应用选择后，加载员工账号列表
            layui.form.on('select(workwx-app)', function (data) {
                layui.caCase.initWorkUser(data.value, []);
            });
            //提交数据
            layui.form.on('submit(kf-case-submit)', function (data) {
                var caseIds = layui.common.getUrlParam('ids');
                var users = xmSelUser.getValue();
                if (users == null || users.length == 0) {
                    layui.common.alertAutoClose('请选择承接人员');
                    return;
                }
                var userIds = [];
                for (var i = 0; i < users.length; i++) {
                    userIds.push(users[i].value);
                }
                data.field.userIds = userIds;
                data.field.caseIds = caseIds.split(',');
                layui.caCase.updateUserIds(data);
                return false; //阻止表单跳转
            });
            //接收选择框消息
            top.addEventListener('message', event => {
                xmSelUser.setValue(event.data);
            });

            //打开选择框
            layui.$('.open-user-select').click(function () {
                var workWxAppId = layui.$('#workwx-app-view').val();
                if (workWxAppId == '') {
                    layui.common.alertAutoClose('请选择所属企微');
                    return;
                }
                var users = xmSelUser.getValue();
                var userIds = [];
                if (users.length > 0) {
                    for (var i = 0; i < users.length; i++) {
                        userIds.push(users[i].value);
                    }
                }
                parent.layui.common.openIframe('选择员工', 890, 630, '../workwx-app/select-user.html?workWxAppId=' + workWxAppId + '&ids=' + userIds.toString());
            });
        });
    </script>
</body>
</html>