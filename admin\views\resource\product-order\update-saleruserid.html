﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 80px; }
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">分配用户：</label>
                <div class="layui-input-inline">
                    <select name="saleUserId" id="admin-user-view" lay-search>
                    </select>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="order-update-saleruserid-submit" value="确认保存">
            </div>
        </div>
    </div>
    <script id="admin-user-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">
            {{item.name}}
            {{#if(item.account!=''){}}
            （{{item.account}}）
            {{# }else{ }}
            {{item.account}}
            {{# } }}
        </option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['productOrder'], function () {
            layui.productOrder.initAdminUser();
            //监听提交事件
            layui.form.on('submit(order-update-saleruserid-submit)', function (data) {
                layui.productOrder.updateSalerUserId(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>