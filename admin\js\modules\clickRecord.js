﻿layui.define(['laytpl', 'form', 'laydate', 'request', 'tableRequest', 'common', 'workWxApp', 'kfCase'], function (exports) {
    var func = {
        /**
        * 渲染应用下拉选框
        * */
        initApp: function (id) {
            layui.workWxApp.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企微' });
                var getTpl = document.getElementById("workwx-app-tpl").innerHTML
                    , view = document.getElementById('workwx-app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('workwx-app-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
        * 获取落地页点击记录列表
        * */
        query: function () {
            layui.tableRequest.request('resource', true, 'click-record-list', '/admin/workwx/click-record/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'workWxAppName', title: '企微名称' },
                { field: 'kfCaseName', title: '加粉方案' },
                { field: 'scene', title: '使用场景' },
                { field: 'sceneParam', title: '场景值' },
                { field: 'openKfId', title: '客服ID' },
                { field: 'pageUrl', title: '落地页地址' },
                { field: 'clickId', title: '线索ID' },
                { field: 'clientIp', title: '客户端IP' },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                }
            ]);
        },
        /**
         * 绑定事件
         * */
        bindEvent: function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            //监听查询按钮
            layui.form.on('submit(click-record-search)', function (data) {
                var field = data.field;
                if (field.callbackStatus == '') {
                    field.callbackStatus = -1;
                }
                if (field.reportStatus == '') {
                    field.reportStatus = -1;
                }
                //执行重载
                layui.tableRequest.reload("click-record-list", {
                    where: field
                });
            });
            //切换企微时加载加粉方案
            layui.form.on('select(workwx-app-id)', function (data) {
                layui.kfCase.queryByWorkWxAppId(data.value, function (res) {
                    if (res.result == null) {
                        res.result = [{ id: '', name: '请选择加粉方案' }]
                    }
                    else {
                        res.result.unshift({ id: '', name: '请选择加粉方案' });
                    }
                    var getTpl = document.getElementById("kf-case-tpl").innerHTML
                        , view = document.getElementById('kf-case-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.form.render('select');
                });
            });
        }
    };
    exports('clickRecord', func);
});