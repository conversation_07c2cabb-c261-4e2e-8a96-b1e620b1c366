﻿layui.define(['laytpl', 'form', 'request', 'uploadFile', 'tableRequest', 'common', 'courseChannel', 'adminUser'], function (exports) {
    var func = {
        /**
         * 渲染课程渠道下拉选框
         * */
        initCourseChannel: function () {
            layui.courseChannel.getAll(function (res) {
                res.result.unshift({ key: '', name: '请选择渠道' });
                var getTpl = document.getElementById("course-channel-tpl").innerHTML
                    , view = document.getElementById('course-channel-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('select');
            });
        },
        /**
        * 渲染所属用户列表
        */
        initAdminUser: function () {
            var companyId = layui.common.getUrlParam('companyId') || '';
            if (companyId == '') {
                layui.common.alertAutoClose("企业id有误");
            }
            layui.adminUser.getUserList(companyId, function (res) {
                res.result.unshift({ id: '', name: '请选择所属用户', account: '' });
                var getTpl = document.getElementById("admin-user-tpl").innerHTML
                    , view = document.getElementById('admin-user-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                var aUserStr = localStorage.getItem('ly-admin-user');
                if (aUserStr != null) {
                    var aUserObj = JSON.parse(aUserStr);
                    document.getElementById('admin-user-view').value = aUserObj.id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 初始化富文本编辑器
         * */
        initTinymce: function () {
            tinymce.init({
                selector: '#detail',
                language: 'zh_CN',
                plugins: 'preview searchreplace autolink directionality visualblocks visualchars fullscreen image link media template code codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists wordcount imagetools help emoticons autosave autoresize axupimgs',
                toolbar: 'code cut copy paste pastetext | forecolor backcolor bold italic underline strikethrough link | alignleft aligncenter alignright alignjustify outdent indent | styleselect formatselect fontselect fontsizeselect | bullist numlist | blockquote subscript superscript removeformat | table image media charmap emoticons pagebreak insertdatetime preview fullscreen bdmap indent2em lineheight formatpainter axupimgs',
                min_height: 500,
                max_height: 500,
                fontsize_formats: '12px 14px 16px 18px 24px 36px 48px 56px 72px',
                font_formats: '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;',
                importcss_append: true,
                images_upload_handler(blobInfo, progress) {
                    return new Promise((resolve, reject) => {
                        var upurl = layui.setter.request.resourceBaseUrl + '/common/file/public/tinymce/put?folder=files/course/detail';
                        var file = blobInfo.blob();
                        var xhr, formData;
                        xhr = new XMLHttpRequest();
                        xhr.withCredentials = false;
                        xhr.open('POST', upurl);
                        xhr.onload = function () {
                            if (xhr.status != 200) {
                                reject('HTTP Error: ' + xhr.status);
                                return;
                            }
                            var json = JSON.parse(xhr.responseText);
                            if (!json || json.location == '') {
                                reject(xhr.responseText);
                                return;
                            }
                            resolve(json.location);
                        };
                        formData = new FormData();
                        formData.append('file', file, file.name);
                        xhr.send(formData);
                    })
                },
                //自定义文件选择器的回调内容
                file_picker_callback: function (callback, value, meta) {
                    var filetype = '.pdf, .txt, .zip, .rar, .7z, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .mp3, .mp4';
                    var upurl = layui.setter.request.resourceBaseUrl + '/common/file/public/tinymce/put?folder=files/course/detail';
                    switch (meta.filetype) {
                        case 'image':
                            filetype = '.jpg, .jpeg, .png, .gif';
                            break;
                        case 'media':
                            filetype = '.mp3, .mp4';
                            break;
                        default:
                    }
                    var input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    input.setAttribute('accept', filetype);
                    input.click();
                    input.onchange = function () {
                        var file = this.files[0];
                        var xhr, formData;
                        xhr = new XMLHttpRequest();
                        xhr.withCredentials = false;
                        xhr.open('POST', upurl);
                        xhr.onload = function () {
                            var json;
                            if (xhr.status != 200) {
                                failure('HTTP Error: ' + xhr.status);
                                return;
                            }
                            json = JSON.parse(xhr.responseText);
                            if (!json || typeof json.location != 'string') {
                                failure('Invalid JSON: ' + xhr.responseText);
                                return;
                            }
                            callback(json.location);
                        };
                        formData = new FormData();
                        formData.append('file', file, file.name);
                        xhr.send(formData);
                    };
                },
                autosave_ask_before_unload: false
            });
        },
        /**
         * 上传封面
         * */
        initFile: function () {
            layui.uploadFile('Resource', 'uploadImg', '/common/file/public/put?folder=files/course/cover', function (res) {
                if (res.isSuccess) {
                    layui.$("#hid_coverUrl").val(res.result);
                    layui.common.alertAutoClose("上传成功");
                    layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.result);
                }
                else {
                    layui.common.alert(res.message, 2);
                }
            });
        },
        /**
         * 通过id获取单个课程信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/course/info/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    if (layui.$('#lab_course').length > 0) {
                        layui.$('#lab_course').text(res.result.name + ' / ¥' + res.result.amount);
                        layui.$('input[name=courseId]').val(res.result.id);
                    }
                    else {
                        layui.$('input[name=id]').val(res.result.id);
                        layui.$('input[name=name]').val(res.result.name);
                        layui.$('input[name=coverUrl]').val(res.result.coverUrl);
                        layui.$('input[name=teacherName]').val(res.result.teacherName);
                        layui.$('input[name=status]').val(res.result.status);
                        layui.$('input[name=amount]').val(res.result.amount);
                        layui.$('input[name=h5Url]').val(res.result.h5Url);
                        layui.$('#status').val(res.result.status);
                        layui.$('#desc').val(res.result.desc);
                        layui.$('#type').val(res.result.type);
                        layui.$('#detail').val(res.result.detail);
                        layui.form.render('select');
                        layui.course.initFile();
                        layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.result.coverUrl);
                        layui.course.initTinymce();
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取课程列表
         * */
        query: function () {
            var companyId = layui.common.getUrlParam('companyId') || '';
            if (companyId == '') {
                layui.common.alertAutoClose("企业id有误");
            }
            layui.tableRequest.request('resource', true, 'course-list', '/admin/course/info/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '名称' },
                { field: 'amount', title: '金额' },
                { field: 'teacherName', title: '投资顾问' },
                { field: 'type', title: '类型' },
                {
                    title: '状态', templet: function (e) {
                        return e.status == 1 ? '正常销售' : '暂停销售';
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 200, align: 'left', toolbar: '#course-bar' }
            ], { companyId: companyId });
            //监听表格事件
            layui.course.tableEvent();
        },
        /**
        * 创建课程
        * */
        create: function (data) {
            var companyId = layui.common.getUrlParam('companyId') || '';
            if (companyId == '') {
                layui.common.alertAutoClose("企业id有误");
            }
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/course/info/create',
                data: JSON.stringify({
                    companyId: companyId,
                    name: data.field.name,
                    coverUrl: data.field.coverUrl,
                    type: data.field.type,
                    teacherName: data.field.teacherName,
                    amount: data.field.amount,
                    h5Url: data.field.h5Url,
                    desc: data.field.desc,
                    detail: tinyMCE.activeEditor.getContent()
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("课程创建成功");
                    setTimeout(function () { location.href = 'list.html'; }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑单个课程
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/course/info/update',
                data: JSON.stringify({
                    id: data.field.id,
                    name: data.field.name,
                    coverUrl: data.field.coverUrl,
                    type: data.field.type,
                    teacherName: data.field.teacherName,
                    amount: data.field.amount,
                    status: data.field.status,
                    h5Url: data.field.h5Url,
                    desc: data.field.desc,
                    detail: tinyMCE.activeEditor.getContent()
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("课程编辑成功");
                    setTimeout(function () { location.href = 'list.html'; }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除课程
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/course/info/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("course-list");
                    layui.common.alertAutoClose("课程删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 生成购买链接
         * @param {any} data
         */
        genUrl: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/course/link/create',
                data: JSON.stringify({
                    courseId: data.field.courseId,
                    channelKey: data.field.channelKey,
                    saleUserId: data.field.saleUserId,
                    isShowKf: data.field.isShowKf
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('#lab_url').text(res.result.url);
                    layui.$('#erwm').attr('src', res.result.qrCodeUrl);
                    layui.$('#buyUrl').show();
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    location.href = 'update.html?id=' + data.id;
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该课程吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.course.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'gen') {
                    layui.common.openIframe('生成购买链接', 620, 450, 'gen-url.html?id=' + data.id);
                }
            });
        },
        /**
         * 绑定事件
         * @param {any} opType
         */
        bindEvent: function (opType) {
            if (opType == 'create') {
                layui.form.on('submit(course-submit)', function (data) {
                    layui.course.create(data);
                });
            }
            else if (opType == 'update') {
                layui.form.on('submit(course-submit)', function (data) {
                    layui.course.update(data);
                });
            }
            else if (opType == 'gen') {
                layui.form.on('submit(gen-url-submit)', function (data) {
                    layui.course.genUrl(data);
                    return false; //阻止表单跳转
                });
                layui.form.on('submit(copy-url-submit)', function (data) {
                    var content = layui.$("#lab_url").text();
                    if (content == "") {
                        layui.common.alertAutoClose("请先点击生成按钮生成购买地址");
                        return false;
                    }
                    let copy = (e) => {
                        e.preventDefault()
                        e.clipboardData.setData('text/plain', content)
                        layui.common.alertAutoClose("复制成功");
                        document.removeEventListener('copy', copy)
                    }
                    document.addEventListener('copy', copy)
                    document.execCommand("Copy");
                    return false; //阻止表单跳转
                });
                layui.form.on('select(course-channel)', function (data) {
                    if (data.value == 'offline') {
                        layui.course.initAdminUser();
                        layui.$('#admin-user-item').show();
                    }
                    else {
                        layui.$('#admin-user-item').hide();
                        layui.$('#admin-user-view').val('');
                    }
                });
            }
        }
    }

    exports('course', func);
});