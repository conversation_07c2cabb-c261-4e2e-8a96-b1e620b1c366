﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 120px; }
        .layui-form-item .layui-input-inline { width: 800px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
        .tb { width: 100%; }
        .tb th { padding: 9px 0; text-align: left; }
        .tb td { padding: 5px 20px 5px 0; }
        .tb td a { margin-right: 10px; font-size: 16px; }
        .tb td a i { font-size: 22px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 20px 0;position:relative;">
                <input id="id" name="id" type="hidden" />
                <div class="layui-form-item">
                    <label class="layui-form-label">使用团队</label>
                    <div class="layui-input-inline" id="subsidiary-item">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">产品名称</label>
                    <div class="layui-input-inline">
                        <input type="text" name="name" disabled="disabled" placeholder="请输入产品名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">产品金额</label>
                    <div class="layui-input-inline">
                        <input type="text" name="amount" disabled="disabled" placeholder="请输入产品金额" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">使用场景</label>
                    <div class="layui-input-inline">
                        <select name="scene" id="scene" disabled="disabled">
                            <option value="">请选择使用场景</option>
                            <option value="1">线上</option>
                            <option value="2">线下</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">产品类型</label>
                    <div class="layui-input-inline">
                        <select name="type" id="type" disabled="disabled">
                            <option value="">请选择产品类型</option>
                            <option value="1">证券投资培训课程</option>
                            <option value="2">证券投资顾问产品</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-inline">
                        <select name="status" disabled="disabled" id="status">
                            <option value="">请选择产品状态</option>
                            <option value="1">正常销售</option>
                            <option value="2">下架</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">风险等级</label>
                    <div class="layui-input-inline">
                        <select name="riskLevel" disabled="disabled" id="riskLevel">
                            <option value="">请选择风险等级</option>
                            <option value="1">低风险</option>
                            <option value="2">中低风险</option>
                            <option value="3">中风险</option>
                            <option value="4">中高风险</option>
                            <option value="5">高风险</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">服务周期</label>
                    <div class="layui-input-inline">
                        <div class="layui-input-inline" style="width:48%;margin-right:0;margin-right:2%;">
                            <input type="text" name="duration" disabled="disabled" placeholder="服务周期，例如：10代表10天|星期|月|年" autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-input-inline" style="width: 50%; margin-right: 0;">
                            <select name="unit" disabled="disabled" id="unit">
                                <option value="">请选择服务周期</option>
                                <option value="年">年</option>
                                <option value="月">月</option>
                                <option value="周">周</option>
                                <option value="天">天</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item" style="display:none;">
                    <label class="layui-form-label">投顾功能权限</label>
                    <div class="layui-input-inline" id="dic-list-view">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">投教开课时间</label>
                    <div class="layui-input-inline">
                        <input type="text" id="startTime" disabled="disabled" name="startTime" placeholder="请选择证券投资培训课程开课时间" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">产品封面<br />分享图标</label>
                    <div class="layui-input-inline" style="width:350px;">
                        <input id="hid_coverUrl" name="coverUrl" type="hidden" />
                        <div class="layui-upload-drag" id="uploadImg">
                            <i class="layui-icon"></i>
                            <p>点击上传，或将文件拖拽到此处</p>
                            <div class="layui-hide" id="uploadDemoView">
                                <hr>
                                <img src="" alt="上传成功后渲染" style="max-width: 196px">
                            </div>
                        </div>
                    </div>
                    <div class="layui-input-inline" style="width:350px;">
                        <input id="hid_coverUrl2" name="shareIconUrl" type="hidden" />
                        <div class="layui-upload-drag" id="uploadImg2">
                            <i class="layui-icon"></i>
                            <p>点击上传分享图标或将文件拖拽到此处</p>
                            <div class="layui-hide" id="uploadDemoView2">
                                <hr>
                                <img src="" alt="上传成功后渲染" style="max-width: 196px">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item zqt-item">
                    <label class="layui-form-label">投资顾问</label>
                    <div class="layui-input-inline">
                        <input type="text" name="teacherName" disabled="disabled" placeholder="产品所属投资顾问" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">加粉方案ID</label>
                    <div class="layui-input-inline">
                        <input type="text" name="kfCaseId" disabled="disabled" placeholder="客服加粉方案ID" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">第三方产品ID</label>
                    <div class="layui-input-inline">
                        <input type="text" name="thirdPartyProductId" disabled="disabled" placeholder="请输入绑定的第三方系统产品ID" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item dn-item">
                    <label class="layui-form-label">自定义合同模板ID</label>
                    <div class="layui-input-inline">
                        <input type="text" name="contractTemelateId" disabled="disabled" placeholder="请输入电子签约自定义合同模板ID" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item dn-item">
                    <label class="layui-form-label">第三方支付地址</label>
                    <div class="layui-input-inline">
                        <input type="text" name="payUrl" disabled="disabled" placeholder="请输入第三方支付跳转地址" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item dn-item">
                    <label class="layui-form-label">投资顾问</label>
                    <div class="layui-input-inline">
                        <table class="tb">
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">分享卡片描述</label>
                    <div class="layui-input-inline">
                        <input type="text" name="shareDesc" disabled="disabled" placeholder="请输入分享卡片描述" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">产品描述</label>
                    <div class="layui-input-inline">
                        <textarea type="text" id="desc" disabled="disabled" name="desc" placeholder="请输入产品描述" autocomplete="off" class="layui-textarea"></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">产品详情</label>
                    <div class="layui-input-inline">
                        <textarea type="text" name="detail" disabled="disabled" id="detail" placeholder="请输入产品详情" autocomplete="off" class="layui-textarea"></textarea>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-inline">
                        <input type="radio" name="auditStatus" value="2" title="通过" checked>
                        <input type="radio" name="auditStatus" value="3" title="驳回">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">审核意见</label>
                    <div class="layui-input-inline">
                        <input type="text" name="auditRemark" placeholder="请输入审核意见" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-inline">
                        <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="product-audit-submit" value="确认审核">
                        <input type="button" class="layui-btn layui-btn-primary" onclick="location.href ='list.html'" value="返回上一页">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script id="dic-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <input type="checkbox" name="dic" value="{{item.key}}" lay-skin="primary" title="{{item.value}}" {{# if(item.isChecked){ }} checked{{# }}}>
        {{#  }); }}
    </script>
    <script id="subsidiary-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/tinymce/tinymce.min.js"></script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        var xmSel;
        var investmentAdvisor = [];
        layui.use(['product', 'laydate'], function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'date'
            });
            layui.product.get();
            layui.product.bindEvent('update-status');
        });
    </script>
</body>
</html>