﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        /* .layui-form-item .layui-input-inline { width: 300px; }*/
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">

                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="标题/类型/模块/操作人" />
                        </div>
                    </div>
                    <div class="layui-inline">

                        <label class="layui-form-label">创建日期</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" />
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="business-log-search">
                                <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="business-log-list" lay-filter="list"></table>
                <script type="text/html" id="business-log-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
                </script>
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['rsBusinessLog'], function () {
            layui.rsBusinessLog.query();
            layui.rsBusinessLog.bindEvent();
        });
    </script>
</body>
</html>