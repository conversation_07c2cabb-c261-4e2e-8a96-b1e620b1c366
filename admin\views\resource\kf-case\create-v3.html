﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 550px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
        .xiaoetong { display: none; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">方案名称</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入企微加粉方案名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">所属企微</label>
            <div class="layui-input-inline">
                <select name="workWxAppId" id="workwx-app-view" lay-filter="workwx-app" lay-verify="required">
                    <option value="">请选择所属企微</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">微信客服</label>
            <div class="layui-input-inline">
                <select name="openKfId" id="openkf-id" lay-search>
                    <option value="">请选择微信客服</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">使用场景</label>
            <div class="layui-input-inline">
                <select name="scene" id="scene" lay-filter="scene" lay-verify="required">
                    <option value="">请选择使用场景</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item tencent-ad-account" style="display:none;">
            <label class="layui-form-label">推广账户</label>
            <div class="layui-input-inline">
                <select name="tencentAdAccountId" id="tencentAdAccountId">
                    <option value="">请选择推广账户</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">企微活码</label>
            <div class="layui-input-inline">
                <select name="workWxQrCodeId" id="workWxQrCodeId" lay-verify="required">
                    <option value="">请选择企微活码</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">重复进线周期</label>
            <div class="layui-input-inline">
                <input type="text" name="cycle" lay-verify="required" placeholder="请输入重复进线周期（例:48小时内重复进线则填写48）" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">可添加员工数</label>
            <div class="layui-input-inline">
                <input type="text" name="maxAddService" lay-verify="required" placeholder="请输入可添加员工数量" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">引导文案</label>
            <div class="layui-input-inline">
                <textarea type="text" name="welcomeStartMessage" lay-verify="required" placeholder="请输入引导文案" autocomplete="off" class="layui-textarea"></textarea>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">点击菜单</label>
            <div class="layui-input-inline">
                <input type="text" name="menuNames" lay-verify="required" placeholder="引导语点击菜单，多个|分隔" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">回传配置</label>
            <div class="layui-input-inline">
                <input type="text" name="baiDuToken" placeholder="百度Token/回传配置" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="kf-case-submit" value="确认添加">
            </div>
        </div>
    </div>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="workwx-qrcode-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['kfCase'], function () {
            layui.kfCase.initApp();
            //渲染使用场景
            layui.dic.query('workwx_scene', function (res) {
                var options = '<option value="">请选择使用场景</option>'
                if (res.isSuccess) {
                    for (var i = 0; i < res.result.length; i++) {
                        options += '<option value="' + res.result[i].key + '">' + res.result[i].value + '</option>';
                    }
                }
                layui.$('#scene').html(options);
                layui.form.render('select');
            });

            layui.form.on('select(workwx-app)', function (data) {
                var workWxAppId = data.value;
                layui.kfAccount.getAll(workWxAppId, function (res) {
                    var options = '<option value="">请选择微信客服</option>'
                    if (res.isSuccess) {
                        for (var i = 0; i < res.result.length; i++) {
                            options += '<option value="' + res.result[i].open_kfid + '">' + res.result[i].name + '</option>';
                        }
                    }
                    layui.$('#openkf-id').html(options);
                    layui.form.render('select');
                });
                //加载活码列表
                layui.qrcode.queryByWorkWxAppId(workWxAppId, function (res) {
                    var options = '<option value="">请选择企微活码</option>'
                    if (res.isSuccess) {
                        for (var i = 0; i < res.result.length; i++) {
                            options += '<option value="' + res.result[i].id + '">' + res.result[i].name + '</option>';
                        }
                    }
                    layui.$('#workWxQrCodeId').html(options);
                    layui.form.render('select');
                });
            });
            //使用场景选择腾讯视频号时，加载腾讯推广账户列表
            layui.form.on('select(scene)', function (data) {
                if (data.value == 'tenxun_sph') {
                    layui.$(".tencent-ad-account").show();
                    var workWxAppId = layui.$("#workwx-app-view").val();
                    layui.tencentAccount.getAll(workWxAppId, function (res) {
                        var options = '<option value="">请选择推广账户</option>'
                        if (res.isSuccess) {
                            for (var i = 0; i < res.result.length; i++) {
                                options += '<option value="' + res.result[i].id + '">' + res.result[i].name + '</option>';
                            }
                        }
                        layui.$('#tencentAdAccountId').html(options);
                        layui.form.render('select');
                    })
                }
                else {
                    layui.$(".tencent-ad-account").hide();
                }
            });
            //提交数据
            layui.form.on('submit(kf-case-submit)', function (data) {
                //腾讯视频号必须选择推广账户，否则数据无法回传
                if (data.field.scene == 'tenxun_sph' && data.field.tencentAdAccountId == '') {
                    layui.common.alertAutoClose('请选择腾讯推广账户');
                    return;
                }
                if (data.field.scene != 'tenxun_sph_zrl') {
                    if (data.field.openKfId == '') {
                        layui.common.alertAutoClose('请选择微信客服');
                        return;
                    }
                }
                data.field.linkUrl = layui.setter.kfLinkUrl;
                layui.kfCase.createV3(data);
                return false; //阻止表单跳转
            });
        });
    </script>
</body>
</html>