﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 520px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
        .img-item { display: inline-block; position: relative; margin-right: 5px; }
        .img-item span.remove { position: absolute; cursor: pointer; right: 0; top: 0; display: inline-block; width: 15px; height: 15px; text-align: center; line-height: 15px; background: #000; opacity: 0.5; color: #fff }
        .pay-info { margin-bottom: 10px; margin-bottom: 10px; float: left; }
        .file-list { width: 100%; margin-top: 5px; }
        .file-list li { width: 100%; line-height: 24px; padding: 3px 10px; box-sizing: border-box; float: left; }
        .file-list li:hover { background: #eee; }
        .file-list li a { font-size: 14px; color: #0094ff; display: inline-block; width: 200px; }
        .file-list li i.layui-icon-link { font-size: 14px; }
        .file-list li i.remove { float: right; cursor: pointer; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <input id="id" name="id" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">直播账号</label>
            <div class="layui-input-inline">
                <select name="accountId" id="account-view" lay-verify="required" lay-search>
                    <option value="">请选择直播账号</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">直播主题</label>
            <div class="layui-input-inline">
                <input type="text" name="title" lay-verify="required" placeholder="请输入直播主题" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">直播时间</label>
            <div class="layui-input-inline" style="width:255px">
                <input type="text" name="startTime" id="startTime" lay-verify="required" placeholder="开始时间" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-input-inline" style="width:255px">
                <input type="text" name="endTime" id="endTime" lay-verify="required" placeholder="结束时间" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">附件</label>
            <div class="layui-input-inline">
                <div style="float:left;">
                    <button type="button" class="layui-btn layui-btn-primary" id="uploadAnnex"><i class="layui-icon layui-icon-upload"></i>上传附件</button>
                </div>

                <div style="float: left; width: 400px;">
                    <ul class="file-list">
                    </ul>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">直播地点</label>
            <div class="layui-input-inline">
                <input type="text" name="address" placeholder="请输入直播地点" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">直播内容</label>
            <div class="layui-input-inline">
                <textarea type="text" name="content" id="content" placeholder="请输入直播内容" autocomplete="off" class="layui-textarea" style="height:200px;"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="plan-create-submit" value="确认保存">
            </div>
        </div>
    </div>

    <script id="account-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=2.4"></script>
    <script type="text/javascript">
        layui.use(['form', 'laydate', 'livePlan'], function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            layui.livePlan.get();
            layui.form.on('submit(plan-create-submit)', function (data) {
                data.field.annex = [];
                var annex = layui.$('.file-list li');
                if (annex.length > 0) {
                    for (var i = 0; i < annex.length; i++) {
                        var that = layui.$(annex).eq(i);
                        data.field.annex.push({ name: that.find('a').text(), url: that.find('a').attr('href'), size: that.find('span').data('size') });
                    }
                }
                layui.livePlan.update(data);
                return false; //阻止表单跳转
            });
        });
    </script>
</body>
</html>