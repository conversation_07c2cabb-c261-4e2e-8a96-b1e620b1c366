﻿<!DOCTYPE html>
<html style="background-color: #f0f2f5;">
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card" style="background: #100c2a">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto" style="padding:20px 30px;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="color:#fff">所属月份</label>
                        <div class="layui-input-inline">
                            <input id="date" name="date" type="text" class="layui-input" placeholder="所属月份" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <div id="main" style="width:100%;height:600px;padding:20px;box-sizing:border-box;"></div>
            </div>
        </div>
    </div>

    <div class="con">
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/lib/echarts/echarts.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['laydate', 'productOrder'], function () {
            layui.laydate.render({
                elem: '#date',
                type: 'month',
                format: 'yyyy年MM月',
                value: new Date(),
                btns: ['now', 'confirm'],
                done: function (value, date, endDate) {
                    layui.productOrder.initChat(value);
                }
            });
            layui.productOrder.initChat(layui.$('#date').val());

        });
    </script>
</body>
</html>