﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .link { color: #50b1fb }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">

                    <div class="layui-inline">
                        <label class="layui-form-label">加粉方案</label>
                        <div class="layui-input-inline" style="width:300px;">
                            <select name="kfCaseId" id="kf-case-view" lay-search>
                                <option value="">请选择加粉方案</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">添加时间</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" readonly="readonly" class="layui-input" placeholder="开始时间" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" readonly="readonly" class="layui-input" placeholder="结束时间" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" id="stat-search" lay-submit lay-filter="stat-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="stat-list" lay-filter="list"></table>
            </div>
        </div>
    </div>
    <script id="kf-case-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=2.1"></script>
    <script type="text/javascript">
        layui.use(['form', 'laydate', 'common', 'externalContact'], function () {
            var from = layui.common.getUrlParam('from') || '';
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime',
                value: new Date(new Date(new Date().toLocaleDateString()).getTime()),
                min: from == 'customer' ? '2024-09-29' : '2022-01-01'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime',
                value: new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1),
                min: from == 'customer' ? '2024-09-29' : '2022-01-01'
            });
            var workWxAppId = layui.common.getUrlParam('workWxAppId') || '';
            if (workWxAppId == '') {
                layui.common.alertAutoClose("企微应用id有误");
                return;
            }
            layui.externalContact.getKfCaseList(workWxAppId);
            layui.externalContact.queryStat(workWxAppId);
            //监听查询按钮
            layui.form.on('submit(stat-search)', function (data) {
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("stat-list", {
                    where: field
                });
            });
        })
    </script>
</body>
</html>