﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline {
            width: 200px;
        }
    </style>
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="员工编号/姓名" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">微信公众号</label>
                        <div class="layui-input-inline">
                            <select name="appId" id="account-view">
                                <option value="">请选择微信公众号</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="staff-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-primary staff-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="staff-list" lay-filter="list"></table>
                <script type="text/html" id="staff-bar">
                    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="down">下载二维码</a>
                </script>
            </div>
        </div>
    </div>
    <script id="account-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.appId}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=0823"></script>
    <script type="text/javascript">
        layui.use(['form', 'laydate', 'common', 'staffQrCode'], function () {
            layui.staffQrCode.initAccount();
            layui.staffQrCode.query();
            //监听查询按钮
            layui.form.on('submit(staff-search)', function (data) {
                layui.tableRequest.reload("staff-list", {
                    where: data.field
                });
            });
            //打开添加弹框
            layui.$('.staff-create').click(function () {
                layui.common.openIframe('添加员工', 550, 400, 'create.html');
            });
        })
    </script>
</body>

</html>