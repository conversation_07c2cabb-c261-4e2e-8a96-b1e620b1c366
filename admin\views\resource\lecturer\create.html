﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label {
            width: 120px;
        }

        .layui-form-item .layui-input-inline {
            width: 400px;
        }
    </style>
</head>

<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list"
        style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">讲师类型</label>
            <div class="layui-input-inline">
                <select name="type" id="type" lay-filter="type" lay-verify="required">
                    <option value=""></option>
                    <option value="1">证券投资培训</option>
                    <option value="2">证券投资顾问</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">姓名</label>
            <div class="layui-input-inline">
                <select name="investmentAdvisorId" id="lecturerNameView" lay-filter="lecturerNameView"
                    lay-verify="required">

                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">执业编号</label>
            <div class="layui-input-inline">
                <input type="text" name="certNo" id="certNo" lay-verify="required" disabled autocomplete="off"
                    class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">头衔</label>
            <div class="layui-input-inline">
                <input type="text" name="rank" lay-verify="required" placeholder="请输入头衔" autocomplete="off"
                    class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">描述</label>
            <div class="layui-input-inline">
                <textarea class="layui-textarea" name="desc" placeholder="请输入讲师描述" autocomplete="off"> </textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">背景图1190*510</label>
            <div class="layui-input-inline">
                <button type="button" class="layui-btn" id="upload-bgImg">
                    <i class="layui-icon">&#xe67c;</i>上传图片
                </button>
                <input id="bgImg" name="bgImg" value="" type="hidden" />
                <div class="layui-inline img-info"></div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">透明背景头像230*255</label>
            <div class="layui-input-inline">
                <button type="button" class="layui-btn" id="upload-transparentBgImg">
                    <i class="layui-icon">&#xe67c;</i>上传图片
                </button>
                <input id="transparentBgImg" name="transparentBgImg" value="" type="hidden" />
                <div class="layui-inline transparentBgImg-info"></div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-inline">
                <input type="text" name="sort" lay-verify="required|number" placeholder="请输入排序ID" autocomplete="off"
                    class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">是否可用</label>
            <div class="layui-input-inline">
                <select name="isEnable" lay-filter="isEnable" lay-verify="required">
                    <option value=""></option>
                    <option value="true">可用</option>
                    <option value="false">不可用</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal"
                    lay-filter="lecturer-create-submit" value="确认添加">
            </div>
        </div>
    </div>
    <script id="lecturer-name-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}" certNo="{{item.certNo}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'lecturer', "uploadFile", "jquery"], function () {
            //监听提交事件
            layui.lecturer.initLecturer()
            layui.form.on('submit(lecturer-create-submit)', function (data) {
                layui.lecturer.create(data);
                return false; //阻止表单跳转
            });
            //选择名称更新职业编号
            layui.form.on('select(lecturerNameView)', function (data) {
                let certNo = layui.$(data.elem).find("option:selected").attr("certNo")
                certNo = certNo !== "undefined" ? certNo : ""
                layui.$("#certNo").val(certNo)
            });
            layui.lecturer.uploadImage("upload-bgImg", "#bgImg", '.img-info', {
                auto: false,
                acceptMime: "image/*"
            }, 1190, 510)
            layui.lecturer.uploadImage("upload-transparentBgImg", "#transparentBgImg", '.transparentBgImg-info', {
                auto: false,
                acceptMime: "image/png",
                ext: "png"
            }, 230, 255)
        })
    </script>
</body>

</html>