﻿<!DOCTYPE html>
<html style="background:#fff">
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-fluid { padding: 0 }
        /* .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 300px; }*/
    </style>
</head>
<body>
    <div class="layui-fluid layui-form ">
        <div class="layui-card">
            <div class="layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <label class="layui-form-label">标题</label>
                    <div class="layui-input-inline" style="width:250px">
                        <input type="text" name="title" id="title" lay-verify="required" placeholder="请输入配置标题" autocomplete="off" class="layui-input">
                    </div>
                    <label class="layui-form-label">生效时间</label>
                    <div class="layui-input-inline">
                        <input type="text" name="startTime" id="startTime" lay-verify="required" placeholder="开始时间" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-input-inline">
                        <input type="text" name="endTime" id="endTime" lay-verify="required" placeholder="结束时间" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">绑定域名</label>
                    <div class="layui-input-inline" id="domainList" style="width:760px">
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <div class="layui-form-item">
                    <label class="layui-form-label">配置项</label>
                    <div style="width: 760px; float: left; ">
                        <div class="config-input-list" style="float:left;">
                            <div class="config-input-title" style="margin-bottom:10px;float:left">
                                <input class="configItemId" name="configItemId" value="0" type="hidden" />
                                <div class="layui-input-inline" style="width:210px">
                                    <div class="layui-form-mid layui-word-aux">绑定系统</div>
                                </div>
                                <div class="layui-input-inline" style="width: 540px;margin-right:0 ">
                                    <div class="layui-input-inline" style="width: 150px">
                                        <div class="layui-form-mid layui-word-aux">分配总人数</div>
                                    </div>
                                    <div class="layui-input-inline" style="width:150px">
                                        <div class="layui-form-mid layui-word-aux">单轮分配人数</div>
                                    </div>
                                    <div class="layui-input-inline" style="width:150px">
                                        <div class="layui-form-mid layui-word-aux">排序</div>
                                    </div>
                                    <div class="layui-input-inline" style="width:50px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="layui-btn layui-btn-primary add-config">
                            <i class="layui-icon">&#xe654;</i> 添加配置项
                        </button>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-inline">
                        <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="form-config-create-submit" value="确认添加">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        var xmSel;
        var customerList = [];
        layui.use(['form', 'formConfig'], function () {
            layui.formConfig.bindEvent();
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.formConfig.initDomainList([]);

                layui.customer.getAll(function (res) {
                    customerList = res.result;
                });
            }
            else {
                layui.formConfig.get(id, 'create');
            }
            //监听提交事件
            layui.form.on('submit(form-config-create-submit)', function (data) {
                var domainList = xmSel.getValue('value');
                if (domainList.length == 0) {
                    layui.common.alertAutoClose('请选择域名');
                    return;
                }
                var items = [];
                var configList = layui.$(".config-input-item");
                if (configList.length == 0) {
                    layui.common.alertAutoClose('请添加配置项');
                    return;
                }
                for (var i = 0; i < configList.length; i++) {
                    var that = configList.eq(i);
                    var option = {
                        customerId: that.find('.customerid').val(),
                        totalCount: that.find('.totalCount').val(),
                        singleCount: that.find('.singleCount').val(),
                        sort: that.find('.sort').val()
                    };
                    items.push(option);
                }
                data.field.domainList = domainList;
                data.field.items = items;
                layui.formConfig.create(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>