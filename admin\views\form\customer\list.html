﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list customer-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="customer-list" lay-filter="list"></table>
                <script type="text/html" id="customer-bar">
                    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="url">登录地址</a>
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['common', 'customer'], function () {
            layui.customer.query();

            layui.$('.customer-create').click(function () {
                layui.common.openIframe('创建客户', 500, 400, 'create.html')
            });
        })
    </script>
</body>
</html>