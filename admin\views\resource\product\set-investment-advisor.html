﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 80px; }
        .layui-form-item .layui-input-inline { width: 330px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <input id="id" name="id" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">投资顾问</label>
            <div class="layui-input-inline" id="investment-advisor-item">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="submit" value="确认修改">
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        var xmSel;
        layui.use(['form', 'product'], function () {
            xmSel = layui.xmSelect.render({
                el: '#investment-advisor-item',
                language: 'zn',
                filterable: true,
                tips: '请选择投顾',
                theme: { color: '#0081ff ' },
                data: [],
                toolbar: { show: true },
                autoRow: true,
            });
            layui.product.getForInvestmentAdvisor();
            //监听提交事件
            layui.form.on('submit(submit)', function (data) {
                data.field.items = [];
                var items = xmSel.getValue();
                if (items.length > 0) {
                    for (var i = 0; i < items.length; i++) {
                        data.field.items.push({
                            id: items[i].value,
                            certNo: items[i].certNo,
                            name: items[i].oldName
                        });
                    }
                }
                layui.product.updateInvestmentAdvisor(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>