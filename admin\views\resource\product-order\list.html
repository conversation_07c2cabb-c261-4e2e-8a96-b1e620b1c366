﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 200px; }
        .red { font-size: 18px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <input id="sort" name="sort" type="hidden" value="0" />
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="订单号/产品/手机号/身份证号" />
                        </div>
                    </div>
                    <div class="layui-inline pay-status-item">
                        <label class="layui-form-label">支付状态</label>
                        <div class="layui-input-inline">
                            <select name="payStatus" id="payStatus">
                                <option value="">请选择支付状态</option>
                                <option value="10">待确认</option>
                                <option value="1">待支付</option>
                                <option value="2">支付成功</option>
                                <option value="3">支付失败</option>
                                <option value="11">已驳回</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">退款状态</label>
                        <div class="layui-input-inline">
                            <select name="refundStatus" id="refundStatus">
                                <option value="">请选择退款状态</option>
                                <option value="1">未退款</option>
                                <option value="2">已提交申请</option>
                                <option value="3">退款成功</option>
                                <option value="4">退款失败</option>
                                <option value="5">部分退款成功</option>
                                <option value="6">部分退款失败</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">订单渠道</label>
                        <div class="layui-input-inline">
                            <select name="channelKey" id="product-channel-view">
                                <option value="">请选择订单渠道</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">下单时间</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" autocomplete="off" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" autocomplete="off" />
                        </div>
                    </div>

                    <div class="senior-search" style="display:none;">
                        <div class="layui-inline">
                            <label class="layui-form-label">支付方式</label>
                            <div class="layui-input-inline">
                                <select name="payType" id="payType">
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">签署状态</label>
                            <div class="layui-input-inline">
                                <select name="signFlowStatus" id="signFlowStatus">
                                    <option value="">请选择合同签署状态</option>
                                    <option value="1">未开始</option>
                                    <option value="2">签署中</option>
                                    <option value="3">签署完成</option>
                                    <option value="4">撤销</option>
                                    <option value="5">过期</option>
                                    <option value="6">拒签</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">所属用户</label>
                            <div class="layui-input-inline">
                                <select name="saleUserId" id="admin-user-view" lay-search>
                                    <option value="">请选择订单所属用户</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">合规审核</label>
                            <div class="layui-input-inline">
                                <select name="auditStatus" id="auditStatus">
                                    <option value="">请选择合规审核状态</option>
                                    <option value="1">待审核</option>
                                    <option value="2">通过</option>
                                    <option value="3">不通过</option>
                                    <option value="4">驳回</option>
                                    <option value="5">未接通</option>
                                    <option value="6">无法接通</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">订单质检</label>
                            <div class="layui-input-inline">
                                <select name="fkAuditStatus" id="fkAuditStatus">
                                    <option value="">请选择质检状态</option>
                                    <option value="10">风险可控</option>
                                    <option value="11">有风险</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">产品类型</label>
                            <div class="layui-input-inline">
                                <select name="type" id="type">
                                    <option value="">请选择产品类型</option>
                                    <option value="1">证券投资培训课程</option>
                                    <option value="2">证券投资顾问产品</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">客户类型</label>
                            <div class="layui-input-inline">
                                <select name="buyType" id="buyType">
                                    <option value="">请选择客户类型</option>
                                    <option value="1">新增</option>
                                    <option value="2">续费</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">投资顾问</label>
                            <div class="layui-input-inline">
                                <input id="teacherName" name="teacherName" type="text" class="layui-input" placeholder="请输入投资顾问名称" />
                            </div>
                        </div>
                        <div class="layui-inline" style="display:none">
                            <label class="layui-form-label">客户来源</label>
                            <div class="layui-input-inline">
                                <input id="customerChannel" name="customerChannel" type="text" class="layui-input" placeholder="请输入客户来源" />
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">所属组织</label>
                            <div class="layui-input-inline">
                                <select name="subsidiaryId" id="subsidiary-view" lay-search></select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">所属部门</label>
                            <div class="layui-input-inline">
                                <select name="departmentId" id="department-view" lay-search>
                                    <option value="">请选择所属部门</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">支付时间</label>
                            <div class="layui-input-inline">
                                <input id="payStartTime" name="payStartTime" type="text" class="layui-input" placeholder="开始时间" autocomplete="off" />
                            </div>
                            <div class="layui-input-inline">
                                <input id="payEndTime" name="payEndTime" type="text" class="layui-input" placeholder="结束时间" autocomplete="off" />
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">审核时间</label>
                            <div class="layui-input-inline">
                                <input id="auditStartTime" name="auditStartTime" type="text" class="layui-input" placeholder="开始时间" autocomplete="off" />
                            </div>
                            <div class="layui-input-inline">
                                <input id="auditEndTime" name="auditEndTime" type="text" class="layui-input" placeholder="结束时间" autocomplete="off" />
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">服务期限</label>
                            <div class="layui-input-inline">
                                <input id="serviceStartTime" name="serviceStartTime" type="text" class="layui-input" placeholder="开始时间" autocomplete="off" />
                            </div>
                            <div class="layui-input-inline">
                                <input id="serviceEndTime" name="serviceEndTime" type="text" class="layui-input" placeholder="结束时间" autocomplete="off" />
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list " lay-submit lay-filter="product-order-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                        <a href="javascript:;" style="color: #c2c2c2;margin-left:10px;" class="layui-font-gray" id="toggleBtn">高级筛选 <i class="layui-icon layui-font-12 layui-icon-down"></i></a>
                    </div>
                    <!--<div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list layui-btn-primary" id="product-order-export" lay-submit lay-filter="product-order-export">
                            <i class="layui-icon layui-icon-export layuiadmin-button-btn"></i>导出
                        </button>
                    </div>-->
                    <!--<div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list layui-btn-warm" id="product-order-create" lay-submit lay-filter="product-order-create">
                            <i class="layui-icon layui-icon-edit layuiadmin-button-btn"></i>手工录入
                        </button>
                    </div>-->
                </div>
            </div>

            <div class="layui-card-body">
                <table id="product-order-list" lay-filter="product-order-list"></table>
                <script type="text/html" id="product-order-bar">
                    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="detail">详情</a>
                    <button class="layui-btn layui-btn-xs" id="layuidropdown_{{d.id}}" lay-filter="ft{{d.id}}"
                            lay-dropdown="{align:'center',menus: [ {layIcon: 'layui-icon-edit',txt: '编辑线下订单', event:'update-offline'}, {layIcon: 'layui-icon-delete', txt: '删除', event:'del'}, {layIcon: 'layui-icon-delete',txt: '删除【线下】', event:'del-offline'}, {layIcon: 'layui-icon-username',txt: '分配订单', event:'distribution'}, {layIcon: 'layui-icon-rmb',txt: '申请退款', event:'refund-offline'}]}">
                        <span>更多</span>
                        <i class="layui-icon layui-icon-triangle-d"></i>
                    </button>
                </script>
                <script type="text/html" id="product-order-bar-dn">
                    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="detail">详情</a>
                    <button class="layui-btn layui-btn-xs" id="layuidropdown_{{d.id}}" lay-filter="ft{{d.id}}"
                            lay-dropdown="{align:'center',menus: [  {layIcon: 'layui-icon-delete', txt: '删除', event:'del'}, {layIcon: 'layui-icon-username',txt: '分配订单', event:'distribution'}]}">
                        <span>更多</span>
                        <i class="layui-icon layui-icon-triangle-d"></i>
                    </button>
                </script>
            </div>
        </div>
    </div>
    <div id="preview" class="preview" style="display:none;">
    </div>
    <div class="layui-form" id="distribution-dialog" style="padding: 20px 0 0 30px; width: 450px; display: none;">
        <div class="layui-form-item">
            <input id="ids" name="ids" value="" type="hidden" />
            <div class="distribution-desc" style="margin-bottom:20px;"></div>
            <div class="config-input-list" style="float:left;">
                <div class="config-input-item" style="margin-bottom:10px;float:left">
                    <div class="layui-input-inline" style="width:210px">
                        <select lay-filter="salerUserId" class="salerUserId" lay-verify="required" lay-search>
                            <option value="">请选择分配用户</option>
                        </select>
                    </div>
                    <div class="layui-input-inline" style="margin-right:0 ">
                        <div class="layui-input-inline" style="width: 120px">
                            <input type="text" lay-verify="required|number" placeholder="分配数量数" autocomplete="off" class="layui-input totalCount">
                        </div>
                        <div class="layui-input-inline" style="width:50px;">
                            <button type="button" class="layui-btn layui-btn-primary remove-config">
                                <i class="layui-icon">&#xe640;</i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <button type="button" class="layui-btn layui-btn-primary add-config">
                <i class="layui-icon">&#xe654;</i> 添加分配用户
            </button>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="order-distribution-submit" value="确认分配">
            </div>
        </div>
    </div>
    <script id="subsidiary-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="product-channel-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.key}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="admin-user-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="department-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="paytype-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script type="text/html" id="topToolBar">
        <div class="layui-btn-container layui-input-inline">
            {{# if(layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.offline.create')){}}
            <button class="layui-btn layui-btn-sm layuiadmin-btn-list layui-btn-warm" id="product-order-create" lay-submit lay-filter="product-order-create">
                <i class="layui-icon layui-icon-edit layuiadmin-button-btn"></i>手工录入
            </button>
            {{#}}}
            {{# if(layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.export')){}}
            <button class="layui-btn layui-btn-sm layuiadmin-btn-list layui-btn-primary" id="product-order-export" lay-submit lay-filter="product-order-export">
                <i class="layui-icon layui-icon-export layuiadmin-button-btn"></i>导出订单
            </button>
            {{#}}}
            {{# if(layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.salersuserid.batch')){}}
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="batch-distribution">
                <i class="layui-icon layui-icon-username layuiadmin-button-btn"></i>Excel导入分配
            </button>
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="batch-distribution2">
                <i class="layui-icon layui-icon-username layuiadmin-button-btn"></i>批量随机分配
            </button>
            {{#}}}
            {{# if(layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.salersuserid.bind')){}}
            <button class="layui-btn layui-btn-sm layuiadmin-btn-list layui-btn-primary" id="product-order-bind" lay-submit lay-filter="product-order-bind">
                <i class="layui-icon layui-icon-edit layuiadmin-button-btn"></i>绑定订单
            </button>
            {{#}}}
            {{# if(layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.refund.create')){}}
            <button class="layui-btn layui-btn-sm layuiadmin-btn-list layui-btn-primary" lay-event="product-order-refund">
                <i class="layui-icon layui-icon-edit layuiadmin-button-btn"></i>批量申请退款
            </button>
            {{#}}}
        </div>
        <div class="amount-desc layui-input-inline">
            订单总金额：
            <span class="red totalAmount">0.00</span>，
            支付成功：
            <span class="red payAmount">0.00</span>，
            待确认：
            <span class="red waitConfirmAmount">0.00</span>，
            已退款：
            <span class="red refundAmount">0.00</span>
        </div>
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=3.2"></script>
    <script type="text/javascript">
        var salerUserList = [];
        layui.use(['productOrder'], function () {
            layui.productOrder.initPayTypeForSerch();
            layui.productOrder.initSubsidiary();
            layui.productOrder.initAdminUser('list');
            layui.productOrder.initProductChannel();
            layui.productOrder.initDepartment();
            layui.productOrder.query();
            layui.productOrder.bindEvent();

            //监听提交事件
            layui.form.on('submit(order-distribution-submit)', function (data) {
                var items = [];
                var configList = layui.$(".config-input-item");
                if (configList.length == 0) {
                    layui.common.alertAutoClose('请添加配置项');
                    return;
                }
                for (var i = 0; i < configList.length; i++) {
                    var that = configList.eq(i);
                    var option = {
                        salerUserId: that.find('.salerUserId').val(),
                        quantity: that.find('.totalCount').val()
                    };
                    items.push(option);
                }
                data.field.ids = data.field.ids.split(',');
                data.field.salerUsers = items;
                layui.productOrder.batchUpdateSalerUserId(data);
                return false; //阻止表单跳转
            });
            if (!layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.copy')) {
                layui.$(document).bind("contextmenu", function () { return false; });
                layui.$(document).bind("selectstart", function () { return false; });
            }
            layui.$("#toggleBtn").click(function () {
                if (layui.$('.senior-search').is(':visible')) {
                    layui.$('#toggleBtn').find('i').removeClass('layui-icon-up').addClass('layui-icon-down');
                }
                else {
                    layui.$('#toggleBtn').find('i').removeClass('layui-icon-down').addClass('layui-icon-up');
                }
                layui.$(".senior-search").slideToggle(150);
            });
        });
    </script>
</body>
</html>