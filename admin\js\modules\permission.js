﻿layui.define(['laytpl', 'form', 'request', 'common', 'menu'], function (exports) {
    var func = {
        /**
         * 获取地址栏参数
         * */
        init: function () {
            var id = layui.common.getUrlParam('id') || '';
            if (id == '') {
                layui.common.alert('对象id有误', 0);
                return;
            }
            layui.$('#objId').val(id);
            var type = layui.common.getUrlParam('type');
            if (type == 'user') {
                layui.$('.type-name').text('用户');
            }
            else if (type == 'role') {
                layui.$('.type-name').text('角色');
            }
            else if (type == 'dep') {
                layui.$('.type-name').text('部门');
            }
            else {
                layui.common.alert('权限类型有误', 0);
                return;
            }
            layui.$('#type').val(type);
            var objName = layui.common.getUrlParam('name') || '-';
            layui.$('.obj-name').text(objName);
            layui.permission.bindEvent();
            layui.permission.queryMenuList();
        },
        /**
         * 获取已拥有的权限列表
         * */
        queryPermission: function (callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/sso/user/permission/by-type/query',
                data: JSON.stringify({
                    objId: layui.$('#objId').val(),
                    type: layui.$('#type').val()
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取系统菜单列表
         * @param {any} appId
         */
        queryMenuList: function () {
            layui.request({
                requestBase: 'sso',
                method: 'post',
                url: '/admin/sso/menu/permission/query',
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    if (res.result.length > 0) {
                        layui.permission.queryPermission(function (queryPermissionRes) {
                            for (var a = 0; a < res.result.length; a++) {
                                var rowspan = 0;
                                for (var i = 0; i < res.result[a].menus.length; i++) {
                                    res.result[a].menus[i].isChecked = queryPermissionRes.result.find((ev) => {
                                        return ev.menuId === res.result[a].menus[i].id;
                                    });
                                    rowspan++;
                                    for (var iper = 0; iper < res.result[a].menus[i].permissions.length; iper++) {
                                        res.result[a].menus[i].permissions[iper].isChecked = queryPermissionRes.result.find((ev) => {
                                            return ev.permissions.indexOf(res.result[a].menus[i].permissions[iper].key) > -1;
                                        });
                                    }
                                    if (res.result[a].menus[i].childs != null && res.result[a].menus[i].childs.length > 0) {
                                        rowspan += res.result[a].menus[i].childs.length;
                                        for (var j = 0; j < res.result[a].menus[i].childs.length; j++) {
                                            res.result[a].menus[i].childs[j].isChecked = queryPermissionRes.result.find((ev) => {
                                                return ev.menuId === res.result[a].menus[i].childs[j].id;
                                            });
                                            for (var iperChild = 0; iperChild < res.result[a].menus[i].childs[j].permissions.length; iperChild++) {
                                                res.result[a].menus[i].childs[j].permissions[iperChild].isChecked = queryPermissionRes.result.find((ev) => {
                                                    return ev.permissions.indexOf(res.result[a].menus[i].childs[j].permissions[iperChild].key) > -1;
                                                });
                                            }
                                        }
                                    }
                                }
                                res.result[a].rowspan = rowspan;
                            }
                            console.log(res)
                            var getTpl = document.getElementById("menu-tpl").innerHTML
                                , view = document.getElementById('menu-view');
                            layui.laytpl(getTpl).render(res.result, function (html) {
                                view.innerHTML = html;
                            });
                            layui.form.render('checkbox');
                        });
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 保存权限
         * @param {any} data
         */
        save: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                requestBase: 'sso',
                method: 'post',
                url: '/admin/sso/user/permission/' + data.type + '/save',
                data: JSON.stringify(data),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("权限编辑成功");
                    setTimeout(function () {
                        parent.historyBack();
                        //var returnUrl = layui.permission.getLocationUrl(data.type);
                        //if (returnUrl != '') {
                        //    location.href = returnUrl;
                        //}
                    }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 绑定事件
         * @param {any} opType
         */
        bindEvent: function (opType) {
            //应用选择后，加载系统菜单列表
            layui.form.on('select(menu)', function (data) {
                layui.permission.queryMenuList(data.value);
            });
            //全选
            layui.form.on('checkbox(allChoose)', function (data) {
                layui.$(".cbk").each(function () {
                    this.checked = data.elem.checked;
                });
                layui.form.render('checkbox');
            });
            //子集选择时，默认选中父级
            layui.form.on('checkbox(child-menu)', function (data) {
                var parentId = layui.$(data.elem).data('parentid');
                if (data.elem.checked) {
                    layui.$('.menu_' + parentId).prop('checked', true);
                }
                else {
                    var isExistChecked = layui.$('.menu_child_' + parentId + ':checked').length > 0;
                    if (!isExistChecked) {
                        layui.$('.menu_' + parentId).prop('checked', false);
                    }
                }
                layui.form.render('checkbox');
            });
            //子集功能选择时，默认选中父级
            layui.form.on('checkbox(child-menu-permission)', function (data) {
                var parentId = layui.$(data.elem).data('parentid');
                var levelId = layui.$('.menu_child_2_' + parentId).data('parentid');
                if (data.elem.checked) {
                    layui.$('.menu_child_2_' + parentId).prop('checked', true);
                    layui.$('.menu_' + levelId).prop('checked', true);
                }
                else {
                    var isExistChecked = layui.$('.permission_' + parentId + ':checked').length > 0;
                    if (!isExistChecked) {
                        layui.$('.menu_child_2_' + parentId).prop('checked', false);
                        //一级菜单
                        var isExistCheckedLevel1 = layui.$('.menu_child_' + levelId + ':checked').length > 0;
                        if (!isExistCheckedLevel1) {
                            layui.$('.menu_' + levelId).prop('checked', false);
                        }
                    }
                }
                layui.form.render('checkbox');
            });
            //提交保存
            layui.form.on('submit(permission-save-submit)', function (data) {
                var menus = [];
                var menuList = layui.$('.menu_cbk:checked');
                for (var i = 0; i < menuList.length; i++) {
                    var menuId = menuList.eq(i).val();
                    var permissionList = layui.$('.permission_' + menuId + ':checked');
                    var permissions = [];
                    if (permissionList.length > 0) {
                        for (var j = 0; j < permissionList.length; j++) {
                            permissions.push(permissionList.eq(j).val());
                        }
                    }
                    menus.push({ menuId: menuId, appId: menuList.eq(i).data('appid'), permissions: permissions });
                }
                data.field.menus = menus;
                data.field.objName = layui.$('.obj-name').text();
                layui.permission.save(data.field);
            });
            //返回上一页
            layui.form.on('submit(cancel)', function (data) {
                //var type = layui.$('#type').val();
                //var returnUrl = layui.permission.getLocationUrl(type);
                //if (returnUrl != '') {
                //    location.href = returnUrl;
                //}
                parent.historyBack();
            });
        },
        /**
         * 获取跳转地址
         * @param {any} type
         */
        getLocationUrl: function (type) {
            var companyId = layui.common.getUrlParam('companyId') || '';
            var from = layui.common.getUrlParam('from') || '';
            switch (type) {
                case 'user':
                    return '../admin-user/list' + (from == 'zqt' ? '-zqt' : '') + '.html' + (companyId != '' ? '?companyId=' + companyId : '');
                case 'role':
                    return '../roles/list.html' + (companyId != '' ? '?companyId=' + companyId : '');
                case 'dep':
                    return '../department/list.html' + (companyId != '' ? '?companyId=' + companyId : '');
                default:
            }
        }
    }

    exports('permission', func);
});