﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 120px; }
        .layui-form-item .layui-input-inline { width: 400px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">登录账号</label>
            <div class="layui-input-inline">
                <input type="text" name="account" lay-verify="required" placeholder="请输入登录账号（手机号）" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">姓名</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入用户姓名" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">登录密码</label>
            <div class="layui-input-inline">
                <input type="password" name="password" lay-verify="required" placeholder="请输入登录密码" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">所属部门</label>
            <div class="layui-input-inline">
                <select name="departmentId" id="department-view" lay-verify="required" lay-search>
                    <option value="">请选择所属部门</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">角色</label>
            <div class="layui-input-inline">
                <select name="roleId" id="role-view" lay-search>
                    <option value="">请选择用户角色</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">绑定客户</label>
            <div class="layui-input-inline" id="customer-list-view">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="admin-user-create-submit" value="确认添加">
            </div>
        </div>
    </div>
    <script id="role-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="customer-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <input type="checkbox" name="customer" value="{{item.id}}" lay-skin="primary" title="{{item.alias}}">
        {{#  }); }}
    </script>
    <script id="department-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'setter', 'formAdminUser'], function () {
            layui.formAdminUser.initDepartment(layui.setter.companyId);
            layui.formAdminUser.initRole(layui.setter.companyId, []);
            layui.formAdminUser.initCustomer([]);

            //监听提交事件
            layui.form.on('submit(admin-user-create-submit)', function (data) {
                var customerIds = [];
                layui.$(":checkbox[name=customer]:checked").each(function () {
                    customerIds.push(layui.$(this).val());
                });
                data.field.customerIds = customerIds;
                layui.formAdminUser.create(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>