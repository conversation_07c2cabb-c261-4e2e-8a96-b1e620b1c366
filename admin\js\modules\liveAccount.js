﻿layui.define(['laytpl', 'request', 'tableRequest', 'common', 'adminUser', 'subsidiary'], function (exports) {
    var func = {
        /**
        * 渲染组织下拉选框
        * */
        initSubsidiary: function (id) {
            layui.subsidiary.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择所属组织' });
                var getTpl = document.getElementById("subsidiary-tpl").innerHTML
                    , view = document.getElementById('subsidiary-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('subsidiary-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
        * 渲染用户列表
        * */
        initUser: function (id) {
            layui.adminUser.queryByCompanyId(layui.setter.productCompanyId, function (res) {
                res.result.unshift({ id: '', name: '请选择绑定账号' });
                var getTpl = document.getElementById("user-tpl").innerHTML
                    , view = document.getElementById('user-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    view.value = id;
                }
                layui.form.render('select');
            });
        },
        /**
        * 获取所有账号列表
        * */
        getAll: function (callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/live/account/get-all',
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 批量修改账号状态
         */
        batchUpdate: function (data) {
            layui.request({
                method: 'post',
                url: '/admin/live/account/status/batch-update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("account-list");
                    parent.layui.common.alertAutoClose("批量修改成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 导出直播账号
         * */
        export: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/live/account/export',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                if (res.isSuccess) {
                    location.href = res.result;
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过id获取单个直播账号
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/live/account/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=account]').val(res.result.account);
                    layui.$('#bigType').val(res.result.bigType);
                    layui.$('#smallType').val(res.result.smallType);
                    layui.$('#status').val(res.result.status);
                    layui.$('#jobType').val(res.result.jobType);
                    if (res.result.authTime != null) {
                        layui.$('#authTime').val(layui.common.timeFormat(res.result.authTime).split(' ')[0]);
                    }
                    if (res.result.expirationTime != null) {
                        layui.$('#expirationTime').val(layui.common.timeFormat(res.result.expirationTime).split(' ')[0]);
                    }
                    layui.$('input[name=teacherName]').val(res.result.teacherName);
                    //layui.$('input[name=jobType]').val(res.result.jobType);
                    layui.$('input[name=jobNumber]').val(res.result.jobNumber);
                    layui.$('#remark').val(res.result.remark);
                    layui.liveAccount.initUser(res.result.userId);
                    layui.liveAccount.initSubsidiary(res.result.subsidiaryId);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取直播账号列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'account-list', '/admin/live/account/query', 'application/json', [
                { checkbox: true, fixed: true },
                { field: 'subsidiaryName', title: '所属组织' },
                { field: 'account', title: '账号' },
                { field: 'name', title: '名称' },
                {
                    field: 'bigType', title: '平台', width: 80, templet: function (e) {
                        if (e.bigType == 1) {
                            return '抖音';
                        }
                        else if (e.bigType == 2) {
                            return '视频号';
                        }
                        else if (e.bigType == 3) {
                            return '快手号';
                        }
                        else if (e.bigType == 4) {
                            return '小鹅通';
                        }
                        else if (e.bigType == 5) {
                            return '小红书';
                        }
                        else {
                            return '-';
                        }
                    }
                },
                {
                    field: 'smallType', title: '账号类别', width: 130, templet: function (e) {
                        if (e.smallType == 1) {
                            return '企业员工号';
                        }
                        else if (e.smallType == 2) {
                            return '员工个人号授权';
                        }
                        else {
                            return '-';
                        }
                    }
                },
                {
                    field: 'status', title: '状态', width: 80, templet: function (e) {
                        if (e.status == 1) {
                            return '使用';
                        }
                        else if (e.status == 2) {
                            return '撤销';
                        }
                        else if (e.status == 3) {
                            return '停用';
                        }
                        else {
                            return '-';
                        }
                    }
                },
                { field: 'teacherName', title: '姓名', width: 100 },
                { field: 'jobNumber', title: '执业编号' },
                {
                    field: 'authTime', title: '授权时间', templet: function (e) {
                        if (e.authTime != null) {
                            return layui.common.timeFormat(e.authTime).split(' ')[0];
                        }
                        else {
                            return '-';
                        }
                    }
                },
                {
                    field: 'expirationTime', title: '过期时间', templet: function (e) {
                        if (e.expirationTime != null) {
                            return layui.common.timeFormat(e.expirationTime).split(' ')[0];
                        }
                        else {
                            return '-';
                        }
                    }
                },
                { field: 'remark', title: '备注' },
                {
                    fixed: 'right', field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 120, align: 'left', toolbar: '#account-bar' }
            ], {}, '#topToolBar');
            //监听表格事件
            layui.liveAccount.tableEvent();
        },
        /**
        * 创建单个直播账号
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/live/account/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("account-list");
                    parent.layui.common.alertAutoClose("账号创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑单个直播账号
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/live/account/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("account-list");
                    parent.layui.common.alertAutoClose("账号编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除单个直播账号
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/live/account/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("account-list");
                    layui.common.alertAutoClose("账号删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 导入直播账号
        * */
        import: function (data) {
            if (data.field.file == '') {
                layui.common.alertAutoClose("请选择文件");
                return;
            }
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            var formData = new FormData(layui.$('#uploadForm')[0]);
            layui.request({
                method: 'post',
                url: '/admin/live/account/import',
                data: formData
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose('导入完成');
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑直播账号', 720, 620, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该直播账号吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.liveAccount.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
            layui.table.on('toolbar(list)', function (obj) {
                if (obj.event == 'import') {
                    layui.common.openIframe('导入直播账号', 600, 350, 'import.html');
                }
                else if (obj.event == 'create') {
                    layui.common.openIframe('创建直播账号', 720, 620, 'create.html');
                }
                else if (obj.event == 'account-update') {
                    var checkedList = layui.table.checkStatus(obj.config.id);
                    if (checkedList.data.length == 0) {
                        layui.common.alertAutoClose("请选择需要操作的记录");
                        return;
                    }
                    var ids = [];
                    for (var i = 0; i < checkedList.data.length; i++) {
                        ids.push(checkedList.data[i].id);
                    }
                    layui.common.openIframe('批量更改账号状态', 400, 200, 'updatebatch.html?id=' + ids.toString());
                }
            });
        }
    }

    exports('liveAccount', func);
});