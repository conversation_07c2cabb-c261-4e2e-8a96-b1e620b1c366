<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        html,
        body,
        .layui-fluid {
            height: 100%;
            box-sizing: border-box;
        }

        .preItemActive {
            display: none;
        }

        .layui-form-item .layui-input-inline {
            width: 300px;
        }

        .layui-card {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .layui-card-body {
            flex: 1;
            overflow: hidden;
        }

        .emty {
            height: 300px;
            text-align: center;
            line-height: 300px;
            font-size: 18px;
            display: none;
        }

        .table {
            display: flex;
            flex-wrap: wrap;
            /* justify-content: space-between; */
        }

        .icon img,
        .hoveIcon img {
            width: 16px;
            height: 16px;
        }

        .hoveIcon {
            display: none;
        }

        .menu_item_hover .hoveIcon {
            display: block;
        }

        .menu_item_hover .icon {
            display: none;
        }

        .layui-form-select dl dd.layui-this {
            background-color: #3193FF;
        }

        .item {
            width: 213px;
            margin-bottom: 15px;
            margin-right: 21px;
            width: 213px;
            height: 323px;
            background: #FFFFFF;
            box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }

        .tabAdd {
            width: 213px;
            height: 323px;
            background: #FFFFFF;
            box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.15);
            border-radius: 5px;
            margin-bottom: 15px;
            margin-right: 30px;
            margin-left: 3px;
            margin-top: 3px;
            padding: 14px;
            box-sizing: border-box;
        }

        .addWarp {
            background: #F7F8F9;
            padding: 16px 16px;
            position: relative;
        }

        .addWarp .addBtn {

            width: 33px;
            height: 33px;
            background: #3193FF;
            border-radius: 50%;
            font-size: 26px;
            color: #FFFFFF;
            text-align: center;
            line-height: 33px;
            margin: 0 auto;
        }

        .add {
            position: absolute;
            top: 45%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .addText {
            margin-top: 14px;
            font-size: 14px;
            color: #3193FF;
            font-weight: 600;
            display: block;
            text-align: center;
        }

        .addWarp .addBg {
            height: 15px;
            background: #F1F3F5;
            border-radius: 2px;
            margin-bottom: 10px;
        }

        .status {
            position: absolute;
            top: -1px;
            left: -1px;
            color: #FFFFFF;
            font-size: 14px;
            font-weight: 600;
            padding: 8px 17px;
            box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.3);
            border-radius: 5px 0px 5px 0px;
        }

        .item:hover {
            position: relative;
        }

        .item_img {
            height: 213px;
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }

        .item_hover::before {
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            content: "";
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 5px 5px 0px 0px;
        }

        .info {
            background: #FFFFFF;
            height: 110px;
            font-size: 12px;
            padding: 12px;
            box-sizing: border-box;
        }

        .title {
            /* text-align: center; */
            font-weight: 700;
            font-size: 14px;
            color: #303133;

            line-height: 16px;
            margin-bottom: 4px;
        }

        #pagetotal {
            position: absolute;
            bottom: 7px;
            left: 50%;
            transform: translate(-50%);
        }

        .success .status {
            background: linear-gradient(180deg, rgba(97, 196, 253, 0.95) 0%, rgba(64, 158, 255, 0.95) 100%);
        }

        .default .status {
            background: linear-gradient(180deg, rgba(191, 191, 191, 0.95) 0%, rgba(149, 149, 149, 0.95) 100%);
        }

        .default .error {
            background: linear-gradient(180deg, rgba(252, 89, 89, 0.95) 0%, rgba(241, 59, 59, 0.95) 100%);
        }

        .tips {
            display: flex;
            justify-content: space-between;
            margin-top: 8px;
            font-size: 11px;
            line-height: 12px;
        }

        .layuiadmin-btn-list {
            background-color: #3193FF;
        }

        .layui-btn {
            border-radius: 5px;
        }

        .tips span {
            display: inline-block;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            margin-left: 7px;
            flex: 1;
            text-align: right;
        }

        label {
            color: #AAAAAA;
        }

        .menu_btn {
            position: absolute;
            left: 0;
            right: 0;
            height: 110px;
            background: #FFFFFF;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: space-around;
            display: none;
            /* display: flex; */
            font-size: 10px;
            line-height: 14px;
        }

        .item_hover .menu_btn {
            display: flex;
        }

        .previewMenu {
            position: absolute;
            left: 50%;
            top: 30%;
            transform: translate(-50%, -50%);
            display: none;
        }

        .preItemIconActive {
            display: none;
        }

        .preItemIcon {
            display: block;
        }

        .preItem.active {
            color: #3193FF;
        }

        .preItem.active .preItemIconActive {
            display: block;
        }

        .preItem.active .preItemIcon {
            display: none;
        }

        .item_hover .previewMenu {
            display: block;
            cursor: pointer;
        }

        .menu_item>div {
            text-align: center;
            width: 34px;
            height: 34px;
            line-height: 34px;
            background: #F1F3F5;
            border-radius: 3px;
            margin-bottom: 8px;
        }

        .menu_item,
        .preItem {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
        }

        .preItem {
            width: 85px;
            height: 35px;
            border-radius: 20px;
            border: 1px solid #FFFFFF;
            line-height: 35px;
            font-size: 14px;
            font-weight: 600;
            color: #FFFFFF;
            margin-top: 17px;
            display: flex;
            flex-direction: row;
            justify-content: center;
        }

        .preItem img {
            width: 16px;
            height: 16px;
            margin-right: 3px;
        }

        .layui-form-label {
            color: #303133;
            width: 120px;
            text-align: right;
        }

        .menu_item:hover {
            color: #3193FF;
        }

        #sort-table-view {
            height: calc(100% - 55px);
            overflow: auto;
        }

        .layui-layer-content {

            overflow: visible !important;
        }
    </style>
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">落地页名称：</label>
                        <div class="layui-input-inline">
                            <input id="txb_kwd" name="keywords" type="text" class="layui-input" placeholder="请输入" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">合规审核状态：</label>
                        <div class="layui-input-inline">
                            <select name="auditStatus" id="auditStatus">
                                <option value=""></option>
                                <option value="1">未提审</option>
                                <option value="2">审核中</option>
                                <option value="3">审核通过</option>
                                <option value="4">审核拒绝</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="sortEdit-search">查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button type="reset" class="layui-btn layui-btn-primary" onclick="reset()">重置</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <div id="sort-table-view">

                </div>
                <script id="sort-table-tpl" type="text/html">
                    <div class="table">
                        <div class="tabAdd"  onclick="goEdit()">
                            <div class="addWarp">
                                <div class="addBg"></div>
                                <div class="addBg"></div>
                                <div class="addBg"></div>
                                <div class="addBg"></div>
                                <div class="addBg"></div>
                                <div class="addBg"></div>
                                <div class="addBg"></div>
                                <div class="addBg"></div>
                                <div class="addBg"></div>
                                <div class="addBg"></div>
                                <div class="addBg"></div>
                                <div class="add">
                                    <div class="addBtn">+</div>
                                    <span class="addText">新增落地页</span>
                                </div>
                            </div>
                        </div>
                        {{#  layui.each(d, function(index, item){ }}
                        <div class="item">
                            <div class="item_img" style="background-image: url('{{getItemUrl(item.content)}}')"></div>
                            <div class="info">
                                <div class="title">{{item.title}}</div>
                                <div class="tips {{item.auditStatus==3?'success':''}} {{item.auditStatus<=2?'default':''}} {{item.auditStatus==4?'error':''}}">
                                    <div class="status">{{['','未提审','审核中','审核通过','审核拒绝'][item.auditStatus]}}</div>
                                    <label> 落地页ID</label>
                                    <span>{{item.id}}</span>
                                </div>
                                <div class="tips">
                                    <label> 创建日期</label>
                                    <span>{{formattedDate(item.createdAt)}}</span>
                                </div>
                                <div class="tips">
                                    <label> 修改日期</label>
                                    <span>{{formattedDate(item.updatedAt)}}</span>
                                </div>
                            </div>
                            <div class="previewMenu">
                                <div class="preItem" style="margin-right: 10px;" onclick="preview('{{item.id}}')">
                                    <div class="preItemIcon"> 
                                        <img src="../../../images/preview.png" alt="">
                                    </div>
                                    <div class="preItemIconActive">
                                        <img src="../../../images/previewActive.png" alt="">
                                    </div>
                                    预览
                                </div>
                                <div class="preItem" onclick="examine('{{item.id}}')">
                                    <div class="preItemIcon"> 
                                        <img src="../../../images/examine.png" alt="">
                                    </div>
                                    <div class="preItemIconActive">
                                        <img src="../../../images/examineActive.png" alt="">
                                    </div>
                                    审核
                                </div>
                            </div>
                            <div class="menu_btn">
                                <div class="menu_item menu_item_hover" onclick="goCase('{{item.id}}','{{item.title}}')">
                                    <div class="icon"> 
                                        <img src="../../../images/friends.png" alt="" >
                                    </div>
                                    <div class="hoveIcon">
                                        <img src="../../../images/friendsActive.png" alt="">
                                    </div>
                                    加粉方案
                                </div>
                                <div class="menu_item" onclick="copy('{{item.id}}')">
                                    <div class="icon"> 
                                        <img src="../../../images/link.png" alt="" >
                                    </div>
                                    <div class="hoveIcon">
                                        <img src="../../../images/linkActive.png" alt="">
                                    </div>
                                    复制链接
                                </div>
                                <div class="menu_item" onclick="goEdit('{{item.id}}')">
                                    <div class="icon"> 
                                        <img src="../../../images/edit.png" alt="" >
                                    </div>
                                    <div class="hoveIcon">
                                        <img src="../../../images/editActive.png" alt="">
                                    </div>
                                    编辑
                                </div>
                                <div class="menu_item" onclick="goEdit('{{item.id}}',true)">
                                    <div class="icon"> 
                                        <img src="../../../images/copy.png" alt="" >
                                    </div>
                                    <div class="hoveIcon">
                                        <img src="../../../images/copyActive.png" alt="">
                                    </div>
                                    复制页面
                                </div>
                            </div>
                        </div>
                        {{#  }); }}
                    </div>
                </script>
                <div id="pagetotal"></div>
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        var copy, reset;
        function formattedDate(timestamp) {
            const date = new Date(timestamp);
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            const hour = date.getHours().toString().padStart(2, '0');
            const minute = date.getMinutes().toString().padStart(2, '0');
            const second = date.getSeconds().toString().padStart(2, '0');
            const formattedDate = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
            return formattedDate;
        }
        function goEdit(id, isCopy) {
            layui.common.openIframe(id && !isCopy ? '编辑落地页' : '创建落地页', '100%', '100%', `edit.html?id=${id}&isCopy=${isCopy}`, true);
        }
        function goCase(id, name) {
            console.log(id, name)
            layui.common.openIframe('关联加粉方案', 440, 330, `case.html?id=${id}&name=${name}`, false);
        }
        function preview(id) {
            layui.common.openIframe('预览', 400, 800, `preview.html?id=${id}`, false);
        }
        function examine(id) {
            layui.common.openIframe('审核', 520, 300, `examine.html?id=${id}`, false);
        }
        function getItemUrl(item) {
            let tabItem = JSON.parse(item)
            let list = tabItem.vdrMap.filter(item => item.type == 'img')
            if (list.length == 0) {
                list.push({
                    value: tabItem.bgImg
                })
            }
            return list[0]?.value
        }
        layui.use(['form', 'common', 'sortEdit', 'jquery'], function () {
            layui.sortEdit.query();
            //监听查询按钮
            layui.form.on('submit(sortEdit-search)', function (data) {
                var field = data.field;
                if (field.auditStatus == '') {
                    delete field.auditStatus
                }
                //执行重载
                layui.sortEdit.query(field);
                return false
            });
            reset = function () {
                layui.$('input[name=keywords]').val('');
                console.log(layui.$('#auditStatus').val())
                layui.$('#auditStatus').val("");
                layui.form.render('select');
                layui.sortEdit.query();
            }
            copy = function (item) {
                let textarea = document.createElement('textarea');
                textarea.style.position = 'fixed';
                textarea.style.opacity = 0;
                textarea.value = `https://test.dnyx.cn/admin/pages/index.html?id=${item}`;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                parent.layui.common.alertAutoClose("复制成功");
            }
        });
    </script>
</body>

</html>