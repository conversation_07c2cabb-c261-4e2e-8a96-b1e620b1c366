﻿layui.define(['laytpl', 'request', "jquery", 'tableRequest', 'common'], function (exports) {
    var func = {
        /**
        * 渲染账号列表
        * */
        initAccount: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/wechat/account/get',
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    res.result.unshift({ appId: '', name: '请选择微信公众号' });
                    var getTpl = document.getElementById("account-tpl").innerHTML
                        , view = document.getElementById('account-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    if (id != undefined) {
                        view.value = id;
                    }
                    layui.form.render('select');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
       
        /**
         * 获取员工列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'staff-list', '/admin/wechat/staff/query', 'application/json', [
                { field: 'mpName', title: '微信公众号' },
                { field: 'staffId', title: '员工编号' },
                { field: 'staffName', title: '员工姓名' },
                { field: 'addCount', title: '添加数' },
                {
                    title: '创建时间', width: 310, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 140, align: 'left', toolbar: '#staff-bar' }
            ], {}, '');
            //监听表格事件
            layui.staffQrCode.tableEvent();
        },

        /**
        * 创建单个员工
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/wechat/staff/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("staff-list");
                    parent.layui.common.alertAutoClose("员工创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
     
        /**
         * 下载员工专属二维码
         * @param {any} data
         */
        download: function (id,staffName) {
            var xhr = new XMLHttpRequest();
            xhr.open("GET", layui.setter.request.resourceBaseUrl+ '/admin/wechat/staff/qrcode/generate?id=' + id, true); // 请求后端接口
            xhr.responseType = "blob"; // 设置响应类型为 Blob (文件流)
            xhr.setRequestHeader(layui.setter.request.tokenName, layui.common.getCookie(layui.setter.request.tokenName));
                xhr.onload = function () {
                    if (xhr.status === 200) {
                        var blob = xhr.response;  // 获取返回的文件流
                        // 创建一个下载链接
                        var link = document.createElement('a');
                        var url = window.URL.createObjectURL(blob); // 创建 Blob 对象 URL
                        link.href = url;
                        link.download = staffName+".png"
                        link.click();

                        // 释放 Blob URL 对象
                        window.URL.revokeObjectURL(url);
                    } else {
                        console.error("文件下载失败");
                    }
                };

                xhr.send();
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'down') {
                    if (data.downloadUrl) {
                        location.href = data.downloadUrl;
                    }
                    else {
                        layui.staffQrCode.download(data.id, data.staffName);
                    }
                }
            });
        }
    }
    exports('staffQrCode', func);
});