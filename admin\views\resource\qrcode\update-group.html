﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 80px; }
        .layui-form-item .layui-input-inline { width: 250px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <input id="id" name="id" type="hidden" />
        <input id="sort" name="sort" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">分组名称</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入分组名称" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="group-update-submit" value="确认编辑">
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'qrcode'], function () {
            layui.qrcode.getGroup();
            layui.form.on('submit(group-update-submit)', function (data) {
                layui.qrcode.updateGroup(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>