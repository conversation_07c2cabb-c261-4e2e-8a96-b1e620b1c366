﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 80px; }
        .layui-form-item .layui-input-inline { width: 250px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list">
        <input id="courseId" name="courseId" type="hidden" />
        <div style="float:left;">
            <div class="layui-form-item" style="margin-top: 25px;">
                <div class="layui-inline">
                    <label class="layui-form-label">课程：</label>
                    <div class="layui-input-inline">
                        <label class="layui-form-mid layui-word-aux" id="lab_course"></label>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">渠道：</label>
                    <div class="layui-input-inline">
                        <select name="channelKey" id="course-channel-view" lay-verify="required" lay-filter="course-channel" lay-search>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item" id="admin-user-item" style="display:none;">
                <div class="layui-inline">
                    <label class="layui-form-label">所属用户：</label>
                    <div class="layui-input-inline">
                        <select name="saleUserId" id="admin-user-view" lay-search>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">客服：</label>
                    <div class="layui-input-inline">
                        <select name="isShowKf" lay-verify="required">
                            <option value="true">支付完成添加客服助理</option>
                            <option value="false">支付完成不添加客服助理</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item" id="buyUrl" style="display:none">
                <div class="layui-inline">
                    <label class="layui-form-label">地址：</label>
                    <div class="layui-input-inline">
                        <label class="layui-form-mid layui-word-aux" id="lab_url" style="width: 250px; word-break: break-all"></label>
                    </div>
                </div>
            </div>
        </div>
        <div style="float: left; margin-top: 25px">
            <img style="width: 200px;" id="erwm" />
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="gen-url-submit" value="生成链接">
                <input type="button" lay-submit="" class="layui-btn layui-btn-primary" lay-filter="copy-url-submit" value="复制链接">
            </div>
        </div>
    </div>
    <script id="course-channel-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.key}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="admin-user-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">
            {{item.name}}
            {{#if(item.account!=''){}}
            （{{item.account}}）
            {{# }else{ }}
            {{item.account}}
            {{# } }}
        </option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['course'], function () {
            layui.course.initCourseChannel();
            layui.course.get();
            layui.course.bindEvent('gen');
        })
    </script>
</body>
</html>