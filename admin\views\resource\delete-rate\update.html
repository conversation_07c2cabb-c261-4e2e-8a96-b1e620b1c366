﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <input id="id" name="id" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">所属日期</label>
            <div class="layui-input-inline">
                <input type="text" id="date" name="date" lay-verify="required" placeholder="请选择所属日期" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">删除率</label>
            <div class="layui-input-inline">
                <input type="text" name="rate" lay-verify="required" placeholder="例：20% 则填写20" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="delete-rate-update-submit" value="确认保存">
            </div>
        </div>
    </div>
    <script id="company-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'deleteRate'], function () {
            layui.laydate.render({
                elem: '#date',
                type: 'date'
            });
            layui.deleteRate.get();
            //监听提交事件
            layui.form.on('submit(delete-rate-update-submit)', function (data) {
                layui.deleteRate.update(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>