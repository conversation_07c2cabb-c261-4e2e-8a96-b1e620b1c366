﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-checkbox[lay-skin=primary] { margin-bottom: 5px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 20px;">
        <div class="layui-input-inline" id="customer-list-view">
        </div>
        <div class="layui-form-item" style="margin-top:10px;">
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="admin-user-set-permission-submit" value="确认保存">
            </div>
        </div>
    </div>
    <script id="customer-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <input type="checkbox" name="customer" value="{{item.id}}" class="cbk" lay-skin="primary" title="{{item.alias}}" {{# if(item.isChecked){ }} checked{{# }}}>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'formAdminUser'], function () {
            layui.formAdminUser.getCustomerPermission();

            //监听提交事件
            layui.form.on('submit(admin-user-set-permission-submit)', function (data) {
                var customerIds = [];
                layui.$(":checkbox[name=customer]:checked").each(function () {
                    customerIds.push(layui.$(this).val());
                });
                data.field.customerIds = customerIds;
                layui.formAdminUser.setPermission(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>