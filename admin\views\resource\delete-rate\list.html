﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 200px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">日期</label>
                        <div class="layui-input-inline">
                            <input id="date" name="date" type="text" class="layui-input" placeholder="配置日期" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="delete-rate-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list delete-rate-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="delete-rate-list" lay-filter="list"></table>
                <script type="text/html" id="delete-rate-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'deleteRate'], function () {
            layui.laydate.render({
                elem: '#date',
                type: 'date'
            });
            layui.deleteRate.query();
            //监听查询按钮
            layui.form.on('submit(delete-rate-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("delete-rate-list", {
                    where: field
                });
            });
            //打开添加弹框
            var workWxAppId = layui.common.getUrlParam('workWxAppId') || '';
            layui.$('.delete-rate-create').click(function () {
                layui.common.openIframe('创建删除率配置', 500, 450, 'create.html?workWxAppId=' + workWxAppId);
            });
        });
    </script>
</body>
</html>