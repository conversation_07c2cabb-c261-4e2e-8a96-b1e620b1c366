﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: auto; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <fieldset class="layui-elem-field">
                    <legend>清空所有资源</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="form-resource-clear">
                                    <i class="layui-icon layui-icon-delete layuiadmin-button-btn"></i>清空所有表单资源
                                </button>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="sms-code-clear">
                                    <i class="layui-icon layui-icon-delete layuiadmin-button-btn"></i>清空所有短信验证码
                                </button>
                            </div>
                        </div>
                    </div>
                </fieldset>
                <fieldset class="layui-elem-field">
                    <legend>清空部分资源</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <div class="layui-input-inline" id="customer-list-view">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="form-resource-customerids-clear">
                                    <i class="layui-icon layui-icon-delete layuiadmin-button-btn"></i>清空选中客户表单资源
                                </button>
                            </div>
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>
    </div>
    <script id="customer-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <input type="checkbox" name="customer" value="{{item.id}}" lay-skin="primary" title="{{item.alias}}">
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'formResource', 'smsCode'], function () {
            layui.formResource.initCustomerForCheckBox();
            //监听清空资源列表按钮
            layui.form.on('submit(form-resource-clear)', function (data) {
                var confirmIndex = layui.layer.confirm('确定清空所有资源记录吗,该操作执行后数据将不可恢复，请谨慎操作！', {
                    icon: 3,
                    title: '提示',
                    btn: ['确定', '取消']
                }, function () {
                    layui.layer.close(confirmIndex);
                    layui.formResource.clearAll();
                }, function () {
                    layui.layer.close(confirmIndex);
                });
            });
            //监听清空短信验证码按钮
            layui.form.on('submit(sms-code-clear)', function (data) {
                var confirmIndex = layui.layer.confirm('确定清空所有短信验证码记录吗,该操作执行后数据将不可恢复，请谨慎操作！', {
                    icon: 3,
                    title: '提示',
                    btn: ['确定', '取消']
                }, function () {
                    layui.layer.close(confirmIndex);
                    layui.smsCode.clearAll();
                }, function () {
                    layui.layer.close(confirmIndex);
                });
            });
            //监听清空选中客户资源按钮
            layui.form.on('submit(form-resource-customerids-clear)', function (data) {
                var customerIds = [];
                layui.$(":checkbox[name=customer]:checked").each(function () {
                    customerIds.push(layui.$(this).val());
                });
                if (customerIds.length == 0) {
                    layui.common.alertAutoClose("请选择需要清除的客户");
                    return;
                }
                data.field.customerIds = customerIds;
                var confirmIndex = layui.layer.confirm('确定清空选中客户资源记录吗,该操作执行后数据将不可恢复，请谨慎操作！', {
                    icon: 3,
                    title: '提示',
                    btn: ['确定', '取消']
                }, function () {
                    layui.layer.close(confirmIndex);
                    layui.formResource.clearByCustomerIds(data);
                }, function () {
                    layui.layer.close(confirmIndex);
                });
            });
        })
    </script>
</body>
</html>