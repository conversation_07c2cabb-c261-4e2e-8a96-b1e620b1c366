﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属客户</label>
                        <div class="layui-input-inline">
                            <select name="customerId" id="customer-view">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关键字</label>
                        <div class="layui-input-inline">
                            <input id="txb_keywords" name="keywords" placeholder="关键词" type="text" class="layui-input" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="keyword-config-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="keyword-config-list" lay-filter="list"></table>
            </div>
        </div>
    </div>
    <script type="text/html" id="topToolBar">
        <div class="layui-btn-container">
            <button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="import"><i class="layui-icon">&#xe654;</i>导入</button>
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="export"><i class="layui-icon">&#xe67d;</i>导出</button>
            <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="clear"><i class="layui-icon">&#xe640;</i>清空</button>
        </div>
    </script>
    <script id="customer-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.alias}}</option>
        {{#  }); }}
    </script>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['common', 'keywordConfig'], function () {
            layui.keywordConfig.initCustomer();
            layui.keywordConfig.query();

        })
    </script>
</body>
</html>