﻿layui.define(['laytpl', 'request', 'tableRequest', 'common'], function (exports) {
    var func = {
        /**
         * 获取通话记录数据
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'call-record-list', '/admin/callcenter/callrecord/query', 'application/json', [
                {
                    field: 'userName', title: '用户名', templet: function (e) {
                        return e.userName != '' ? e.userName : '-';
                    }
                },
                { field: 'from', title: '主叫' },
                { field: 'to', title: '被叫' },
                {
                    field: 'beginTime', title: '通话开始时间', templet: function (e) {
                        return layui.common.timeFormat(e.beginTime);
                    }
                },
                {
                    field: 'ringTime', title: '通话响铃时间', templet: function (e) {
                        return e.ringTime != null ? layui.common.timeFormat(e.ringTime) : '-';
                    }
                },
                {
                    field: 'establishedTime', title: '通话接通时间', templet: function (e) {
                        return e.establishedTime != null ? layui.common.timeFormat(e.establishedTime) : '-';
                    }
                },
                {
                    field: 'establishedLength', title: '接通时长', templet: function (e) {
                        if (e.establishedLength === 0) return '-';
                        var hours = Math.floor((e.establishedLength % 86400000) / 3600000); //时
                        var minutes = Math.floor((e.establishedLength % 3600000) / 60000); //分
                        var seconds = Math.floor((e.establishedLength % 60000) / 1000); //秒

                        var text = '';
                        if (hours > 0) {
                            text += hours + '小时';
                        }
                        if (minutes > 0) {
                            text += minutes + '分';
                        }
                        if (seconds > 0) {
                            text += seconds + '秒';
                        }
                        return text;
                    }
                },
                {
                    field: 'finishedTime', title: '通话结束时间', templet: function (e) {
                        return e.finishedTime != null ? layui.common.timeFormat(e.finishedTime) : '-';
                    }
                },
                {
                    field: 'userCallType', title: '呼叫类型', templet: function (e) {
                        if (e.userCallType == 11) {
                            return '来电';
                        }
                        else if (e.userCallType == 12) {
                            return '去电';
                        }
                        else if (e.userCallType == 13) {
                            return '绑定分机对呼去电';
                        }
                        else if (e.userCallType == 14) {
                            return '监听';
                        }
                        else if (e.userCallType == 15) {
                            return '强插';
                        }
                        else if (e.userCallType == 16) {
                            return '会议';
                        }
                        else if (e.userCallType == 17) {
                            return '软转接';
                        }
                        else if (e.userCallType == 20) {
                            return '硬转接C';
                        }
                        else if (e.userCallType == 21) {
                            return '硬转接B';
                        }
                        else if (e.userCallType == 30) {
                            return '来电抢接者';
                        }
                        else if (e.userCallType == 31) {
                            return '来电被抢接';
                        }
                        else if (e.userCallType == 32) {
                            return '呼叫转移过来';
                        }
                        else {
                            return '-';
                        }
                    }
                },
                {
                    field: 'finishedReason', title: '结束原因', templet: function (e) {
                        if (e.finishedReason == 2) {
                            return '请求超时';
                        }
                        else if (e.finishedReason == 3) {
                            return '发送命令超时';
                        }
                        else if (e.finishedReason == 4) {
                            return '音频长时间静音,无语音包';
                        }
                        else if (e.finishedReason == 5) {
                            return '回铃超时';
                        }
                        else if (e.finishedReason == 6) {
                            return '未回铃';
                        }
                        else if (e.finishedReason == 7) {
                            return '通话超时';
                        }
                        else if (e.finishedReason == 16) {
                            return '呼叫失败';
                        }
                        else if (e.finishedReason == 17) {
                            return '呼叫拒接';
                        }
                        else if (e.finishedReason == 18) {
                            return '本地挂机';
                        }
                        else if (e.finishedReason == 19) {
                            return '本地取消';
                        }
                        else if (e.finishedReason == 20) {
                            return '对方挂机';
                        }
                        else if (e.finishedReason == 21) {
                            return '抢接服务器挂机';
                        }
                        else {
                            return '-';
                        }
                    }
                },
                { fixed: 'right', title: '操作', width: 120, align: 'left', toolbar: '#call-record-bar' }
            ]);
            //监听表格事件
            layui.callRecord.tableEvent();
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                var fileUrl = '//dn-sound-record.oss-cn-shanghai.aliyuncs.com/' + data.recordFileUrl;
                if (obj.event === 'download') {
                    if (data.establishedLength == 0) {
                        layui.common.alertAutoClose("当前无可下载的录音文件或录音暂未同步");
                        return;
                    }
                    window.open(fileUrl);
                }
                else if (obj.event == 'player') {
                    if (data.establishedLength == 0) {
                        layui.common.alertAutoClose("当前无可播放的录音文件或录音暂未同步");
                        return;
                    }
                    document.getElementById('audio_dom').src = fileUrl;
                    layui.common.openPage('播放录音', 500, 100, '#audio-dialog', function () {
                        document.getElementById('audio_dom').src = '';
                    });
                }
            });
        },
        /**
           * 获取通话录音列表（阿里云）
           * @param {any} customerId
           */
        queryAliCallRecord: function () {
            layui.tableRequest.request('resource', true, 'ali-call-list', '/admin/callcenter/ali/callrecord/query', 'application/json', [
                { field: 'userName', title: '通话人' },
                { field: 'contactType', title: '通话类型' },
                { field: 'startTime', title: '开始时间' },
                { field: 'releaseTime', title: '结束时间' },
                { field: 'contactDisposition', title: '结束原因' },
                { field: 'releaseInitiator', title: '挂断方' },
                {
                    field: 'callDuration', title: '通话时长', templet: function (e) {
                        if (e.callDuration == '') return '-';

                        var hours = Math.floor(parseInt(e.callDuration) / 3600);
                        var minutes = Math.floor((parseInt(e.callDuration) - (hours * 3600)) / 60);
                        var seconds = parseInt(e.callDuration) % 60;

                        return [hours, minutes, seconds]
                            .map(num => num < 10 ? '0' + num : num.toString())
                            .filter(num => num)
                            .join(':');
                    }
                },
                { fixed: 'right', title: '操作', width: 120, align: 'left', toolbar: '#ali-call-bar' }
            ], {}, null, 'auto');

            layui.table.on('tool(ali-call-list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'download') {
                    if (!data.recordingReady) {
                        layui.common.alertAutoClose("当前无可下载的录音文件或录音暂未同步");
                        return;
                    }
                    layui.callRecord.getAliCallRecordFileUrl(data.id, 1);
                }
                else if (obj.event == 'player') {
                    if (!data.recordingReady) {
                        layui.common.alertAutoClose("当前无可播放的录音文件或录音暂未同步");
                        return;
                    }
                    layui.callRecord.getAliCallRecordFileUrl(data.id, 2);
                }
            });
        },
        /**
         * 获取通话记录录音文件地址
         * @param {any} id
         * @param {any} type
         */
        getAliCallRecordFileUrl: function (id, type) {
            layui.request({
                method: 'post',
                url: '/admin/callcenter/ali/callrecord/file/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    if (type == 1) {
                        window.open(res.result.fileUrl);
                    }
                    else {
                        document.getElementById('audio_dom').src = res.result.fileUrl;
                        layui.common.openPage('播放录音', 500, 100, '#audio-dialog', function () {
                            document.getElementById('audio_dom').src = '';
                        });
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
    }
    exports('callRecord', func);
});