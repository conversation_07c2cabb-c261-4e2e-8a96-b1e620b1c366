﻿layui.define(['laytpl', 'form', 'request', 'tableRequest', 'common', 'uploadFile'], function (exports) {
    var func = {
        /**
         * 获取意见反馈列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'feedback-list', '/admin/common/feedback/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'customerName', title: '姓名' },
                { field: 'customerMobile', title: '联系方式' },
                { field: 'content', title: '反馈内容' },

                {
                    field: 'createdAt', title: '反馈时间', templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                }
            ]);
        },
    }

    exports('feedback', func);
});