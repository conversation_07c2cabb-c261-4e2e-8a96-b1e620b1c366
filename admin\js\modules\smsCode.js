﻿layui.define(['laytpl', 'form', 'laydate', 'request', 'tableRequest', 'common', 'customer'], function (exports) {
    var func = {
        /**
        * 渲染客户列表
        * @param {any} companyId
        * @param {any} selectedId
        */
        initCustomer: function (selectedId) {
            layui.customer.getAll(function (res) {
                res.result.unshift({ id: '', alias: '请选择客户' });
                var getTpl = document.getElementById("customer-tpl").innerHTML
                    , view = document.getElementById('customer-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (selectedId != undefined) {
                    view.value = selectedId;
                }
                layui.form.render('select');
            });
        },
        /**
        * 获取短信验证码列表
        * */
        query: function () {
            layui.tableRequest.request('form', true, 'sms-code-list', '/admin/res/sms-code/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'customerName', title: '客户名称' },
                { field: 'mobile', title: '手机号' },
                { field: 'code', title: '验证码' },
                { field: 'sendStatusDesc', title: '发送状态' },
                {
                    field: 'validateStatusDesc', title: '验证状态', templet: function (e) {
                        return e.validateStatusDesc + (e.validateTime ? ' - ' + layui.common.timeFormat(e.validateTime) : '');
                    }
                },
                { field: 'scene', title: '使用场景' },
                { field: 'clientIP', title: '客户端IP' },
                { field: 'remark', title: '备注' },
                { field: 'sendPageUrl', title: '落地页地址' },
                { field: 'responseContent', title: '第三方返回结果' },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                }
            ]);
        },
        /**
        * 清空所有短信验证码发送记录
        * */
        clearAll: function () {
            layui.request({
                requestBase: 'form',
                method: 'post',
                url: '/admin/res/sms-code/physical-clear-all',
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("表单资源记录清除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 绑定事件
         * */
        bindEvent: function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            //监听查询按钮
            layui.form.on('submit(sms-code-search)', function (data) {
                var field = data.field;
                if (field.sendStatus == '') {
                    field.sendStatus = -1;
                }
                if (field.validateStatus == '') {
                    field.validateStatus = -1;
                }
                //执行重载
                layui.tableRequest.reload("sms-code-list", {
                    where: field
                });
            });
        }
    };
    exports('smsCode', func);
});