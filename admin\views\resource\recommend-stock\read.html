﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>点牛优选</title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <style>
        *::-webkit-scrollbar { width: 12px; height: 12px; }
        *::-webkit-scrollbar-button { width: 0px; height: 0px; display: none; }
        *::-webkit-scrollbar-corner { background-color: transparent; }
        *::-webkit-scrollbar-thumb { border: 4px solid rgba(0, 0, 0, 0); height: 6px; border-radius: 25px; background-clip: padding-box; background-color: rgba(0, 0, 0, 0.30); }

        * { padding: 0; margin: 0; box-sizing: border-box; }
        a { color: #1477d7 !important }
        ul { list-style: none; }
        .mid { width: 1000px; margin: 0 auto; }
        .mid .con { float: left; height: 100vh; position: relative; }
        .mid .top { z-index: 999; padding: 12px 10px; font-size: 20px; font-weight: bold; background: linear-gradient(90deg,#FF7643 0%, #FFBB6C 100%); color: #fff; position: absolute; top: 0; left: 0; width: 100%; box-shadow: 0 1px 2px 0 rgba(0,0,0,.1) }
        .mid .left { width: 25%; height: 100%; background: #f8d6b8; float: left; position: relative; }
        .mid .left ul li { padding: 10px 8px; text-align: center; color: #313131; font-size: 16px; }
        .mid .left ul li:nth-child(even) { background: linear-gradient(90deg,#FF7643 0%, #FFBB6C 100%); color: #fff }
        .mid .left .tips { font-size: 14px; color: #f00; padding: 8px; margin-top: 30px; text-align: justify; text-justify: inter-word; position: absolute; bottom: 0 }
        .mid .right { width: 74.5%; float: right; padding: 0 6px 16px 16px; height: 100%; overflow-y: scroll; overflow-x: hidden; }
        .mid .right .title { color: #313131; width: 100%; text-align: center; line-height: 40px; margin: 20px 0 16px 0; }
        .mid .right .content { width: 100%; line-height: 25px; color: #212121; text-align: justify; text-justify: inter-word; font-size: 14px; margin-top: 10px; padding-left: 14px; }
        .mid .right .content img { max-width: 100%; }
        .mid .right .subtitle { width: 100%; background: #f8d6b8; padding: 6px 14px; font-size: 17px; color: #313131; font-weight: 600; margin: 20px 10px 0 0; }
        .mid .right .subtitle span { background: #FF7643; display: inline-block; width: 4px; height: 16px; float: left; margin-top: 4px; border-radius: 4px; margin-right: 10px; }
        .mid .right .report, .mid .right .statement { line-height: 25px; color: #212121; text-align: justify; text-justify: inter-word; font-size: 14px; margin-top: 5px; padding-left: 14px; }
        .mid .right .report img { max-width: 100%; }
    </style>
</head>
<body>

    <div class="mid" id="mid">
        <div class="con">
            <div class="top">
                <img src="../../../images/dn-logo-2.ico" style="width:25px;float:left;margin-right:5px;" />点牛优选
            </div>
            <div class="left">
                <div style="height:50px;"></div>
                <ul>
                    <li>发布日期：<span id="releaseAt">-</span></li>
                    <li>建议股票：<span id="stockName">-</span></li>
                    <li>建议买入区间（元）：<span id="buyAmount">-</span></li>
                    <li>建议止损区间（元）：<span id="stopLossPrice">-</span></li>
                    <li>建议仓位：<span id="position">-</span>%</li>
                    <li>状态：<span id="opStatus">-</span></li>
                    <li>建议目标区间（元）：<span id="sellAmount">-</span></li>
                    <li style="padding:6px 8px;">投资顾问：<span id="investmentAdvisor">-</span></li>
                </ul>
                <p class="tips">
                    风险提示：观点仅供参考学习，不构成具体投资建议，风险自担！股市有风险，入市需谨慎！
                </p>
            </div>
            <div class="right">
                <div style="height:50px;"></div>
                <h2 class="title" id="title"></h2>
                <h3 class="subtitle"><span></span><label id="top_title">操作理由</label></h3>
                <div class="content" id="content">
                    -
                </div>
                <div id="referenceReportDom" style="display:none;">
                    <h3 class="subtitle"><span></span><label>参考研报</label></h3>
                    <div class="report" id="referenceReport">
                        -
                    </div>
                </div>

                <h3 class="subtitle"><span></span><label>特别声明</label></h3>
                <div class="statement">
                    点牛优选有限公司是中国证监会批准的证券投资咨询机构。本信息归中的信息源均来源于公开可获得资料，我们力求准确可靠，但对信息的准确性及完整性不具有任何保证。据此投资，责任自负。本报告不构成个人投资建议，也没有考虑到个别客户特殊的投资目标财务状况或需要。客户应考虑本报告中的任何意见或建议是否符合其特定状况。
                </div>
                <div class="log-item" style="display: none;">
                    <h3 class="subtitle"><span></span><label>驳回记录</label></h3>
                    <table id="logTable" lay-filter="list"></table>
                </div>
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.41"></script>
    <script type="text/javascript">

        layui.use(['recommendStock'], function () {
            canvasWM('点牛优选');
            layui.recommendStock.getForPreview();

        });
        function canvasWM(content) {
            var canvas = document.createElement('canvas');
            const container = document.getElementById('mid');
            const fontSize = 16; // 字体大小
            const degree = -30; // 旋转角度，顺时针为正，逆时针为负
            const areaWidth = window.innerWidth; // 水印区域的宽度
            const areaHeight = window.innerHeight; // 水印区域的高度
            const nameStr = content;
            //const timeStr = (new Date()).toLocaleString();
            const lineSpace = 70; // 每行文字的垂直间隔（包含文字高度），px
            const colSpace = 3; // 显示文字的区域的宽度与文字宽度的比例
            const zIndex = 1000;

            canvas.setAttribute('width', areaWidth)
            canvas.setAttribute('height', areaHeight)

            const ctx = canvas.getContext('2d');

            ctx.rotate(degree * Math.PI / 180);
            ctx.font = `${fontSize}px Microsoft Yahei`; // 设置字体及大小
            const textWidth = ctx.measureText(nameStr).width  // 获取文字宽度
            const textUnitWidth = textWidth * colSpace + 70; // 一个文字单元的宽度（文字宽度+左右空白）

            ctx.fillStyle = 'rgba(0, 0, 0, 0.4)'; // 字体的颜色
            ctx.textAlign = "center"; // 文本的对齐方式
            ctx.textBaseline = "middle"; // 文本的基线属性
            ctx.globalAlpha = 0.5; // 透明度

            ctx.translate(100, 100); // 给画面一个基础偏移量，也可以省略该值

            let xNum = Math.ceil(areaWidth / textUnitWidth) //不旋转时横向可以展示的最多文字单元数
            let yNum = Math.ceil(areaHeight / lineSpace)//（不旋转时）纵向可以展示的最多文字单元数

            // 当文字旋转时，有一部分文字会被转走，需要扩展写字的范围，使用正弦函数确定扩展的最大范围
            let xStart = 0, yStart = 0, sin = Math.sin(Math.abs(degree) * Math.PI / 180);
            if (degree > 0) {
                // 顺时针旋转时，右侧和上侧可能会有空余
                xNum += Math.ceil(sin * areaHeight / textUnitWidth);
                yStart = Math.ceil(sin * areaWidth / lineSpace) + -1;
            } else {
                // 逆时针旋转时，左侧和下侧可能会有空余
                xStart = Math.ceil(sin * areaHeight / textUnitWidth) * -1;
                yNum += Math.ceil(sin * areaWidth / lineSpace);
            }

            for (let x = xStart; x < xNum; x++) {
                for (let y = yStart; y < yNum; y++) {
                    const offsetY = y % 2 == 0 ? 0 : textUnitWidth / 2; // 隔行横向偏移一半的距离
                    const startX = textUnitWidth * x + offsetY; // 文字的X轴起始点
                    const startY = lineSpace * y; // 文字的Y轴起始点
                    ctx.fillText(nameStr, startX, startY);
                    //ctx.fillText(timeStr, startX, startY + fontSize * 1.5);
                }
            }

            var base64Url = canvas.toDataURL();
            const watermarkDiv = document.createElement("div");
            watermarkDiv.setAttribute('style', 'position:fixed;top:0;left:0;width:100%;height:100%;z-index:' + zIndex + ';pointer-events:none;background-repeat:repeat;background-image:url(\'' + base64Url + '\')');

            container.style.position = 'relative';
            container.insertBefore(watermarkDiv, container.firstChild);

        }
    </script>
</body>
</html>