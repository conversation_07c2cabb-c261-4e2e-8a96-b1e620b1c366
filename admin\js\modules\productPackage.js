﻿layui.define(['laytpl', 'form', 'xmSelect', 'request', 'tableRequest', 'common', 'company', 'product'], function (exports) {
    var func = {
        /**
         * 获取所有产品包列表
         * @param {any} callbackFunc
         */
        getAll: function (callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/product/package/get-all',
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res)
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 渲染产品列表
         * */
        initProductForList: function () {
            layui.product.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择产品' });
                var getTpl = document.getElementById("product-tpl").innerHTML
                    , view = document.getElementById('product-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('select');
            });
        },
        /**
         * 渲染产品列表
         * @param {any} checkedIds
         */
        initProduct: function (selectIds) {
            layui.product.getAll(function (res) {
                var data = [];
                var selectItems = [];
                if (res.result.length > 0) {
                    for (var i = 0; i < res.result.length; i++) {
                        var item = { name: '[' + (res.result[i].type == 1 ? '投教' : '投顾') + '] ' + res.result[i].name, value: res.result[i].id };
                        data.push(item);
                        if (selectIds.length > 0 && selectIds.indexOf(res.result[i].id) > -1) {
                            selectItems.push(item);
                        }
                    }
                }
                xmSel = layui.xmSelect.render({
                    el: '#product-item',
                    language: 'zn',
                    filterable: true,
                    tips: '请选择产品',
                    theme: { color: '#0081ff ' },
                    data: data,
                    toolbar: { show: true },
                    autoRow: true,
                });
                if (selectItems.length > 0) {
                    xmSel.setValue(selectItems);
                }
            });
        },
        /**
         * 通过id获取产品落地页信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                setTimeout(function () {
                    parent.location.reload();
                }, 3000);
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/product/package/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=teacherName]').val(res.result.teacherName);
                    layui.productPackage.initProduct(res.result.productIds);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取产品包列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'product-package-list', '/admin/product/package/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '名称' },
                { field: 'teacherName', title: '投资顾问' },
                {
                    field: 'productIds', title: '产品数量', templet: function (e) {
                        return e.productIds.length + '个';
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', align: 'left', width: 170, toolbar: '#product-package-bar' }
            ]);
            //监听表格事件
            layui.productPackage.tableEvent();
        },
        /**
        * 创建产品包
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/package/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("产品包创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑产品包
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/product/package/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("产品包编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除产品落地页
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/product/package/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("产品包删除成功");
                    layui.landingPage.query();
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑产品包', 620, 550, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该产品包吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.productPackage.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('productPackage', func);
});