﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 200px; }
        .link { color: #0094ff }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="活动名称/参与用户" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="lottery-draw-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list lottery-draw-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="lottery-draw-list" lay-filter="list"></table>
                <script type="text/html" id="lottery-draw-bar">
                    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="status">重置中奖用户</a>
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'lotteryDraw'], function () {
            layui.lotteryDraw.query();
            //监听查询按钮
            layui.form.on('submit(lottery-draw-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("lottery-draw-list", {
                    where: field
                });
            });
            //打开添加弹框
            layui.$('.lottery-draw-create').click(function () {
                layui.common.openIframe('创建抽奖活动', 600, 400, 'create.html');
            });
        });
    </script>
</body>
</html>