﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 120px; }
        .layui-form-item .layui-input-inline { width: 250px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <input id="id" name="id" type="hidden" />
        <div class="layui-col-xs6">
            <div class="layui-form-item">
                <label class="layui-form-label">数据日期</label>
                <div class="layui-input-inline">
                    <input type="text" name="date" id="date" lay-verify="required" placeholder="请选择数据日期" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">是否工作日</label>
                <div class="layui-input-inline">
                    <select name="isWeekday" id="isWeekday" lay-verify="required">
                        <option value="">请选择是否工作日</option>
                        <option value="true">是</option>
                        <option value="false">否</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">消费总金额</label>
                <div class="layui-input-inline">
                    <input type="text" name="consumptionAmount" id="consumptionAmount" lay-verify="required" placeholder="请输入消费总金额" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">加微数量</label>
                <div class="layui-input-inline">
                    <input type="text" name="followCount" id="followCount" lay-verify="required" placeholder="请输入加微数量" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">加微成本</label>
                <div class="layui-input-inline">
                    <input type="text" name="followCost" id="followCost" lay-verify="required" readonly="readonly" placeholder="请输入加微成本" autocomplete="off" class="layui-input layui-disabled">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">去重加微数量</label>
                <div class="layui-input-inline">
                    <input type="text" name="distinctFollowCount" id="distinctFollowCount" lay-verify="required" placeholder="请输入去重加微数量" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">去重加微成本</label>
                <div class="layui-input-inline">
                    <input type="text" name="distinctFollowCost" id="distinctFollowCost" readonly="readonly" lay-verify="required" placeholder="请输入去重加微成本" autocomplete="off" class="layui-input layui-disabled">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">新增率</label>
                <div class="layui-input-inline">
                    <input type="text" name="addedRate" id="addedRate" readonly="readonly" lay-verify="required" placeholder="请输入新增率" autocomplete="off" class="layui-input layui-disabled">
                </div>
            </div>
        </div>
        <div class="layui-col-xs6">
            <div class="layui-form-item">
                <label class="layui-form-label">投资顾问</label>
                <div class="layui-input-inline">
                    <input type="text" name="teacherName" lay-verify="required" placeholder="请输入投资顾问名称" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">直播时长(小时)</label>
                <div class="layui-input-inline">
                    <input type="text" name="liveDuration" lay-verify="required" placeholder="请输入直播时长(小时)" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">助播姓名</label>
                <div class="layui-input-inline">
                    <input type="text" name="assistantName" lay-verify="required" placeholder="请输入助播姓名" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">24小时开口数</label>
                <div class="layui-input-inline">
                    <input type="text" name="opendCountFor24Hours" lay-verify="required" placeholder="请输入24小时内开口总数" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">24小时去重开口数</label>
                <div class="layui-input-inline">
                    <input type="text" name="distinctOpendCountFor24Hours" lay-verify="required" placeholder="请输入24小时内全局去重开口总数" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">24小时删除数</label>
                <div class="layui-input-inline">
                    <input type="text" name="deletedCountFor24Hours" lay-verify="required" placeholder="请输入24小时内删除总数" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">24小时去重删除数</label>
                <div class="layui-input-inline">
                    <input type="text" name="distinctDeletedCountFor24Hours" lay-verify="required" placeholder="请输入24小时内全局去重删除总数" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <div class="layui-input-inline">
                    <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="external-contact-stat-update-submit" value="确认修改">
                </div>
            </div>
        </div>
    </div>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.1"></script>
    <script type="text/javascript">
        layui.use(['form', 'externalContact'], function () {
            layui.externalContact.getStatRecord();
            //监听提交事件
            layui.form.on('submit(external-contact-stat-update-submit)', function (data) {
                layui.externalContact.updateStatRecord(data);
                return false; //阻止表单跳转
            });
            layui.laydate.render({
                elem: '#date',
                type: 'date',
            });
            layui.$("#consumptionAmount,#followCount,#distinctFollowCount").blur(function () {
                var $this = layui.$(this);
                var totalCount = parseInt(layui.$('#followCount').val());
                var globalDeDuplicationCount = parseInt(layui.$('#distinctFollowCount').val());
                if ($this.val() != '') {
                    var consumption = parseInt(layui.$('#consumptionAmount').val());
                    var cost = consumption;
                    if (totalCount > 0) {
                        cost = consumption / totalCount;
                    }
                    var distinctCost = consumption;
                    if (globalDeDuplicationCount > 0) {
                        distinctCost = consumption / globalDeDuplicationCount;
                    }
                    layui.$('#followCost').val(cost.toFixed(2));
                    layui.$('#distinctFollowCost').val(distinctCost.toFixed(2));
                }
                if (totalCount != '' && globalDeDuplicationCount != '') {
                    layui.$('#addedRate').val(parseFloat((globalDeDuplicationCount / totalCount * 100).toFixed(2)));

                }
            });
        })
    </script>
</body>
</html>