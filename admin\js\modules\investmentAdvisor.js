﻿layui.define(['laytpl', 'request', 'tableRequest', 'uploadFile', 'common', 'subsidiary', 'adminUser'], function (exports) {
    var func = {
        /**
        * 渲染所属组织下拉选框
        * */
        initSubsidiary: function (id) {
            layui.subsidiary.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择所属组织' });
                var getTpl = document.getElementById("subsidiary-tpl").innerHTML
                    , view = document.getElementById('subsidiary-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('subsidiary-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
       * 渲染用户列表
       * */
        initUser: function (id) {
            layui.adminUser.queryByCompanyId(layui.setter.companyId, function (res) {
                res.result.unshift({ id: '', name: '请选择绑定账号' });
                var getTpl = document.getElementById("user-tpl").innerHTML
                    , view = document.getElementById('user-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    view.value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过id获取投资顾问
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/basis/investment-advisor/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=certNo]').val(res.result.certNo);
                    layui.$('.img-info').html('<img src="' + res.result.certificateUrl + '" class="" height="35" />');
                    layui.$('#certificateUrl').val(res.result.certificateUrl);
                    layui.investmentAdvisor.initSubsidiary(res.result.subsidiaryId);
                    layui.investmentAdvisor.initUser(res.result.userId);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过组织ID查询投资顾问列表
         * @param {any} callbackFunc
         */
        queryBySubsidiaryId: function (subsidiaryId, callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/basis/investment-advisor/by-subsidiaryid/qurey?subsidiaryId=' + subsidiaryId,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                callbackFunc(res);
            })
        },
                /**
         * 通过组织ID查询投资顾问列表 新
         * @param {any} callbackFunc
         */
                newQueryBySubsidiaryId: function (subsidiaryId, callbackFunc) {
                    layui.request({
                        method: 'post',
                        url: '/admin/for-order/investment-advisor/by-subsidiaryid/qurey?subsidiaryId=' + subsidiaryId,
                        headers: { 'Content-Type': 'application/json' },
                    }).then(function (res) {
                        callbackFunc(res);
                    })
                },
        /**
         * 通过当前登录用户id查询投资顾问列表
         * @param {any} callbackFunc
         */
        queryByUserId: function (callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/basis/investment-advisor/by-userid/qurey',
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                callbackFunc(res);
            })
        },
        /**
         * 获取投资顾问列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'investment-advisor-list', '/admin/basis/investment-advisor/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '名称' },
                { field: 'certNo', title: '证件编号' },
                { field: 'subsidiaryName', title: '所属组织' },
                { field: 'userName', title: '绑定账号' },
                {
                    title: '凭证', templet: function (e) {
                        if (e.certificateUrl != null && e.certificateUrl != '') {
                            return '<a href="preview.html?url=' + e.certificateUrl + '" target="_blank"><img src="' + e.certificateUrl + '" class="" height="35" /></a>';
                        }
                        return '-';
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 130, align: 'left', toolbar: '#investment-advisor-bar' }
            ]);
            //监听表格事件
            layui.investmentAdvisor.tableEvent();
        },
        /**
        * 创建投资顾问
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/basis/investment-advisor/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("investment-advisor-list");
                    parent.layui.common.alertAutoClose("投顾创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑投资顾问
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/basis/investment-advisor/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("investment-advisor-list");
                    parent.layui.common.alertAutoClose("投顾编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除投资顾问
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/basis/investment-advisor/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("investment-advisor-list");
                    layui.common.alertAutoClose("投顾删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑投顾', 500, 450, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除当前投顾吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.investmentAdvisor.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('investmentAdvisor', func);
});