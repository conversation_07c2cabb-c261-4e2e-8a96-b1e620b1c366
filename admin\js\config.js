﻿layui.define(['laytpl', 'layer', 'element', 'util'], function (exports) {
    exports('setter', {
        container: 'LAY_app' //容器ID
        , base: layui.cache.base //记录静态资源所在路径
        , views: layui.cache.base + 'tpl/' //动态模板所在目录
        , entry: 'index' //默认视图文件名
        , engine: '.html' //视图文件后缀名
        , pageTabs: true //是否开启页面选项卡功能。iframe版推荐开启

        , name: '后台管理'
        , logo: ''
        , debug: true //是否开启调试模式。如开启，接口异常时会抛出异常 URL 等信息

        //自定义请求字段
        , request: {
            //  ssoBaseUrl: 'http://localhost:5000',
            //  resourceBaseUrl: 'http://localhost:5000',
            //  formBaseUrl: 'http://localhost:5000',
            //  logBaseUrl: 'http://localhost:5000',
            //  payBaseUrl: 'http://localhost:5000',

            ssoBaseUrl: 'https://test.dnyx.cn/resource',//'http://localhost:8081',
            resourceBaseUrl: 'https://test.dnyx.cn/resource',//8083
            formBaseUrl: 'https://test.dnyx.cn/resource',
            logBaseUrl: 'https://test.dnyx.cn/resource',
            payBaseUrl: 'https://test.dnyx.cn/resource',
            formCustomerHost: 'sa.yaquxq.com',
            tokenName: 'x-token' //自动携带 token 的字段名（如：access_token）。可设置 false 不携带。
        }
        , aliCloudCallInstanceId: 'dn2024'
        , h5PageUrl: 'https://h5.dnyx.com/pages/dy/dn/tyhk01/index.html'
        , productCompanyId: '6340ecbf4d7e5a21ed46e996'
        , companyId: '6340ecbf4d7e5a21ed46e996'
        , roleId: '63749d2d883bc542796ffc99'
        , workWxAppId: '62e23f633e6ecaa906b2f2c7'
        , kfLinkUrl: 'https://dy5.zqt888.cn/pages/qrcode/zqt_v3.html?id={id}&uid={uid}&sp={sp}&kfid={kfid}'
        , stockAmountRangePercentage: 0.3
        , rsaPublicKey: '-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgIYcbCUEolb0yLWsK5bcTeJGsIwk5caKUNwDICrxjYxyJVftt1Peew4Rs9fT2pYUlP6BkZvFtUg5qphpn9oByBDOhlId9vUK9BipGsCioSSQq50PM3hC2VaInPhicWNeFLxE7h2GNQzk9dFWa/0VIFrcfNNJgNleGuogHFdxq79VrLp3GjhxSzd2xS4FlHSFEb5XNEexYzOmgot4QOTZdNAWV/Dpb28KqBDtxpjNbQrWk3XcrWT0lleIFeFPFg7Fc9y9v2sTaF1FpR8XE/o21puCm4StHbvboYZE2FW4BzQcsfkoYXNF3slTPTHXUSrz9AK+8LQAjziFp1x5lLH8WwIDAQAB-----END PUBLIC KEY-----'
        , callConfig: {
            isOpend: false,
            ip: '************',
            port: 8833,
            registerPort: 6060,
            rsaPrivateKey: '-----BEGIN RSA PRIVATE KEY-----MIICXAIBAAKBgQCHFQWlPoqWipJjlVtpz0sB3Uf5TMP65p2QGFN3T+bVDo70ZHNp+2ipgtEhoRkQXPx8P1gE0ymUEEf4bb7vvsS+NTJq0VfyJRV2tEG83uqhH+Ol5IfJ5UoS9jSfaTkgwJOl/WA+b/L0mkeuuN4RIkXbdDBR9nN8eDuQ/TKbODJUUQIDAQABAoGAR/LnheNw98wpOseD4DDcV8rgGiZMQKhzBhwe4O77GUJe3EdgvfN+BMO71nizG+cbSKaIoFFE1Sty2hzCVXo72fc2laPc0lnwkhdVutC3cGlF5bbwMTOdA+TgkVQLOw9h4+FAE1FoNQu98CnQ5zfwN8pNzgUayDNn+s3fYbVd8lECQQDbXxpgbepnAKCl96nG776BT7YrpAEhOunEkMG7ElnsqqcxtXqGwZ2iEfbRg4yP++e7W0xF7pIZcBljx2vAK26NAkEAnaMFvGl3t18epWzQRbvKtqUiWxWM2bE4CcrWCpUNyOysx+1aDTv/7Oa6HO0iZxjau2UC2h4o52vxeRcOMob91QJAdIM2hzDZ5os2x++ahMag8B1eLB+XWgboUjZwLuO4TzL0SjqRMTF0f7f615x+85pfoesDv8DOaCRZ19z7Jb3roQJAabGy2N/Ge1b1z2kkRH1jsA8M+BF8/UGMF1S/Kx1BvuSh/PMyHAmM0aTqSRbeqE3L6zYRMXpjZrfDkrNYu5wzeQJBAJ2l6rbcYXZHgMKmDRdApI3a1GNCt6jB5nZreCofqOCGY62bwLKV2pVYDNh+WE8uPdvPj/qHFfy3qPlraGAYA1E=-----END RSA PRIVATE KEY-----'
        }
        , payTypeArr: [
            { id: 10, name: '线上-微信内支付', isOnline: true },
            { id: 11, name: '线上-微信外支付', isOnline: true },
            { id: 20, name: '线上-支付宝支付', isOnline: true },
            { id: 201, name: '线下-微信支付', isOnline: false },
            { id: 202, name: '线下-支付宝支付', isOnline: false },
            { id: 203, name: '线下-乐刷', isOnline: false },
            { id: 204, name: '线下-建行(0055)', isOnline: false },
            { id: 205, name: '线下-收款账号', isOnline: false },
            { id: 206, name: '线下-企微', isOnline: false },
            { id: 207, name: '线下-农行(4624)', isOnline: false },
            { id: 300, name: '混合支付', isOnline: false },
        ]
        , excludeMenuIds: [

        ]
        //主题配置
        , theme: {
            //内置主题配色方案
            color: [{
                main: '#20222A' //主题色
                , selected: '#50b1fb' //选中色
                , alias: 'default' //默认别名
            }, {
                main: '#03152A'
                , selected: '#3B91FF'
                , alias: 'dark-blue' //藏蓝
            }, {
                main: '#2E241B'
                , selected: '#A48566'
                , alias: 'coffee' //咖啡
            }, {
                main: '#50314F'
                , selected: '#7A4D7B'
                , alias: 'purple-red' //紫红
            }, {
                main: '#344058'
                , logo: '#1E9FFF'
                , selected: '#1E9FFF'
                , alias: 'ocean' //海洋
            }, {
                main: '#3A3D49'
                , logo: '#2F9688'
                , selected: '#5FB878'
                , alias: 'green' //墨绿
            }, {
                main: '#20222A'
                , logo: '#F78400'
                , selected: '#F78400'
                , alias: 'red' //橙色
            }, {
                main: '#28333E'
                , logo: '#AA3130'
                , selected: '#AA3130'
                , alias: 'fashion-red' //时尚红
            }, {
                main: '#24262F'
                , logo: '#3A3D49'
                , selected: '#50b1fb'
                , alias: 'classic-black' //经典黑
            }, {
                logo: '#226A62'
                , header: '#2F9688'
                , alias: 'green-header' //墨绿头
            }, {
                main: '#344058'
                , logo: '#0085E8'
                , selected: '#1E9FFF'
                , header: '#1E9FFF'
                , alias: 'ocean-header' //海洋头
            }, {
                header: '#393D49'
                , alias: 'classic-black-header' //经典黑头
            }, {
                main: '#50314F'
                , logo: '#50314F'
                , selected: '#7A4D7B'
                , header: '#50314F'
                , alias: 'purple-red-header' //紫红头
            }, {
                main: '#28333E'
                , logo: '#28333E'
                , selected: '#AA3130'
                , header: '#AA3130'
                , alias: 'fashion-red-header' //时尚红头
            }, {
                main: '#28333E'
                , logo: '#50b1fb'
                , selected: '#50b1fb'
                , header: '#50b1fb'
                , alias: 'green-header' //墨绿头
            }]

            //初始的颜色索引，对应上面的配色方案数组索引
            //如果本地已经有主题色记录，则以本地记录为优先，除非请求本地数据（localStorage）
            , initColorIndex: 0
        }
    });
});