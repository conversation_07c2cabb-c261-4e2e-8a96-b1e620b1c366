﻿layui.define(['laytpl', 'request', 'tableRequest', 'common', 'workWxApp', 'kfCase', 'oceanengineApp'], function (exports) {
    var func = {
        /**
         * 获取所有推广账户
         * @param {any} callbackFunc
         */
        getAll: function (callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/oceanengine/account/get-all',
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 渲染企微应用下拉选框
         * @param {any} id
         */
        initWorkWxApp: function (id) {
            layui.workWxApp.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企微' });
                var getTpl = document.getElementById("workwx-app-tpl").innerHTML
                    , view = document.getElementById('workwx-app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('workwx-app-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 渲染巨量应用下拉选框
         * @param {any} id
         */
        initOceanengineApp: function (id) {
            layui.oceanengineApp.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择巨量应用' });
                var getTpl = document.getElementById("oceanengine-app-tpl").innerHTML
                    , view = document.getElementById('oceanengine-app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('oceanengine-app-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过id获取单个巨量账户
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/oceanengine/account/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=accountId]').val(res.result.accountId);
                    layui.$('input[name=accountStringId]').val(res.result.accountStringId);
                    layui.$('input[name=accountName]').val(res.result.accountName);
                    layui.oceanengineAccount.initOceanengineApp(res.result.oceanengineAppId);
                    layui.oceanengineAccount.initWorkWxApp(res.result.workWxAppId);
                    layui.kfCase.queryByWorkWxAppId(res.result.workWxAppId, function (resKfCase) {
                        if (resKfCase.result == null) {
                            resKfCase.result = [{ id: '', name: '请选择加粉方案' }]
                        }
                        else {
                            resKfCase.result.unshift({ id: '', name: '请选择加粉方案' });
                        }
                        var getTpl = document.getElementById("kf-case-tpl").innerHTML
                            , view = document.getElementById('kf-case-view');
                        layui.laytpl(getTpl).render(resKfCase.result, function (html) {
                            view.innerHTML = html;
                        });
                        document.getElementById('kf-case-view').value = res.result.kfCaseId;
                        layui.form.render('select');
                    });
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取巨量账户
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'account-list', '/admin/workwx/oceanengine/account/query', 'application/json', [
                { type: 'checkbox', fixed: 'left' },
                { field: 'oceanengineAppName', title: '应用名称', width: 150 },
                { field: 'accountId', title: '账户ID', width: 200 },
                { field: 'accountName', title: '账户名称' },
                { field: 'kfCaseName', title: '绑定加粉方案' },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 150, align: 'left', toolbar: '#account-bar' }
            ], {}, '#topToolBar');
            //监听表格事件
            layui.oceanengineAccount.tableEvent();
        },
        /**
        * 创建巨量账户
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/oceanengine/account/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("account-list");
                    parent.layui.common.alertAutoClose("巨量账户创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑巨量账户
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/oceanengine/account/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("account-list");
                    parent.layui.common.alertAutoClose("巨量账户编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除巨量账户
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/oceanengine/account/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("account-list");
                    layui.common.alertAutoClose("巨量账户删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 批量删除
         * @param {any} ids
         */
        batchDelete: function (ids) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/oceanengine/account/batch-delete',
                data: JSON.stringify({ ids: ids }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("account-list");
                    layui.common.alertAutoClose("巨量账户删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('toolbar(list)', function (obj) {
                if (obj.event === 'batch-delete') {
                    var checkedList = layui.table.checkStatus(obj.config.id);
                    if (checkedList.data.length == 0) {
                        layui.common.alertAutoClose("请选择需要操作的记录");
                        return;
                    }
                    var ids = [];
                    for (var i = 0; i < checkedList.data.length; i++) {
                        ids.push(checkedList.data[i].id);
                    }
                    layui.oceanengineAccount.batchDelete(ids);
                }
            });
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑巨量应用', 600, 450, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该巨量账户吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.oceanengineAccount.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('oceanengineAccount', func);
});