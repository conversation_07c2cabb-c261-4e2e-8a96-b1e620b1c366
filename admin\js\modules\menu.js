﻿layui.define(['laytpl', 'form', 'request', 'app'], function (exports) {
    var func = {
        /**
         * 获取菜单列表，外部调用
         * @param {any} appId
         * @param {any} callbackFunc
         */
        getByAppId: function (appId, callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/sso/menu/auth/query?appId=' + appId,
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取所有应用列表
         * */
        initApp: function (id) {
            layui.app.getAll(function (res) {
                var getTpl = document.getElementById("app-tpl").innerHTML
                    , view = document.getElementById('app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id == undefined || id == '') {
                    id = res.result[0].id;
                }
                document.getElementById('app-view').value = id;
                layui.form.render('select');
                layui.menu.query(id);
            });
        },
        /**
         * 通过id获取菜单信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/sso/menu/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=url]').val(res.result.url);
                    layui.$('input[name=icon]').val(res.result.icon);
                    layui.$('input[name=sort]').val(res.result.sort);
                    layui.$('#show_icon').html(res.result.icon);
                    layui.$('#isShow').prop("checked", res.result.isShow ? true : false);
                    if (res.result.permissions != null && res.result.permissions.length > 0) {
                        for (var i = 0; i < res.result.permissions.length; i++) {
                            var menuTpl = '<div class="menu-input-item" style="margin-bottom:10px;float:left">';
                            menuTpl += '<div class="layui-input-inline" style="width:210px">';
                            menuTpl += '<input type="text" lay-verify="required" value="' + res.result.permissions[i].key + '" placeholder="权限KEY,不可重复" autocomplete="off" class="layui-input menu-key">';
                            menuTpl += '</div>';
                            menuTpl += '<div class="layui-input-inline" style="width: 280px; ">';
                            menuTpl += '<div class="layui-input-inline" style="width:210px">';
                            menuTpl += '<input type="text" lay-verify="required" value="' + res.result.permissions[i].name + '" placeholder="权限名称" autocomplete="off" class="layui-input menu-name">';
                            menuTpl += '</div>';
                            menuTpl += '<div class="layui-input-inline" style="width:50px;">';
                            menuTpl += '<button type="button" class="layui-btn layui-btn-primary remove-menu">';
                            menuTpl += '<i class="layui-icon">&#xe640;</i>';
                            menuTpl += '</button>';
                            menuTpl += '</div>';
                            menuTpl += '<div class="reply-container">';
                            menuTpl += '</div>';
                            menuTpl += '</div>';
                            menuTpl += '</div>';
                            layui.$('.menu-input-list').append(menuTpl);
                            layui.form.render('select');
                        }
                    }
                    layui.form.render('checkbox');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 查询菜单列表
         * @param {any} appId
         */
        query: function (appId) {
            layui.request({
                method: 'post',
                url: '/admin/sso/menu/query?appId=' + appId,
            }).then(function (res) {
                if (res.isSuccess) {
                    var getTpl = document.getElementById("menu-tpl").innerHTML
                        , view = document.getElementById('menu-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });

                    //绑定添加事件
                    layui.$('.menu-create').unbind('click');
                    layui.$('.menu-create').bind('click', function () {
                        var appId = layui.$('#app-view').val();
                        var parentId = layui.$(this).data('id');
                        layui.common.openIframe('创建系统菜单', 710, 600, 'create.html?appId=' + appId + '&parentId=' + parentId);
                    });
                    //绑定编辑事件
                    layui.$('.menu-update').unbind('click');
                    layui.$('.menu-update').bind('click', function () {
                        var id = layui.$(this).data('id');
                        layui.common.openIframe('编辑系统菜单', 710, 600, 'update.html?id=' + id);
                    });
                    //绑定删除事件
                    layui.$('.menu-del').unbind('click');
                    layui.$('.menu-del').bind('click', function () {
                        var id = layui.$(this).data('id');
                        var confirmIndex = layui.layer.confirm('确定删除该系统菜单吗？', {
                            icon: 3,
                            title: '提示',
                            btn: ['确定', '取消']
                        }, function () {
                            layui.menu.delete(id);
                        }, function () {
                            layui.layer.close(confirmIndex);
                        });
                    });
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 创建系统菜单
         * @param {any} data
         */
        create: function (data) {
            var appId = layui.common.getUrlParam('appId') || '';
            if (appId == '') {
                layui.common.alert('应用id有误', 0);
                return;
            }
            var parentId = layui.common.getUrlParam('parentId') || '';
            var isShow = layui.$('#isShow').is(':checked')
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/sso/menu/create',
                data: JSON.stringify({
                    appId: appId,
                    parentId: parentId,
                    name: data.field.name,
                    url: data.field.url,
                    icon: data.field.icon,
                    isShow: isShow,
                    sort: data.field.sort,
                    permissions: data.field.permissions
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    var appId = parent.layui.$('#app-view').val();
                    parent.layui.menu.query(appId);
                    parent.layui.common.alertAutoClose("系统菜单创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过id编辑系统菜单信息
         * @param {any} data
         */
        update: function (data) {
            var isShow = layui.$('#isShow').is(':checked')
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/sso/menu/update',
                data: JSON.stringify({
                    id: data.field.id,
                    name: data.field.name,
                    url: data.field.url,
                    icon: data.field.icon,
                    isShow: isShow,
                    sort: data.field.sort,
                    permissions: data.field.permissions
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    var appId = parent.layui.$('#app-view').val();
                    parent.layui.menu.query(appId);
                    parent.layui.common.alertAutoClose("系统菜单编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除系统菜单
         * @param {any} id
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/sso/menu/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    var appId = layui.$('#app-view').val();
                    layui.menu.query(appId);
                    layui.common.alertAutoClose("系统菜单删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 绑定事件
         * @param {any} opType
         */
        bindEvent: function (opType) {
            //添加菜单行
            layui.$(document).on("click", '.add-menu', function () {
                var menuTpl = '<div class="menu-input-item" style="margin-bottom:10px;float:left">';
                menuTpl += '<div class="layui-input-inline" style="width:210px">';
                menuTpl += '<input type="text" lay-verify="required" placeholder="权限KEY,不可重复" autocomplete="off" class="layui-input menu-key">';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width: 280px; ">';
                menuTpl += '<div class="layui-input-inline" style="width:210px">';
                menuTpl += '<input type="text" lay-verify="required" placeholder="权限名称" autocomplete="off" class="layui-input menu-name">';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:50px;">';
                menuTpl += '<button type="button" class="layui-btn layui-btn-primary remove-menu">';
                menuTpl += '<i class="layui-icon">&#xe640;</i>';
                menuTpl += '</button>';
                menuTpl += '</div>';
                menuTpl += '<div class="reply-container">';
                menuTpl += '</div>';
                menuTpl += '</div>';
                menuTpl += '</div>';
                layui.$('.menu-input-list').append(menuTpl);
            });

            //删除菜单行
            layui.$(document).on("click", '.remove-menu', function () {
                layui.$(this).parent().parent().parent('.menu-input-item').remove();
            });

            //应用选择后，加载系统菜单列表
            layui.form.on('select(menu)', function (data) {
                layui.menu.query(data.value);
            });

            //提交数据
            layui.form.on('submit(menu-submit)', function (data) {
                var permissions = [];
                var menuList = layui.$(".menu-input-item");
                for (var i = 0; i < menuList.length; i++) {
                    var that = menuList.eq(i);
                    var option = { key: that.find('.menu-key').val(), name: that.find('.menu-name').val() };
                    permissions.push(option);
                }
                data.field.permissions = permissions;
                if (opType == 'create') {
                    layui.menu.create(data);
                }
                else if (opType == 'update') {
                    layui.menu.update(data);
                }
                return false; //阻止表单跳转
            });
        }
    }

    exports('menu', func);
});