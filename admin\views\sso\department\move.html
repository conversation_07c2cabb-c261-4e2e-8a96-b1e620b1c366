﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 380px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <input id="id" name="id" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">父级部门</label>
            <div class="layui-input-inline">
                <select name="parentId" id="department-view" lay-search>
                    <option value="">设置为一级部门</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="department-submit" value="确认添加">
            </div>
        </div>
    </div>
    <script id="department-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'department'], function () {
            var id = layui.common.getUrlParam('id') || '';
            var companyId = layui.common.getUrlParam('companyId') || '';
            layui.$('#id').val(id);
            layui.department.initDepartment(companyId, '');
            //监听提交事件
            layui.form.on('submit(department-submit)', function (data) {
                layui.department.updateParentId(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>