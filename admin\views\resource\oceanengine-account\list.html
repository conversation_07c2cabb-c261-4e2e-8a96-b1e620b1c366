﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="txb_keywords" name="keywords" placeholder="账户ID/名称" type="text" class="layui-input" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="account-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list account-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="account-list" lay-filter="list"></table>
                <script type="text/html" id="account-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>
    <script type="text/html" id="topToolBar">
        <div class="layui-btn-container layui-input-inline">
            <button class="layui-btn layui-btn-sm layuiadmin-btn-list layui-btn-primary" lay-event="batch-delete">
                <i class="layui-icon layui-icon-delete layuiadmin-button-btn"></i>批量删除
            </button>
        </div>
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'oceanengineAccount'], function () {
            layui.oceanengineAccount.query();
            //监听查询按钮
            layui.form.on('submit(account-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("account-list", {
                    where: field
                });
            });
            //打开添加弹框
            layui.$('.account-create').click(function () {
                layui.common.openIframe('创建巨量账户', 600, 450, 'create.html');
            });
        });
    </script>
</body>
</html>