﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 200px; }
        .layui-table tbody > tr:nth-last-child(1) { background: #eee; font-weight: bold; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属企微</label>
                        <div class="layui-input-inline">
                            <select name="workWxAppId" id="workwx-app-view" lay-filter="workwx-app-id">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">加粉方案</label>
                        <div class="layui-input-inline">
                            <select name="kfCaseId" id="kf-case-view" lay-search>
                                <option value="">请选择加粉方案</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="投资顾问名称/助播名称" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">查询时间</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="stat-record-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                        <button class="layui-btn  layuiadmin-btn-list layui-btn-primary" id="external-contact-stat-export" lay-submit lay-filter="external-contact-stat-export">
                            <i class="layui-icon layui-icon-export layuiadmin-button-btn"></i>导出
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="stat-record-list" lay-filter="list"></table>
                <script type="text/html" id="stat-record-bar">
                    {{#if(d.id==null){ }}
                    {{# }else{ }}
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                    {{# }}}
                </script>
            </div>
        </div>
    </div>

    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="kf-case-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'externalContact'], function () {
            layui.externalContact.initApp();
            layui.externalContact.queryStatRecord();
            //监听查询按钮
            layui.form.on('submit(stat-record-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("stat-record-list", {
                    where: field
                });
            });
            layui.form.on('submit(external-contact-stat-export)', function (data) {
                var confirmIndex = layui.layer.confirm('确定导出当前数据吗？', {
                    icon: 3,
                    title: '提示',
                    btn: ['确定', '取消']
                }, function () {
                    layui.externalContact.exportStat(data);
                    layui.layer.close(confirmIndex);
                }, function () {
                    layui.layer.close(confirmIndex);
                });
            });
            layui.laydate.render({
                elem: '#startTime',
                type: 'date',
                //value: new Date(new Date(new Date().toLocaleDateString()).getTime())
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'date',
                //value: new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
            });
            //切换企微应用时加载加粉方案
            layui.form.on('select(workwx-app-id)', function (data) {
                layui.kfCase.queryByWorkWxAppId(data.value, function (res) {
                    if (res.result == null) {
                        res.result = [{ id: '', name: '请选择加粉方案' }]
                    }
                    else {
                        res.result.unshift({ id: '', name: '请选择加粉方案' });
                    }
                    var getTpl = document.getElementById("kf-case-tpl").innerHTML
                        , view = document.getElementById('kf-case-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.form.render('select');
                });
            });
        });
    </script>
</body>
</html>