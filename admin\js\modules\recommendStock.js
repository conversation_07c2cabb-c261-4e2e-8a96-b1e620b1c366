﻿layui.define(['laytpl', 'form', 'request', 'uploadFile', 'tableRequest', 'common', 'subsidiary', 'investmentAdvisor', "jquery"], function (exports) {
    var func = {
        /**
       * 渲染所属组织下拉选框
       * */
        initSubsidiary: function (id) {
            layui.subsidiary.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择所属组织' });
                var getTpl = document.getElementById("subsidiary-tpl").innerHTML
                    , view = document.getElementById('subsidiary-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('subsidiary-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 渲染投资顾问下拉选框
         * @param {any} id
         */
        initInvestmentadvisor: function (id) {
            layui.investmentAdvisor.queryByUserId(function (res) {
                res.result.unshift({ id: '', name: '请选择投资顾问', certNo: '' });
                var getTpl = document.getElementById("investmentadvisor-tpl").innerHTML
                    , view = document.getElementById('investmentadvisor-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('investmentadvisor-view').value = id;
                }
                else {
                    if (res.result.length == 2) {
                        document.getElementById('investmentadvisor-view').value = res.result[1].id;
                        document.getElementById('investmentadvisor-view').setAttribute("disabled", "disabled");
                    }
                }
                layui.form.render('select');
            });
        },
        /**
         * 渲染投资顾问下拉选框【审核】
         * @param {any} id
         */
        initAllInvestmentadvisor: function (id) {
            layui.investmentAdvisor.queryBySubsidiaryId('', function (res) {
                res.result.unshift({ id: '', name: '请选择投资顾问', certNo: '' });
                var getTpl = document.getElementById("investmentadvisor-tpl").innerHTML
                    , view = document.getElementById('investmentadvisor-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('investmentadvisor-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
        * 渲染投资顾问下拉选框【审核】新
        * @param {any} id
        */
        newInitAllInvestmentadvisor: function (id) {
            layui.investmentAdvisor.newQueryBySubsidiaryId('', function (res) {
                res.result.unshift({ id: '', name: '请选择投资顾问', certNo: '' });
                var getTpl = document.getElementById("investmentadvisor-tpl").innerHTML
                    , view = document.getElementById('investmentadvisor-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('investmentadvisor-view').value = id;
                }
                layui.form.render('select');
            });
        },

        /**
         * 初始化富文本编辑器
         * */
        initTinymce: function (id) {
            tinymce.init({
                selector: id,
                language: 'zh_CN',
                menubar: false,
                branding: false,
                plugins: 'preview fullscreen image link media code table advlist lists wordcount emoticons autosave autoresize axupimgs',
                toolbar: 'code pastetext fontsize fontfamily forecolor backcolor link alignleft aligncenter alignright alignjustify image media axupimgs  outdent indent styleselect bullist numlist bold italic underline strikethrough blockquote subscript superscript removeformat table charmap emoticons pagebreak insertdatetime preview fullscreen bdmap indent2em lineheight formatpainter',
                toolbar_mode: 'wrap',
                min_height: 400,
                max_height: 600,
                font_size_formats: '11px 12px 13px 14px 15px 16px 17px 18px 20px 24px 36px 48px 56px 72px',
                font_size_input_default_unit: "px",
                font_family_formats: '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;',
                importcss_append: true,
                images_upload_handler(blobInfo, progress) {
                    return new Promise((resolve, reject) => {
                        var upurl = layui.setter.request.resourceBaseUrl + '/common/file/public/tinymce/put?folder=files/recommendstock/detail';
                        var file = blobInfo.blob();
                        var xhr, formData;
                        xhr = new XMLHttpRequest();
                        xhr.withCredentials = false;
                        xhr.open('POST', upurl);
                        xhr.onload = function () {
                            if (xhr.status != 200) {
                                reject('HTTP Error: ' + xhr.status);
                                return;
                            }
                            var json = JSON.parse(xhr.responseText);
                            if (!json || json.location == '') {
                                reject(xhr.responseText);
                                return;
                            }
                            resolve(json.location);
                        };
                        formData = new FormData();
                        formData.append('file', file, file.name);
                        xhr.send(formData);
                    })
                },
                //自定义文件选择器的回调内容
                file_picker_callback: function (callback, value, meta) {
                    var filetype = '.pdf, .txt, .zip, .rar, .7z, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .mp3, .mp4';
                    var upurl = layui.setter.request.resourceBaseUrl + '/common/file/public/tinymce/put?folder=files/recommendstock/detail';
                    switch (meta.filetype) {
                        case 'image':
                            filetype = '.jpg, .jpeg, .png, .gif';
                            break;
                        case 'media':
                            filetype = '.mp3, .mp4';
                            break;
                        default:
                    }
                    var input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    input.setAttribute('accept', filetype);
                    input.click();
                    input.onchange = function () {
                        var file = this.files[0];
                        var xhr, formData;
                        xhr = new XMLHttpRequest();
                        xhr.withCredentials = false;
                        xhr.open('POST', upurl);
                        xhr.onload = function () {
                            var json;
                            if (xhr.status != 200) {
                                failure('HTTP Error: ' + xhr.status);
                                return;
                            }
                            json = JSON.parse(xhr.responseText);
                            if (!json || typeof json.location != 'string') {
                                failure('Invalid JSON: ' + xhr.responseText);
                                return;
                            }
                            callback(json.location);
                        };
                        formData = new FormData();
                        formData.append('file', file, file.name);
                        xhr.send(formData);
                    };
                },
                autosave_ask_before_unload: false,
                link_default_target: '_blank'
            });
        },
        /**
         * 通过id获取单个产品信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/media/recommend-stock/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    if (res.result.type != 1) {
                        layui.jquery(".hidden").css("display", "none")
                        layui.jquery(".hidden")[0].innerHTML = '';
                        layui.jquery(".layNoRequired").css("display", "block")
                        layui.jquery(".layNoRequired")[0].innerHTML =
                            `
                            <label class="layui-form-label">仓位(%)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="position" placeholder="请输入仓位（%）" autocomplete="off"
                                    class="layui-input">
                            </div>
                        `
                        layui.jquery(".layRequired").css("display", "none")
                        layui.jquery(".layRequired")[0].innerHTML = '';
                        layui.jquery(".inlineBox").css("display", "none")
                        layui.jquery(".inlineBox")[0].innerHTML = '';
                        layui.jquery(".inlineHidden").css("display", "inline-block")
                        layui.jquery(".inlineHidden")[0].innerHTML =
                            `
                                <label class="layui-form-label col2_left"><span class="red">* </span>状态</label>
                                <div class="layui-input-inline col2_right">
                                    <select name="opStatus" lay-verify="required" lay-filter="required">
                                        <option value="">请选择状态</option>
                                        <option value="5">关注</option>
                                        <option value="6">取关</option>
                                    </select>
                                </div>
                            `
                    } else {
                        layui.jquery(".layNoRequired").css("display", "none")
                        layui.jquery(".layNoRequired")[0].innerHTML = "";
                        layui.jquery(".layRequired").css("display", "block")
                        layui.jquery(".layRequired")[0].innerHTML =
                            `<label class="layui-form-label"><span class="red">* </span>仓位(%)</label>
                        <div class="layui-input-inline">
                            <input type="text" name="position" placeholder="请输入仓位（%）" autocomplete="off"
                                lay-verify="required" class="layui-input">
                        </div>`
                        layui.jquery(".hidden").css("display", "block")
                        switch (res.result.opStatus) {
                            case 1:
                                layui.jquery(".hidden")[0].innerHTML =
                                    `
                                        <div class="layui-form-item">
                                            <div class="layui-inline">
                                                <label class="layui-form-label"><span class="red required_tips">* </span>买入区间</label>
                                                <div class="layui-input-inline" style="width: 120px;">
                                                    <input type="text" name="buyMinAmount" lay-verify="required" placeholder="￥"
                                                        autocomplete="off" class="layui-input">
                                                </div>
                                                <div class="layui-form-mid">-</div>
                                                <div class="layui-input-inline" style="width: 120px;">
                                                    <input type="text" name="buyMaxAmount" lay-verify="required" placeholder="￥"
                                                        autocomplete="off" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline">
                                                <label class="layui-form-label"><span class="red">* </span>目标区间</label>
                                                <div class="layui-input-inline" style="width: 120px;">
                                                    <input type="text" name="sellMinAmount" lay-verify="required" placeholder="￥"
                                                        autocomplete="off" class="layui-input">
                                                </div>
                                                <div class="layui-form-mid">-</div>
                                                <div class="layui-input-inline" style="width: 120px;">
                                                    <input type="text" name="sellMaxAmount" lay-verify="required" placeholder="￥"
                                                        autocomplete="off" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline">
                                                <label class="layui-form-label"><span class="red required_tips">* </span>止损区间</label>
                                                <div class="layui-input-inline" style="width: 120px;">
                                                    <input type="text" name="stopLossPrice" placeholder="￥" autocomplete="off" lay-verify="required"
                                                        class="layui-input">
                                                </div>
                                                <div class="layui-form-mid">-</div>
                                                <div class="layui-input-inline" style="width: 120px;">
                                                    <input type="text" name="stopLossMaxPrice" placeholder="￥" autocomplete="off" lay-verify="required"
                                                        class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                    `
                                break
                            case 2:
                            case 3:
                            case 4:
                                console.log(res.result.opStatus)
                                layui.jquery(".hidden")[0].innerHTML =
                                    `
                                        <div class="layui-form-item">
                                            <div class="layui-inline">
                                                <label class="layui-form-label"><span class="red">* </span>目标区间</label>
                                                <div class="layui-input-inline" style="width: 120px;">
                                                    <input type="text" name="sellMinAmount" lay-verify="required" placeholder="￥"
                                                        autocomplete="off" class="layui-input">
                                                </div>
                                                <div class="layui-form-mid">-</div>
                                                <div class="layui-input-inline" style="width: 120px;">
                                                    <input type="text" name="sellMaxAmount" lay-verify="required" placeholder="￥"
                                                        autocomplete="off" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                    `
                                break
                        }
                        layui.jquery(".inlineBox").css("display", "inline-block")
                        layui.jquery(".inlineBox")[0].innerHTML =
                            `
                            <label class="layui-form-label col2_left"><span class="red">* </span>状态</label>
                            <div class="layui-input-inline col2_right">
                                <select name="opStatus" lay-verify="required" lay-filter="opStatus">
                                    <option value="">请选择状态</option>
                                    <option value="1">买入</option>
                                    <option value="2">止盈</option>
                                    <option value="3">止损</option>
                                    <option value="4">卖出</option>
                                </select>
                            </div>
                        `
                        layui.jquery(".inlineHidden").css("display", "none")
                        layui.jquery(".inlineHidden")[0].innerHTML = ""
                    }
                    layui.form.render("select");
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=title]').val(res.result.title);
                    layui.$('select[name=type]').val(res.result.type);
                    layui.$('select[name=opStatus]').val(res.result.opStatus);
                    layui.$('input[name=stockCode]').val(res.result.stockCode);
                    layui.$('input[name=stockName]').val(res.result.stockName);
                    layui.$('input[name=buyMinAmount]').val(res.result.buyMinAmount == -1 ? (res.result.type == 2 ? '--' : '') : res.result.buyMinAmount);
                    layui.$('input[name=buyMaxAmount]').val(res.result.buyMaxAmount == -1 ? (res.result.type == 2 ? '--' : '') : res.result.buyMaxAmount);
                    layui.$('input[name=sellMinAmount]').val(res.result.sellMinAmount == -1 ? (res.result.type == 2 ? '--' : '') : res.result.sellMinAmount);
                    layui.$('input[name=sellMaxAmount]').val(res.result.buyMinAmount == -1 ? (res.result.type == 2 ? '--' : '') : res.result.sellMaxAmount);
                    layui.$('input[name=stopLossPrice]').val(res.result.stopLossPrice == -1 ? '' : res.result.stopLossPrice);
                    layui.$('input[name=stopLossMaxPrice]').val(res.result.stopLossMaxPrice == -1 ? '' : res.result.stopLossMaxPrice);
                    layui.$('input[name=position]').val(res.result.position == -1 ? '' : res.result.position);
                    layui.$('select[name=capitalLimit]').val(res.result.capitalLimit);
                    layui.$('input[name=releaseAt]').val(layui.common.timeFormat(res.result.releaseAt).split(' ')[0]);
                    if (res.result.buyDate != null) {
                        layui.$('input[name=buyDate]').val(layui.common.timeFormat(res.result.buyDate).split(' ')[0]);
                    }
                    if (res.result.sellDate != null) {
                        layui.$('input[name=sellDate]').val(layui.common.timeFormat(res.result.sellDate).split(' ')[0]);
                    }
                    if (res.result.incomePercentage != 0) {
                        layui.$('input[name=incomePercentage]').val(res.result.incomePercentage);
                    }
                    layui.$('#content').val(res.result.content);
                    layui.$('#referenceReport').val(res.result.referenceReport);

                    if (res.result.opStatus == 1) {
                        layui.$('.required_tips').removeClass('layui-hide');
                        layui.$('input[name=buyMinAmount]').attr('lay-verify', 'required');
                        layui.$('input[name=buyMaxAmount]').attr('lay-verify', 'required');
                    }
                    else {
                        layui.$('.required_tips').addClass('layui-hide');
                        layui.$('input[name=buyMinAmount]').removeAttr('lay-verify', 'required');
                        layui.$('input[name=buyMaxAmount]').removeAttr('lay-verify', 'required');
                    }
                    layui.form.render();

                    layui.recommendStock.initTinymce('#content');
                    layui.recommendStock.initTinymce('#referenceReport');
                    layui.recommendStock.initSubsidiary(res.result.subsidiaryId);
                    layui.recommendStock.initInvestmentadvisor(res.result.investmentAdvisorId);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 通过id获取单个产品信息
        * */
        getForPreview: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/media/recommend-stock/preview/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('#releaseAt').text(layui.common.timeFormat(res.result.releaseAt).split(' ')[0]);
                    layui.$('#stockName').text(res.result.stockName);
                    if (res.result.buyMinAmount == -1 && res.result.buyMaxAmount == -1) {
                        layui.$('#buyAmount').text('--');
                    }
                    else if (res.result.buyMinAmount != -1 && res.result.buyMaxAmount != -1) {
                        layui.$('#buyAmount').text(res.result.buyMinAmount + '-' + res.result.buyMaxAmount);
                    }
                    else {
                        layui.$('#buyAmount').text(res.result.buyMinAmount > -1 ? res.result.buyMinAmount : res.result.buyMaxAmount);
                    }

                    if (res.result.sellMinAmount == -1 && res.result.sellMaxAmount == -1) {
                        layui.$('#sellAmount').text('--');
                    }
                    else if (res.result.sellMinAmount != -1 && res.result.sellMaxAmount != -1) {
                        layui.$('#sellAmount').text(res.result.sellMinAmount + '-' + res.result.sellMaxAmount);
                    }
                    else {
                        layui.$('#sellAmount').text(res.result.sellMinAmount > -1 ? res.result.sellMinAmount : res.result.sellMaxAmount);
                    }

                    if (res.result.stopLossPrice == -1 && res.result.stopLossMaxPrice == -1) {
                        layui.$('#stopLossPrice').text('--');
                    }
                    else if (res.result.stopLossPrice != -1 && res.result.stopLossMaxPrice != -1) {
                        layui.$('#stopLossPrice').text(res.result.stopLossPrice + '-' + res.result.stopLossMaxPrice);
                    }
                    else {
                        layui.$('#stopLossPrice').text(res.result.stopLossPrice > -1 ? res.result.stopLossPrice : res.result.stopLossMaxPrice);
                    }
                    layui.$('#position').text(res.result.position == -1 ? '--' : res.result.position);
                    layui.$('#investmentAdvisor').text(res.result.investmentAdvisorName + '[' + res.result.investmentAdvisorCertNo + ']');
                    if (res.result.opStatus == 1) {
                        layui.$('#opStatus').text('买入');
                    }
                    else if (res.result.opStatus == 2) {
                        layui.$('#opStatus').text('止盈');
                    }
                    else if (res.result.opStatus == 3) {
                        layui.$('#opStatus').text('止损');
                    }
                    else if (res.result.opStatus == 4) {
                        layui.$('#opStatus').text('卖出');
                    }
                    else if (res.result.opStatus == 5) {
                        layui.$('#opStatus').text('关注');
                    }
                    else if (res.result.opStatus == 6) {
                        layui.$('#opStatus').text('取关');
                    }
                    layui.$('#top_title').text('推荐理由');
                    layui.$('#referenceReportDom').show();
                    layui.$('#title').text(res.result.stockName + '（' + res.result.stockCode + '）');
                    layui.$('#content').html(res.result.content);
                    if (res.result.referenceReport != '') {
                        layui.$('#referenceReport').html(res.result.referenceReport);
                    }

                    layui.recommendStock.getRecordQuery(id);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
* 通过id获取单个产品信息
* */
        newGetForPreview: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/for-order/recommend-stock/preview/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('#releaseAt').text(layui.common.timeFormat(res.result.releaseAt).split(' ')[0]);
                    layui.$('#stockName').text(res.result.stockName);
                    if (res.result.buyMinAmount == -1 && res.result.buyMaxAmount == -1) {
                        layui.$('#buyAmount').text('--');
                    }
                    else if (res.result.buyMinAmount != -1 && res.result.buyMaxAmount != -1) {
                        layui.$('#buyAmount').text(res.result.buyMinAmount + '-' + res.result.buyMaxAmount);
                    }
                    else {
                        layui.$('#buyAmount').text(res.result.buyMinAmount > -1 ? res.result.buyMinAmount : res.result.buyMaxAmount);
                    }

                    if (res.result.sellMinAmount == -1 && res.result.sellMaxAmount == -1) {
                        layui.$('#sellAmount').text('--');
                    }
                    else if (res.result.sellMinAmount != -1 && res.result.sellMaxAmount != -1) {
                        layui.$('#sellAmount').text(res.result.sellMinAmount + '-' + res.result.sellMaxAmount);
                    }
                    else {
                        layui.$('#sellAmount').text(res.result.sellMinAmount > -1 ? res.result.sellMinAmount : res.result.sellMaxAmount);
                    }

                    if (res.result.stopLossPrice == -1 && res.result.stopLossMaxPrice == -1) {
                        layui.$('#stopLossPrice').text('--');
                    }
                    else if (res.result.stopLossPrice != -1 && res.result.stopLossMaxPrice != -1) {
                        layui.$('#stopLossPrice').text(res.result.stopLossPrice + '-' + res.result.stopLossMaxPrice);
                    }
                    else {
                        layui.$('#stopLossPrice').text(res.result.stopLossPrice > -1 ? res.result.stopLossPrice : res.result.stopLossMaxPrice);
                    }
                    layui.$('#position').text(res.result.position == -1 ? '--' : res.result.position);
                    layui.$('#investmentAdvisor').text(res.result.investmentAdvisorName + '[' + res.result.investmentAdvisorCertNo + ']');
                    if (res.result.opStatus == 1) {
                        layui.$('#opStatus').text('买入');
                    }
                    else if (res.result.opStatus == 2) {
                        layui.$('#opStatus').text('止盈');
                    }
                    else if (res.result.opStatus == 3) {
                        layui.$('#opStatus').text('止损');
                    }
                    else if (res.result.opStatus == 4) {
                        layui.$('#opStatus').text('卖出');
                    }
                    else if (res.result.opStatus == 5) {
                        layui.$('#opStatus').text('关注');
                    }
                    else if (res.result.opStatus == 6) {
                        layui.$('#opStatus').text('取关');
                    }
                    layui.$('#top_title').text('推荐理由');
                    layui.$('#referenceReportDom').show();
                    layui.$('#title').text(res.result.stockName + '（' + res.result.stockCode + '）');
                    layui.$('#content').html(res.result.content);
                    if (res.result.referenceReport != '') {
                        layui.$('#referenceReport').html(res.result.referenceReport);
                    }

                    layui.recommendStock.newGetRecordQuery(id);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 通过id获取单个产品信息
        * */
        getForAudit: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/media/recommend-stock/preview/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('#subsidiaryName').text(res.result.subsidiaryName);
                    layui.$('#title').text(res.result.title);
                    layui.$('#stockInfo').text(res.result.stockCode + '（' + res.result.stockName + '）');
                    if (res.result.buyMinAmount == -1 && res.result.buyMaxAmount == -1) {
                        layui.$('#buyAmount').text('--');
                    }
                    else if (res.result.buyMinAmount != -1 && res.result.buyMaxAmount != -1) {
                        layui.$('#buyAmount').text(res.result.buyMinAmount + '-' + res.result.buyMaxAmount + '元');
                    }
                    else {
                        layui.$('#buyAmount').text((res.result.buyMinAmount > -1 ? res.result.buyMinAmount : res.result.buyMaxAmount) + '元');
                    }

                    if (res.result.sellMinAmount == -1 && res.result.sellMaxAmount == -1) {
                        layui.$('#sellAmount').text('--');
                    }
                    else if (res.result.sellMinAmount != -1 && res.result.sellMaxAmount != -1) {
                        layui.$('#sellAmount').text(res.result.sellMinAmount + '-' + res.result.sellMaxAmount + '元');
                    }
                    else {
                        layui.$('#sellAmount').text((res.result.sellMinAmount > -1 ? res.result.sellMinAmount : res.result.sellMaxAmount) + '元');
                    }

                    if (res.result.stopLossPrice == -1 && res.result.stopLossMaxPrice == -1) {
                        layui.$('#stopLossPrice').text('--');
                    }
                    else if (res.result.stopLossPrice != -1 && res.result.stopLossMaxPrice != -1) {
                        layui.$('#stopLossPrice').text(res.result.stopLossPrice + '-' + res.result.stopLossMaxPrice + '元');
                    }
                    else {
                        layui.$('#stopLossPrice').text((res.result.stopLossPrice > -1 ? res.result.stopLossPrice : res.result.stopLossMaxPrice) + '元');
                    }
                    layui.$('#position').text(res.result.position == -1 ? '--' : res.result.position + '%');
                    layui.$('#investmentAdvisor').text(res.result.investmentAdvisorName + ' [' + res.result.investmentAdvisorCertNo + ']');
                    layui.$('#releaseAt').text(layui.common.timeFormat(res.result.releaseAt).split(' ')[0]);
                    if (res.result.type == 1) {
                        layui.$('#type').text('产品策略');
                    }
                    else if (res.result.type == 2) {
                        layui.$('#type').text('福利资料');
                    }
                    else if (res.result.type == 3) {
                        layui.$('#type').text('服务资料');
                    }
                    if (res.result.opStatus == 1) {
                        layui.$('#opStatus').text('买入');
                    }
                    else if (res.result.opStatus == 2) {
                        layui.$('#opStatus').text('止盈');
                    }
                    else if (res.result.opStatus == 3) {
                        layui.$('#opStatus').text('止损');
                    }
                    else if (res.result.opStatus == 4) {
                        layui.$('#opStatus').text('卖出');
                    }
                    else if (res.result.opStatus == 5) {
                        layui.$('#opStatus').text('关注');
                    }
                    else if (res.result.opStatus == 6) {
                        layui.$('#opStatus').text('取关');
                    }
                    if (res.result.capitalLimit > 0) {
                        layui.$('#capitalLimit').text('vip' + res.result.capitalLimit + '以上');
                    }
                    if (res.result.buyDate != null) {
                        layui.$('#buyDate').text(layui.common.timeFormat(res.result.buyDate).split(' ')[0]);
                    }
                    if (res.result.sellDate != null) {
                        layui.$('#sellDate').text(layui.common.timeFormat(res.result.sellDate).split(' ')[0]);
                    }
                    layui.$('#incomePercentage').text(res.result.incomePercentage + '%');
                    layui.$('#content').html(res.result.content);
                    layui.$('#referenceReport').html(res.result.referenceReport);
                    layui.recommendStock.getRecordQuery(id);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 通过id获取审核记录
        * @param {*} page
        * @returns
        */
        getRecordQuery: function (id) {
            layui.tableRequest.request('resource', false, 'logTable', '/admin/media/recommend-stock/audit-record/query?id=' + id, 'application/json', [
                { field: 'title', title: '标题', width: 200 },
                { field: 'auditRemark', title: '审核意见', width: 296 },
                { field: 'auditUsername', title: '审核人', width: 100 },
                {
                    field: 'auditTime', title: '审核时间', width: 110, templet: function (e) {
                        if (e.auditTime != null) {
                            return layui.common.timeFormat(e.auditTime).split(' ')[0];
                        }
                        else {
                            return '-';
                        }
                    }
                }
            ], {}, '', 'auto', null, function (resCallback, curr, count) {
                if (resCallback.data.length > 0) {
                    layui.$('.log-item').show();
                }
            });
        },
        /**
             * 通过id获取审核记录
             * @param {*} page
             * @returns
             */
        newGetRecordQuery: function (id) {
            layui.tableRequest.request('resource', false, 'logTable', '/admin/for-order/recommend-stock/audit-record/query?id=' + id, 'application/json', [
                { field: 'title', title: '标题', width: 200 },
                { field: 'auditRemark', title: '审核意见', width: 296 },
                { field: 'auditUsername', title: '审核人', width: 100 },
                {
                    field: 'auditTime', title: '审核时间', width: 110, templet: function (e) {
                        if (e.auditTime != null) {
                            return layui.common.timeFormat(e.auditTime).split(' ')[0];
                        }
                        else {
                            return '-';
                        }
                    }
                }
            ], {}, '', 'auto', null, function (resCallback, curr, count) {
                if (resCallback.data.length > 0) {
                    layui.$('.log-item').show();
                }
            });
        },

        /**
         * 查询合规审核荐股列表
         * */
        query: function () {
            let page = { curr: 1, limit: 30 }
            layui.tableRequest.request('resource', true, 'recommend-stock-list', '/admin/media/recommend-stock/query', 'application/json', [
                { field: 'subsidiaryName', title: '所属组织' },
                {
                    title: '类型', width: 90, templet: function (e) {
                        if (e.type == 1) {
                            return '产品策略';
                        }
                        else if (e.type == 2) {
                            return '福利资料';
                        }
                        else if (e.type == 3) {
                            return '服务资料';
                        }
                        return '-';
                    }
                },
                { field: 'title', title: '标题' },
                {
                    title: '股票', templet: function (e) {
                        return e.stockCode + '[' + e.stockName + ']'
                    }
                },
                {
                    title: '买入区间', templet: function (e) {
                        if (e.buyMinAmount == -1 && e.buyMaxAmount == -1) {
                            return '--';
                        }
                        else if (e.buyMinAmount != -1 && e.buyMaxAmount != -1) {
                            return e.buyMinAmount + '-' + e.buyMaxAmount + '元';
                        }
                        else {
                            return (e.buyMinAmount > -1 ? e.buyMinAmount : e.buyMaxAmount) + '元';
                        }
                    }
                },
                {
                    title: '目标区间', templet: function (e) {
                        if (e.sellMinAmount == -1 && e.sellMaxAmount == -1) {
                            return '--';
                        }
                        else if (e.sellMinAmount != -1 && e.sellMaxAmount != -1) {
                            return e.sellMinAmount + '-' + e.sellMaxAmount + '元';
                        }
                        else {
                            return (e.sellMinAmount > -1 ? e.sellMinAmount : e.sellMaxAmount) + '元';
                        }
                    }
                },
                {
                    title: '止损区间', templet: function (e) {
                        if (e.stopLossPrice == -1 && e.stopLossMaxPrice == -1) {
                            return '--';
                        }
                        else if (e.stopLossPrice != -1 && e.stopLossMaxPrice != -1) {
                            return e.stopLossPrice + '-' + e.stopLossMaxPrice + '元';
                        }
                        else {
                            return (e.stopLossPrice > -1 ? e.stopLossPrice : e.stopLossMaxPrice) + '元';
                        }
                    }
                },
                {
                    title: '仓位', width: 80, templet: function (e) {
                        return e.position == -1 ? '--' : e.position + '%'
                    }
                },
                { field: 'investmentAdvisorName', width: 90, title: '投资顾问' },
                {
                    title: '审核状态', width: 90, templet: "#tableStatus"
                },
                {
                    title: '审核人', width: 90, templet: function (e) {
                        var result = '-';
                        if (e.auditUsername != null && e.auditUsername != '') {
                            result = e.auditUsername;
                        }
                        return result;
                    }
                },
                {
                    field: 'releaseAt', title: '发布时间', width: 120, templet: function (e) {
                        if (e.releaseAt != null) {
                            return layui.common.timeFormat(e.releaseAt).split(' ')[0];
                        }
                        else {
                            return '-';
                        }
                    }
                },
                { fixed: 'right', title: '操作', width: 120, align: 'left', toolbar: '#recommend-stock-bar' }
            ], { companyId: layui.setter.companyId });
            //监听表格事件
            layui.recommendStock.tableEvent();
        },
        /**
      * 查询合规审核荐股列表新
      * */
        newQuery: function () {
            let page = { curr: 1, limit: 30 }
            layui.tableRequest.request('resource', true, 'recommend-stock-list', '/admin/for-order/recommend-stock/query', 'application/json', [
                { field: 'subsidiaryName', title: '所属组织' },

                { field: 'title', title: '标题' },
                {
                    title: '股票', templet: function (e) {
                        return e.stockCode + '[' + e.stockName + ']'
                    }
                },
                {
                    title: '买入区间', templet: function (e) {
                        if (e.buyMinAmount == -1 && e.buyMaxAmount == -1) {
                            return '--';
                        }
                        else if (e.buyMinAmount != -1 && e.buyMaxAmount != -1) {
                            return e.buyMinAmount + '-' + e.buyMaxAmount + '元';
                        }
                        else {
                            return (e.buyMinAmount > -1 ? e.buyMinAmount : e.buyMaxAmount) + '元';
                        }
                    }
                },
                {
                    title: '目标区间', templet: function (e) {
                        if (e.sellMinAmount == -1 && e.sellMaxAmount == -1) {
                            return '--';
                        }
                        else if (e.sellMinAmount != -1 && e.sellMaxAmount != -1) {
                            return e.sellMinAmount + '-' + e.sellMaxAmount + '元';
                        }
                        else {
                            return (e.sellMinAmount > -1 ? e.sellMinAmount : e.sellMaxAmount) + '元';
                        }
                    }
                },
                {
                    title: '止损区间', templet: function (e) {
                        if (e.stopLossPrice == -1 && e.stopLossMaxPrice == -1) {
                            return '--';
                        }
                        else if (e.stopLossPrice != -1 && e.stopLossMaxPrice != -1) {
                            return e.stopLossPrice + '-' + e.stopLossMaxPrice + '元';
                        }
                        else {
                            return (e.stopLossPrice > -1 ? e.stopLossPrice : e.stopLossMaxPrice) + '元';
                        }
                    }
                },
                {
                    title: '仓位', width: 80, templet: function (e) {
                        return e.position == -1 ? '--' : e.position + '%'
                    }
                },
                { field: 'investmentAdvisorName', width: 90, title: '投资顾问' },
                {
                    title: '审核人', width: 90, templet: function (e) {
                        var result = '-';
                        if (e.auditUsername != null && e.auditUsername != '') {
                            result = e.auditUsername;
                        }
                        return result;
                    }
                },
                {
                    field: 'releaseAt', title: '发布时间', width: 120, templet: function (e) {
                        if (e.releaseAt != null) {
                            return layui.common.timeFormat(e.releaseAt).split(' ')[0];
                        }
                        else {
                            return '-';
                        }
                    }
                },
                { fixed: 'right', title: '操作', width: 120, align: 'center', toolbar: '#recommend-stock-bar' }
            ], { companyId: layui.setter.companyId });
            //监听表格事件
            layui.recommendStock.tableEvent();
        },
        /**
         * 通过用户id获取荐股列表
         * */
        queryByUserId: function () {
            layui.tableRequest.request('resource', true, 'recommend-stock-list', '/admin/media/recommend-stock/by-userid/query', 'application/json', [
                { field: 'subsidiaryName', title: '组织' },
                {
                    title: '类型', width: 90, templet: function (e) {
                        if (e.type == 1) {
                            return '产品策略';
                        }
                        else if (e.type == 2) {
                            return '福利资料';
                        }
                        else if (e.type == 3) {
                            return '服务资料';
                        }
                        return '-';
                    }
                },
                { field: 'title', title: '标题' },
                {
                    title: '股票', templet: function (e) {
                        return e.stockCode + '[' + e.stockName + ']'
                    }
                },
                {
                    title: '买入区间', templet: function (e) {
                        if (e.buyMinAmount == -1 && e.buyMaxAmount == -1) {
                            return '--';
                        }
                        else if (e.buyMinAmount != -1 && e.buyMaxAmount != -1) {
                            return e.buyMinAmount + '-' + e.buyMaxAmount + '元';
                        }
                        else {
                            return (e.buyMinAmount > -1 ? e.buyMinAmount : e.buyMaxAmount) + '元';
                        }
                    }
                },
                {
                    title: '目标区间', templet: function (e) {
                        if (e.sellMinAmount == -1 && e.sellMaxAmount == -1) {
                            return '--';
                        }
                        else if (e.sellMinAmount != -1 && e.sellMaxAmount != -1) {
                            return e.sellMinAmount + '-' + e.sellMaxAmount + '元';
                        }
                        else {
                            return (e.sellMinAmount > -1 ? e.sellMinAmount : e.sellMaxAmount) + '元';
                        }
                    }
                },
                {
                    title: '止损区间', templet: function (e) {
                        if (e.stopLossPrice == -1 && e.stopLossMaxPrice == -1) {
                            return '--';
                        }
                        else if (e.stopLossPrice != -1 && e.stopLossMaxPrice != -1) {
                            return e.stopLossPrice + '-' + e.stopLossMaxPrice + '元';
                        }
                        else {
                            return (e.stopLossPrice > -1 ? e.stopLossPrice : e.stopLossMaxPrice) + '元';
                        }
                    }
                },
                {
                    title: '仓位', width: 80, templet: function (e) {
                        return e.position == -1 ? '--' : e.position + '%'
                    }
                },
                { field: 'investmentAdvisorName', width: 90, title: '投资顾问' },
                {
                    title: '合规审核', width: 90, templet: function (e) {
                        var result = '-';
                        if (e.hgAuditStatus == 1) {
                            result = '待送审';
                        }
                        else if (e.hgAuditStatus == 2) {
                            result = '待审核';
                        }
                        else if (e.hgAuditStatus == 3) {
                            result = '完成';
                        }
                        else if (e.hgAuditStatus == 4) {
                            result = '驳回';
                        }
                        if (e.hgAuditStatus != 1 && e.hgAuditStatus != 2 && e.hgAuditRemark != null && e.hgAuditRemark != '') {
                            result += '（' + e.hgAuditRemark + '）';
                        }
                        return result;
                    }
                },
                {
                    field: 'releaseAt', title: '发布时间', width: 120, templet: function (e) {
                        if (e.releaseAt != null) {
                            return layui.common.timeFormat(e.releaseAt).split(' ')[0];
                        }
                        else {
                            return '-';
                        }
                    }
                },
                { fixed: 'right', title: '操作', width: 170, align: 'left', toolbar: '#recommend-stock-bar' }
            ], { companyId: layui.setter.companyId });
            //监听表格事件
            layui.recommendStock.tableEvent();
        },
        /**
        * 创建荐股记录
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/media/recommend-stock/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("操作成功");
                    setTimeout(function () { location.href = 'create.html'; }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑荐股记录
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/media/recommend-stock/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("操作成功");
                    setTimeout(function () { location.href = 'list-ss.html'; }, 2000)
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 客服编辑送审状态
        * @param {any} id
        * @param {any} status
        */
        updateStatusForKf: function (id, status) {
            var data = { id: id, hgAuditStatus: status }
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/media/recommend-stock/status-kf/update',
                data: JSON.stringify(data),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("操作成功");
                    layui.tableRequest.reload('recommend-stock-list');
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 合规编辑审核状态
        * @param {any} id
        * @param {any} status
        */
        updateStatusForHg: function (data) {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            data.field.id = id;
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/media/recommend-stock/status-hg/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("操作成功");
                    setTimeout(function () { location.href = 'list-audit.html'; }, 2000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 风控编辑审核状态
        * @param {any} id
        * @param {any} status
        */
        updateStatusForFk: function (data) {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            data.field.id = id;
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/media/recommend-stock/status-fk/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("操作成功");
                    setTimeout(function () { location.href = 'list-audit.html'; }, 2000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除单个荐股记录
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/media/recommend-stock/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("recommend-stock-list");
                    layui.common.alertAutoClose("删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 导出荐股记录
        * @param {any} data
        */
        export: function (data) {
            var confirmIndex = layui.layer.confirm('确定导出当前荐股记录吗？', {
                icon: 3,
                title: '提示',
                btn: ['确定', '取消']
            }, function () {
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/media/recommend-stock/export',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.layer.close(confirmIndex);
                    if (res.isSuccess) {
                        location.href = res.result;
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            }, function () {
                layui.layer.close(confirmIndex);
            });
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            let $ = layui.jquery;
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    location.href = 'update.html?id=' + data.id;
                }
                else if (obj.event === 'read') {
                    location.href = 'read.html?id=' + data.id;
                }
                else if (obj.event === 'audit') {
                    let pageSize = $(".layui-laypage-limits").find("option:selected").val() //分页数目
                    let pageIndex = $(".layui-laypage-skip").find("input").val() //当前页码值
                    console.log(pageSize, pageIndex) //当前页码值
                    location.href = 'audit.html?type=1&id=' + data.id;
                }
                else if (obj.event === 'fk-audit') {
                    location.href = 'audit.html?type=2&id=' + data.id;
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该记录吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.recommendStock.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'submit') {
                    var confirmIndex = layui.layer.confirm('确定送审该记录吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.recommendStock.updateStatusForKf(data.id, 2);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'cancel') {
                    var confirmIndex = layui.layer.confirm('确定撤回该记录吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.recommendStock.updateStatusForKf(data.id, 1);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        },
        /**
         * 绑定事件
         */
        bindEvent: function () {
            //切换类型
            layui.form.on('select(type)', function (data) {
                if (data.value == 1) {
                    layui.$('input[name=buyMinAmount]').val('');
                    layui.$('input[name=buyMaxAmount]').val('');
                    layui.$('input[name=sellMinAmount]').val('');
                    layui.$('input[name=sellMaxAmount]').val('');
                    layui.$('input[name=stopLossPrice]').val('');
                    layui.$('input[name=stopLossMaxPrice]').val('');
                    layui.$('input[name=buyMinAmount]').removeAttr('disabled');
                    layui.$('input[name=buyMaxAmount]').removeAttr('disabled');
                    layui.$('input[name=sellMinAmount]').removeAttr('disabled');
                    layui.$('input[name=sellMaxAmount]').removeAttr('disabled');
                    layui.$('input[name=stopLossPrice]').removeAttr('disabled');
                    layui.$('input[name=stopLossMaxPrice]').removeAttr('disabled');
                }
                else {
                    layui.$('input[name=buyMinAmount]').val('--');
                    layui.$('input[name=buyMaxAmount]').val('--');
                    layui.$('input[name=sellMinAmount]').val('--');
                    layui.$('input[name=sellMaxAmount]').val('--');
                    layui.$('input[name=stopLossPrice]').val('--');
                    layui.$('input[name=stopLossMaxPrice]').val('--');
                    layui.$('input[name=buyMinAmount]').attr('disabled', 'disabled');
                    layui.$('input[name=buyMaxAmount]').attr('disabled', 'disabled');
                    layui.$('input[name=sellMinAmount]').attr('disabled', 'disabled');
                    layui.$('input[name=sellMaxAmount]').attr('disabled', 'disabled');
                    layui.$('input[name=stopLossPrice]').attr('disabled', 'disabled');
                    layui.$('input[name=stopLossMaxPrice]').attr('disabled', 'disabled');
                }
            });
            //切换状态
            layui.form.on('select(opStatus)', function (data) {
                if (data.value == 1) {
                    layui.$('input[name=buyMinAmount]').attr('lay-verify', 'required');
                    layui.$('input[name=buyMaxAmount]').attr('lay-verify', 'required');
                }
                else {
                    layui.$('input[name=buyMinAmount]').removeAttr('lay-verify', 'required');
                    layui.$('input[name=buyMaxAmount]').removeAttr('lay-verify', 'required');
                }
                layui.form.render();
            });
            var regex = /^(\d+)?(\.\d{1,2})?$/;
            //买入区间
            layui.$('input[name=buyMinAmount]').blur(function () {
                var val = layui.$(this).val();
                if (val.indexOf('?') <= -1 && val.indexOf('？') <= -1 && val != '') {
                    if (!regex.test(val)) {
                        layui.common.alertAutoClose("输入金额格式有误");
                        layui.$(this).val('');
                    }
                    //如果最大买入区间不为空，计算输入数值是否在允许值内
                    var buyMaxAmount = layui.$('input[name=buyMaxAmount]').val();
                    if (regex.test(buyMaxAmount)) {
                        var min = parseFloat(buyMaxAmount) - parseFloat(buyMaxAmount) * layui.setter.stockAmountRangePercentage;
                        if (parseFloat(val) < min) {
                            layui.common.alertAutoClose("区间金额大小不可超过" + (layui.setter.stockAmountRangePercentage * 100) + "%");
                            layui.$(this).val('');
                        }
                    }
                }
            });
            layui.$('input[name=buyMaxAmount]').blur(function () {
                var val = layui.$(this).val();
                if (val.indexOf('?') <= -1 && val.indexOf('？') <= -1 && val != '') {
                    if (!regex.test(val)) {
                        layui.common.alertAutoClose("输入金额格式有误");
                        layui.$(this).val('');
                    }
                    //如果最大买入区间不为空，计算输入数值是否在允许值内
                    var buyMinAmount = layui.$('input[name=buyMinAmount]').val();
                    if (regex.test(buyMinAmount)) {
                        if (parseFloat(buyMinAmount) > parseFloat(val)) {
                            layui.common.alertAutoClose("最大金额不可低于最小金额");
                            layui.$(this).val('');
                        }
                        var max = parseFloat(buyMinAmount) + parseFloat(buyMinAmount) * layui.setter.stockAmountRangePercentage;
                        if (parseFloat(val) > max) {
                            layui.common.alertAutoClose("区间金额大小不可超过" + (layui.setter.stockAmountRangePercentage * 100) + "%");
                            layui.$(this).val('');
                        }
                    }
                }
            });
            //卖出区间
            layui.$('input[name=sellMinAmount]').blur(function () {
                var val = layui.$(this).val();
                if (val.indexOf('?') <= -1 && val.indexOf('？') <= -1 && val != '') {
                    if (!regex.test(val)) {
                        layui.common.alertAutoClose("输入金额格式有误");
                        layui.$(this).val('');
                    }
                    //如果最大买入区间不为空，计算输入数值是否在允许值内
                    var sellMaxAmount = layui.$('input[name=sellMaxAmount]').val();
                    if (regex.test(sellMaxAmount)) {
                        var min = parseFloat(sellMaxAmount) - parseFloat(sellMaxAmount) * layui.setter.stockAmountRangePercentage;
                        if (parseFloat(val) < min) {
                            layui.common.alertAutoClose("区间金额大小不可超过" + (layui.setter.stockAmountRangePercentage * 100) + "%");
                            layui.$(this).val('');
                        }
                    }
                }
            });
            layui.$('input[name=sellMaxAmount]').blur(function () {
                var val = layui.$(this).val();
                if (val.indexOf('?') <= -1 && val.indexOf('？') <= -1 && val != '') {
                    if (!regex.test(val)) {
                        layui.common.alertAutoClose("输入金额格式有误");
                        layui.$(this).val('');
                    }
                    //如果最大买入区间不为空，计算输入数值是否在允许值内
                    var sellMinAmount = layui.$('input[name=sellMinAmount]').val();
                    if (regex.test(sellMinAmount)) {
                        if (parseFloat(sellMinAmount) > parseFloat(val)) {
                            layui.common.alertAutoClose("最大金额不可低于最小金额");
                            layui.$(this).val('');
                        }
                        var max = parseFloat(sellMinAmount) + parseFloat(sellMinAmount) * layui.setter.stockAmountRangePercentage;
                        if (parseFloat(val) > max) {
                            layui.common.alertAutoClose("区间金额大小不可超过" + (layui.setter.stockAmountRangePercentage * 100) + "%");
                            layui.$(this).val('');
                        }
                    }
                }
            });
        }
    }

    exports('recommendStock', func);
});