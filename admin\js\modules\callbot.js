﻿layui.define(['laytpl', 'request', 'tableRequest', 'tree', 'common'], function (exports) {
    var func = {
        /**
         * 获取所有外呼任务列表
         * */
        getAll: function (callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/callbot/task/get-all',
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 获取话术模板列表
         * */
        getTemplate: function () {
            layui.request({
                method: 'post',
                url: '/admin/callbot/template/get-all',
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    res.result.unshift({ templateId: '', templateName: '请选择话术模板' });
                    var getTpl = document.getElementById("template-tpl").innerHTML
                        , view = document.getElementById('template-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.form.render('select');
                }
            })
        },
        /**
         * 获取对话消息列表
         * */
        getMessage: function () {
            var id = layui.common.getUrlParam('id');
            layui.request({
                method: 'get',
                url: '/admin/callbot/message/' + id,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    const chatMessages = document.getElementById('chat-messages');
                    chatMessages.innerHTML = '';
                    res.result.forEach(message => {
                        const messageEl = document.createElement('div');
                        messageEl.className = `message ${message.senderType === 1 ? 'bot' : 'user'}`;
                        messageEl.innerHTML = `
                            <div class="avatar">${message.senderType === 1 ? 'Bot' : '客户'}</div>
                            <div class="message-content">
                                <p class="message-text">${message.msg}</p>
                                <p class="message-time">${new Date(message.dateTime * 1000).toLocaleString()}</p>
                            </div>
                        `;
                        chatMessages.appendChild(messageEl);
                    });
                }
            })
        },
        /**
         * 获取外呼任务列表
         * */
        query: function () {
            layui.tableRequest.request('resource', false, 'task-list', '/admin/callbot/task/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'taskName', title: '任务名称' },
                { field: 'templateName', title: '话术模板名称' },
                { field: 'robotNum', title: '机器人数量' },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                }
            ]);
        },
        /**
        * 创建外呼任务
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/callbot/task/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("任务创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 2000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
    }

    exports('callbot', func);
});