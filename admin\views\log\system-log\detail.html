﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        /* .layui-form-item .layui-input-inline { width: 300px; }*/
        .layui-form-label { width: 180px; }
        .layui-code { width: 800px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-tab-title" style="height: auto">
                <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" style="margin:15px 0 15px 30px;" onclick="history.back(-1)">
                    <i class="layui-icon layui-icon-left"></i>返回日志列表
                </button>
            </div>
            <div class="layui-card-body">

                <div class="layui-form-item">
                    <label class="layui-form-label">日志ID:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="id"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">应用ID:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="appId"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">应用名称:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="appName"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标题:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="title"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">操作平台:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="platform"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">日志等级:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="level"></div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">详细消息:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid">
                            <pre class="layui-code" id="content"></pre>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">客户端IP:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="clientIP"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">创建时间:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="createdAt"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['code', 'systemLog'], function () {
            layui.systemLog.get();
        });
    </script>
</body>
</html>