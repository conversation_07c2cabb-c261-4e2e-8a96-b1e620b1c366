﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 50px; }
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-form" style="padding: 20px 30px 0 0;">
        <input name="userId" type="hidden" />
        <div class="layui-form-item">
            <label class="layui-form-label">账号</label>
            <div class="layui-input-inline">
                <input type="text" name="name" placeholder="请输入坐席账号" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">Key</label>
            <div class="layui-input-inline">
                <input type="text" name="account" placeholder="请输入账号对应的ACCESS_KEY_ID" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">Secret</label>
            <div class="layui-input-inline">
                <input type="text" name="password" placeholder="请输入账号对应的ACCESS_KEY_SECRET" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="seats-submit" value="确认修改">
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'seats'], function () {
            layui.seats.get();
            layui.form.on('submit(seats-submit)', function (data) {
                layui.seats.update(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>