﻿layui.define(['upload', 'setter', 'common'], function (exports) {
    var uploadFile = function (requestBase, domId, path, callbackFunc, chooseCallbackFunc, option) {
        var baseUrl = '';
        if (requestBase == "sso") {
            baseUrl = layui.setter.request.ssoBaseUrl;
        }
        else if (requestBase == "form") {
            baseUrl = layui.setter.request.formBaseUrl;
        }
        else if (requestBase == "log") {
            baseUrl = layui.setter.request.logBaseUrl;
        }
        else if (requestBase == "pay") {
            baseUrl = layui.setter.request.payBaseUrl;
        }
        else {
            baseUrl = layui.setter.request.resourceBaseUrl
        }
        var headers = {};
        headers[layui.setter.request.tokenName] = layui.common.getCookie(layui.setter.request.tokenName);

        var userStr = localStorage.getItem('ly-admin-user');
        if (userStr != null) {
            var userObj = JSON.parse(userStr);
            headers['uid'] = userObj.id;
        }
        var loddingIndex;
        layui.upload.render({
            headers: headers,
            acceptMime: option ? option.acceptMime : ""
            , ext: option ? option.ext : ""
            , elem: '#' + domId
            , url: baseUrl + path
            , auto: option ? option.auto : true
            , accept: 'file'
            , before: function (obj) {
                loddingIndex = layui.layer.msg('上传中', { icon: 16, time: 0 });
            }
            , choose: function (obj) {
                if (chooseCallbackFunc != null && chooseCallbackFunc != undefined) {
                    chooseCallbackFunc(obj);
                }
            }
            , done: function (res, index, upload) {
                layui.layer.close(loddingIndex);
                if (!res.isSuccess && res.code == 22000) {
                    layui.common.delCookie(layui.setter.request.tokenName);
                    var appId = layui.common.getUrlParam('appId') || '';
                    var loginUrl = '/admin/login.html';
                    if (appId != '') {
                        loginUrl += '?appId=' + appId;
                    }
                    location.href = loginUrl;
                }
                else {
                    callbackFunc(res, index, upload);
                }
            }
        });
    }
    //输出 uploadFile 接口
    exports('uploadFile', uploadFile);
});