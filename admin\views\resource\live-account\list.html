﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline {
            width: 200px;
        }
    </style>
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input"
                                placeholder="账号名称/投顾名称" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">平台</label>
                        <div class="layui-input-inline">
                            <select name="bigType" id="bigType">
                                <option value="">请选择平台</option>
                                <option value="1">抖音</option>
                                <option value="2">视频号</option>
                                <option value="3">快手号</option>
                                <option value="4">小鹅通</option>
                                <option value="5">小红书</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">账号类别</label>
                        <div class="layui-input-inline">
                            <select name="smallType" id="smallType">
                                <option value="">请选择账号类别</option>
                                <option value="1">企业员工号</option>
                                <option value="2">员工个人号授权</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="status" id="status">
                                <option value="">请选择账号状态</option>
                                <option value="1">使用</option>
                                <!-- <option value="2">撤销</option> -->
                                <option value="3">停用</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="account-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                        <button class="layui-btn layuiadmin-btn-list layui-btn-primary" lay-submit
                            lay-filter="live-account-export">
                            <i class="layui-icon layui-icon-export layuiadmin-button-btn"></i>导出
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="account-list" lay-filter="list"></table>
                <script type="text/html" id="account-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>
    <script type="text/html" id="topToolBar">
        <div class="layui-btn-container">
            <button class="layui-btn layui-btn-sm layui-btn-primary " lay-event="create"><i class="layui-icon">&#xe654;</i>添加</button>
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="import"><i class="layui-icon layui-icon-export"></i>批量导入账号</button>
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="account-update"><i class="layui-icon layui-icon-edit"></i>批量修改状态</button>
        </div>
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=0823"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'liveAccount', "table"], function () {
            layui.liveAccount.query();
            //监听查询按钮
            layui.form.on('submit(account-search)', function (data) {
                var field = data.field;
                if (field.bigType == '') {
                    field.bigType = -1;
                }
                if (field.smallType == '') {
                    field.smallType = -1;
                }
                if (field.status == '') {
                    field.status = -1;
                }
                //执行重载
                layui.tableRequest.reload("account-list", {
                    where: field
                });
            });
            //监听导出按钮
            layui.form.on('submit(live-account-export)', function (data) {
                var field = data.field;
                if (field.bigType == '') {
                    field.bigType = -1;
                }
                if (field.smallType == '') {
                    field.smallType = -1;
                }
                if (field.status == '') {
                    field.status = -1;
                }
                var confirmIndex = layui.layer.confirm('确定导出账号吗？', {
                    icon: 3,
                    title: '提示',
                    btn: ['确定', '取消']
                }, function () {
                    layui.layer.close(confirmIndex);
                    layui.liveAccount.export(data);
                }, function () {
                    layui.layer.close(confirmIndex);
                });
            });

        });
    </script>
</body>

</html>