﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 200px; }
        .red { font-size: 18px; }
        .layui-fluid { padding: 0; }
        .layui-card-header { padding: 20px 0 10px 0 !important; }
        .layui-card-body { padding: 0 }
        audio { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <input id="sort" name="sort" type="hidden" value="0" />
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="订单号/姓名/手机号/身份证号" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">购买产品</label>
                        <div class="layui-input-inline">
                            <select name="productId" id="product-view" lay-search>
                                <option value="">请选择购买产品</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline handled layui-hide">
                        <label class="layui-form-label">拨打状态</label>
                        <div class="layui-input-inline">
                            <select name="callStatus" id="callStatus">
                                <option value="">请选择拨打状态</option>
                                <option value="0">未拨打</option>
                                <option value="1">拨打中</option>
                                <option value="2">已接通</option>
                                <option value="3">无人接听</option>
                                <option value="4">关机</option>
                                <option value="5">停号</option>
                                <option value="6">空号</option>
                                <option value="7">拒接</option>
                                <option value="9">用户正忙</option>
                                <option value="10">系统拦截</option>
                                <option value="11">线路拦截</option>
                                <option value="12">超时失效</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline handled layui-hide">
                        <label class="layui-form-label">客户意向</label>
                        <div class="layui-input-inline">
                            <select name="intentionCategoryId" id="intentionCategoryId">
                                <option value="">请选择拨打结果</option>
                                <option value="0">未接通</option>
                                <option value="1">通过</option>
                                <option value="2">不通过</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">下单时间</label>
                        <div class="layui-input-inline" style="width:150px">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" autocomplete="off" />
                        </div>
                        <div class="layui-input-inline" style="width:150px">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" autocomplete="off" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list " lay-submit lay-filter="product-order-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="product-order-list" lay-filter="product-order-list"></table>
                <script type="text/html" id="product-order-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
                </script>
                <script type="text/html" id="product-order-bar2">
                    <a class="layui-btn layui-btn-xs" lay-event="chat">对话</a>
                    {{# if(d.callStatus!=1&&d.voiceUrl!=null&&d.voiceUrl!=''){}}
                    <!--<button type="button" class="layui-btn layui-btn-primary layui-btn-xs" lay-event="player">播放</button>-->
                    <!--<input type="button" value="播放" class="layui-btn layui-btn-primary layui-btn-xs" lay-event="player" />-->
                    <a class="layui-btn layui-btn-primary layui-btn-xs player" lay-event="player">播放</a>
                    {{#}}}
                </script>
            </div>
        </div>
    </div>

    <div id="preview" class="preview" style="display:none;">
    </div>
    <div id="add-call-dialog" class="layui-form" style="padding: 20px 0 0 30px; width: 450px;display:none;">
        <div class="layui-form-item">
            <input id="ids" name="ids" value="" type="hidden" />
            <input id="isSelectAll" name="isSelectAll" value="false" type="hidden" />
            <label class="layui-form-label">任务模板</label>
            <div class="layui-input-inline" style="width:300px;">
                <select name="taskId" id="task-view" lay-verify="required">
                    <option value="">请选择任务模板</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="add-call-submit" value="确认添加">
            </div>
        </div>
    </div>
    <div id="audio-dialog" style="display:none;">
        <audio id="audio_dom" style="width:100%;" autoplay="autoplay" controls>
            <source src="" type="audio/mpeg">
        </audio>
    </div>
    <script id="product-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="task-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.taskId}}">{{item.taskName}}</option>
        {{#  }); }}
    </script>
    <script type="text/html" id="topToolBar">
        <div class="layui-btn-container layui-input-inline">
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="batch-call-select">
                <i class="layui-icon layui-icon-service layuiadmin-button-btn"></i>批量拨打当前选中
            </button>
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="batch-call-all">
                <i class="layui-icon layui-icon-service layuiadmin-button-btn"></i>批量拨打所有
            </button>
        </div>
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=2.3"></script>
    <script type="text/javascript">
        var salerUserList = [];
        layui.use(['productOrder'], function () {
            layui.productOrder.initProduct();
            layui.productOrder.initCallBotTask();
            layui.productOrder.queryForFollow();
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            layui.form.on('submit(product-order-search)', function (data) {
                if (data.field.callStatus == '') {
                    data.field.callStatus = -1;
                }
                if (data.field.intentionCategoryId == '') {
                    data.field.intentionCategoryId = -1;
                }
                layui.tableRequest.reload("product-order-list", {
                    where: data.field,
                    page: {
                        curr: 1
                    }
                });
            });
            layui.table.on('tool(product-order-list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    location.href = 'detail.html?id=' + data.id;
                }
                else if (obj.event === 'chat') {
                    layui.common.openIframe('聊天对话', 600, 540, '../callbot/chat.html?id=' + data.id);
                }
                else if (obj.event == 'player') {
                    if (data.voiceUrl == null || data.voiceUrl == '') {
                        layui.common.alertAutoClose("当前无可播放的录音文件");
                        return;
                    }
                    document.getElementById('audio_dom').src = data.voiceUrl;
                    layui.common.openPage('播放录音', 500, 100, '#audio-dialog');
                }
            });
            layui.table.on('toolbar(product-order-list)', function (obj) {
                if (obj.event === 'batch-call-select') {
                    var checkedList = layui.table.checkStatus(obj.config.id);
                    if (checkedList.data.length == 0) {
                        layui.common.alertAutoClose("请选择需要操作的记录");
                        return;
                    }
                    var ids = [];
                    for (var i = 0; i < checkedList.data.length; i++) {
                        ids.push(checkedList.data[i].id);
                    }
                    layui.$('#isSelectAll').val('false');
                    layui.$('#ids').val(ids.toString());
                    layui.common.openPage('批量添加拨打数据', 500, 400, '#add-call-dialog');
                }
                else if (obj.event === 'batch-call-all') {
                    layui.$('#isSelectAll').val('true');
                    layui.$('#ids').val('');
                    layui.common.openPage('批量添加拨打数据', 500, 400, '#add-call-dialog');
                }
            });

            //监听提交事件
            layui.form.on('submit(add-call-submit)', function (data) {
                data.field.keywords = layui.$('input[name=keywords]').val();
                data.field.startTime = layui.$('input[name=startTime]').val();
                data.field.endTime = layui.$('input[name=endTime]').val();
                data.field.productId = layui.$('select[name=productId]').val();
                if (data.field.ids != '') {
                    data.field.ids = data.field.ids.split(',');
                }
                else {
                    data.field.ids = [];
                }
                layui.productOrder.addCallRecord(data);
                return false; //阻止表单跳转
            });
            if (!layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.copy')) {
                layui.$(document).bind("contextmenu", function () { return false; });
                layui.$(document).bind("selectstart", function () { return false; });
            }
        });
    </script>
</body>
</html>