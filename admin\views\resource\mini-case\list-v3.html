﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 250px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属企微</label>
                        <div class="layui-input-inline">
                            <select name="workWxAppId" id="workwx-app-view">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="txb_kwd" name="keywords" type="text" class="layui-input" placeholder="方案名称" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="mini-case-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list mini-case-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="mini-case-list" lay-filter="list"></table>
                <script type="text/html" id="mini-case-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="edit-v3">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del-v3">删除</a>
                </script>
            </div>
        </div>
    </div>

    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'miniCase'], function () {
            layui.miniCase.initApp();
            layui.miniCase.queryV3();
            //监听查询按钮
            layui.form.on('submit(mini-case-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("mini-case-list", {
                    where: field
                });
            });
            //打开添加弹框
            layui.$('.mini-case-create').click(function () {
                layui.common.openIframe('创建小程序加粉方案', 740, 500, 'create-v3.html');
            });
        });
    </script>
</body>
</html>