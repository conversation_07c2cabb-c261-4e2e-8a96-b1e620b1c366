﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 515px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
        .col2_left { width: 70px !important; }
        .col2_right { width: 200px !important; }
        .red { color: #f00 }
        .layui-form-item { margin-bottom: 0 }
        #content img, #referenceReport img { max-width: 100%; }
        .log-item { display: none; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 20px 0;">
                <input id="id" name="id" type="hidden" />
                <div class="layui-row">
                    <div class="layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">所属组织：</label>
                            <div class="layui-form-mid" id="subsidiaryName">-</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">标题：</label>
                            <div class="layui-form-mid" id="title">-</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">类型：</label>
                            <div class="layui-form-mid col2_right" id="type">-</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label ">状态：</label>
                            <div class="layui-form-mid " id="opStatus">-</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">股票：</label>
                            <div class="layui-form-mid " id="stockInfo">-</div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">买入区间：</label>
                                <div class="layui-form-mid" id="buyAmount">-</div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">目标区间：</label>
                                <div class="layui-form-mid" id="sellAmount">-</div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">止损区间：</label>
                            <div class="layui-form-mid col2_right" id="stopLossPrice">-</div>
                        </div>
                    </div>
                    <div class="layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label ">仓位：</label>
                            <div class="layui-form-mid col2_right" id="position">-</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">投资顾问：</label>
                            <div class="layui-form-mid " id="investmentAdvisor">-</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">资金限制：</label>
                            <div class="layui-form-mid " id="capitalLimit">-</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">发布时间：</label>
                            <div class="layui-form-mid " id="releaseAt">-</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label ">买入日期：</label>
                            <div class="layui-form-mid " id="buyDate">-</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">卖出日期：</label>
                            <div class="layui-form-mid " id="sellDate">-</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label ">收益：</label>
                            <div class="layui-form-mid " id="incomePercentage">-</div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">内容：</label>
                    <div class="layui-form-mid " id="content" style="width: 80%">-</div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">参考报告：</label>
                    <div class="layui-form-mid " id="referenceReport" style="width: 80%">-</div>
                </div>
                <div class="layui-form-item log-item">
                    <label class="layui-form-label">驳回记录：</label>
                    <div class="layui-form-mid">
                        <table id="logTable" lay-filter="list"></table>
                    </div>
                </div>
                <div style="height:100px;width:100%"></div>
                <div style="position: fixed; bottom: 0; left: 0; background: #fff; width: 100%; border-top: 1px solid #eee; padding: 15px 0; text-align: center; z-index: 999; box-shadow: 2px 1px 8px 1px rgba(0,0,0,.1) ">
                    <textarea type="text" name="auditRemark" placeholder="审核意见备注" autocomplete="off" class="layui-textarea" style="display:inline-block; width:400px;min-height:30px;margin-bottom:10px;"></textarea><br />
                    <input type="button" lay-submit="" data-status="3" class="layui-btn layui-btn-normal" lay-filter="recommendstock-submit" value="通过">
                    <input type="button" lay-submit="" data-status="4" class="layui-btn layui-btn-danger" lay-filter="recommendstock-submit" value="驳回">
                    <input type="button" class="layui-btn layui-btn-primary" onclick="location.href ='list-audit.html'" value="返回上一页">
                </div>
            </div>
        </div>
    </div>
    <script id="subsidiary-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="investmentadvisor-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">
            {{item.name}}
            {{# if(item.certNo!=''){}}
            （{{item.certNo}}）
            {{# }}}
        </option>
        {{#  }); }}
    </script>
    <script src="/lib/tinymce/tinymce.min.js"></script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.4"></script>
    <script type="text/javascript">
        layui.use(['recommendStock', 'laydate'], function () {

            layui.recommendStock.getForAudit();
            layui.recommendStock.bindEvent();
            //提交数据
            layui.form.on('submit(recommendstock-submit)', function (data) {
                data.field.auditStatus = layui.$(data.elem).data('status');
                var type = layui.common.getUrlParam('type');
                if (type == 1) {
                    layui.recommendStock.updateStatusForHg(data);
                }
                else if (type == 2) {
                    layui.recommendStock.updateStatusForFk(data);
                }
                return false; //阻止表单跳转
            });
        });
    </script>
</body>
</html>