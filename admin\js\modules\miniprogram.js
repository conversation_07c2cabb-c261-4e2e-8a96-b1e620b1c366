﻿layui.define(['laytpl', 'request', 'tableRequest', 'common', 'workWxApp'], function (exports) {
    var func = {
        /**
         * 渲染企微应用下拉选框
         * */
        initApp: function (id) {
            layui.workWxApp.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企微' });
                var getTpl = document.getElementById("workwx-app-tpl").innerHTML
                    , view = document.getElementById('workwx-app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('workwx-app-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过id获取单个微信小程序
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/miniprogram/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=appId]').val(res.result.appId);
                    layui.$('input[name=appSecret]').val(res.result.appSecret);
                    layui.$('input[name=page]').val(res.result.page);
                    layui.miniprogram.initApp(res.result.workWxAppId);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 通过企微应用id获取小程序列表
        * */
        queryByWorkWxAppId: function (workWxAppId, callbackFunc) {
            var url = '/admin/workwx/miniprogram/by-workwx-app-id/query?workWxAppId=' + workWxAppId;
            layui.request({
                method: 'post',
                url: url,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                callbackFunc(res);
            })
        },
        /**
         * 获取微信小程序列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'miniprogram-list', '/admin/workwx/miniprogram/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'workWxAppName', title: '所属企微' },
                { field: 'name', title: '小程序名称' },
                { field: 'appId', title: '小程序AppID' },
                { field: 'page', title: '页面地址' },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 150, align: 'left', toolbar: '#miniprogram-bar' }
            ]);
            //监听表格事件
            layui.miniprogram.tableEvent();
        },
        /**
        * 创建微信小程序
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/miniprogram/create',
                data: JSON.stringify({
                    name: data.field.name,
                    workWxAppId: data.field.workWxAppId,
                    appId: data.field.appId,
                    appSecret: data.field.appSecret,
                    page: data.field.page
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("miniprogram-list");
                    parent.layui.common.alertAutoClose("微信小程序创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑微信小程序
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/miniprogram/update',
                data: JSON.stringify({
                    id: data.field.id,
                    name: data.field.name,
                    appId: data.field.appId,
                    appSecret: data.field.appSecret,
                    workWxAppId: data.field.workWxAppId,
                    page: data.field.page
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("miniprogram-list");
                    parent.layui.common.alertAutoClose("微信小程序编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除微信小程序
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/miniprogram/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("miniprogram-list");
                    layui.common.alertAutoClose("微信小程序删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑微信小程序', 600, 400, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('删除微信小程序后，转化回传将失效，确定删除该小程序吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.miniprogram.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('miniprogram', func);
});