﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-table-cell { height: auto; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <input id="sort" type="hidden" name="sort" value="0" />
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="订单号/课程/姓名/手机号" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">支付方式</label>
                        <div class="layui-input-inline">
                            <select name="payType" id="payType">
                                <option value="">请选择支付方式</option>
                                <option value="1">未选择</option>
                                <option value="10">微信JsApi支付</option>
                                <option value="11">微信H5支付</option>
                                <option value="20">支付宝H5支付</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">支付状态</label>
                        <div class="layui-input-inline">
                            <select name="payStatus" id="payStatus">
                                <option value="">请选择支付状态</option>
                                <option value="1">待支付</option>
                                <option value="2">支付成功</option>
                                <option value="3">支付失败</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">退款状态</label>
                        <div class="layui-input-inline">
                            <select name="refundStatus" id="refundStatus">
                                <option value="">请选择退款状态</option>
                                <option value="1">未退款</option>
                                <option value="2">已提交申请</option>
                                <option value="3">部分退款成功</option>
                                <option value="4">部分退款失败</option>
                                <option value="5">全额退款成功</option>
                                <option value="6">全额退款失败</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">审核状态</label>
                        <div class="layui-input-inline">
                            <select name="reviewStatus" id="reviewStatus">
                                <option value="">请选择审核状态</option>
                                <option value="1">待审核</option>
                                <option value="2">审核通过</option>
                                <option value="3">审核驳回</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">来源渠道</label>
                        <div class="layui-input-inline">
                            <select name="channelKey" id="course-channel-view">
                                <option value="">请选择来源渠道</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list " lay-submit lay-filter="course-order-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="course-order-list" lay-filter="course-order-list"></table>
                <script type="text/html" id="course-order-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="detail">退款</a>
                    <a class="layui-btn layui-btn-xs  layui-btn-primary" lay-event="review">审核</a>
                    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>
    <script id="course-channel-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.key}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['courseOrder'], function () {
            layui.courseOrder.initCourseChannel();
            layui.courseOrder.query();
            layui.courseOrder.bindEvent();
        });
    </script>
</body>
</html>