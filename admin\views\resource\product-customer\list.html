<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        /* .layui-form-item .layui-input-inline { width: 300px; }*/
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="姓名/身份证号/手机号" />
                        </div>
                    </div>
                    <!--<div class="layui-inline">
                        <label class="layui-form-label">实名状态</label>
                        <div class="layui-input-inline">
                            <select name="isRealname" id="isRealname">
                                <option value="">请选择实名状态</option>
                                <option value="1">未实名</option>
                                <option value="2">已实名</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">来源</label>
                        <div class="layui-input-inline">
                            <select name="from" id="from">
                                <option value="">请选择来源</option>
                                <option value="1">购买产品</option>
                                <option value="2">优品客户端</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">注册时间</label>
                        <div class="layui-input-inline">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" autocomplete="off" />
                        </div>
                        <div class="layui-input-inline">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" autocomplete="off" />
                        </div>
                    </div>-->
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="customer-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div id="customer-list-body" class="layui-card-body">
                <table id="customer-list" lay-filter="list" style="width:1200px"></table>
                <script type="text/html" id="customer-bar">
                    <button class="layui-btn layui-btn-xs" id="layuidropdown_{{d.id}}" lay-filter="ft{{d.id}}"
                            lay-dropdown="{align:'center',menus: [{layIcon:'layui-icon-file',txt: '查看详情', event:'detail'}, {layIcon: 'layui-icon-edit',txt: '编辑', event:'edit'}, {layIcon: 'layui-icon-username', txt: '{{d.status==1?'冻结':'恢复'}}', event:'status'}, {layIcon: 'layui-icon-refresh-3',txt: '重置密码', event:'reset'},  {layIcon: 'layui-icon-read',txt: '查看证照', event:'read'},{layIcon: 'layui-icon-file',txt: '签署申请书', event:'sign'},{layIcon: 'layui-icon-about',txt: '查看申请书', event:'sign-view'}, {layIcon: 'layui-icon-delete', txt: '删除', event:'del'}]}">
                        <span>操作</span>
                        <i class="layui-icon layui-icon-triangle-d"></i>
                    </button>
                </script>
            </div>
        </div>
    </div>
    <script type="text/html" id="topToolBar">
        <div class="layui-btn-container layui-input-inline">
            {{# if(layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.customer.export')){}}
            <button class="layui-btn layui-btn-sm layuiadmin-btn-list layui-btn-primary" id="customer-export" lay-submit lay-filter="customer-export">
                <i class="layui-icon layui-icon-export layuiadmin-button-btn"></i>导出客户信息
            </button>
            {{#}}}
        </div>
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=2.9"></script>
    <script type="text/javascript">
        layui.use(['productCustomer'], function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            layui.productCustomer.query();
            layui.productCustomer.bindEvent();
            if (!layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.copy')) {
                layui.$(document).bind("contextmenu", function () { return false; });
                layui.$(document).bind("selectstart", function () { return false; });
            }
        });
    </script>
</body>
</html>