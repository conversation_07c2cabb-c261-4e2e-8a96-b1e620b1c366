﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item { margin-bottom: 0; }
        .layui-form-label { width: 100px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-tab-title" style="height: auto">
                <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" style="margin:15px 0 15px 30px;" onclick="history.back(-1)">
                    <i class="layui-icon layui-icon-left"></i>返回上一页
                </button>
            </div>
            <div class="layui-card-body">

                <div class="layui-form-item">
                    <label class="layui-form-label">数据ID:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="id"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">所属企微:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="workWxAppName"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">数据产生时间:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="dataTime"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">操作用户:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="opUserName"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">生成时间:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" id="createdAt"></div>
                    </div>
                </div>
                <table id="stat-list" class="layui-table" lay-filter="list">
                    <thead>
                        <tr>
                            <th>投资顾问</th>
                            <th>消费</th>
                            <th>加微量</th>
                            <th>加微成本</th>
                            <th>去重加微量</th>
                            <th>去重加微成本</th>
                            <th style="width:60px;">直播时长（小时）</th>
                            <th>新增率</th>
                            <th>助播</th>
                            <th>开口数</th>
                            <th>去重开口数</th>
                            <th>删除数</th>
                            <th>去重删除数</th>
                        </tr>
                    </thead>
                    <tbody id="stat-view">
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script id="stat-tpl" type="text/html">
        {{# if(d.length==0){ }}
        <tr><td colspan="14" style="text-align:center;">暂无符合条件的数据</td></tr>
        {{# } else { }}
        {{#  layui.each(d, function(index, item){ }}
        <tr>
            <td>{{item.teacherName}}</td>
            <td>{{item.consumptionAmount}}</td>
            <td>{{item.followCount}}</td>
            <td>{{item.followCost}}</td>
            <td>{{item.distinctFollowCount}}</td>
            <td>{{item.distinctFollowCost}}</td>
            <td>{{item.liveDuration}}</td>
            <td>{{item.addedRate}}%</td>
            <td>{{item.assistantName}}</td>
            <td>{{item.opendCountFor24Hours}}</td>
            <td>{{item.distinctOpendCountFor24Hours}}</td>
            <td>{{item.deletedCountFor24Hours}}</td>
            <td>{{item.distinctDeletedCountFor24Hours}}</td>
        </tr>
        {{#  }); }}
        {{# } }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'externalContact'], function () {
            layui.externalContact.getStatRecord();
        });
    </script>
</body>
</html>