﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属企业</label>
                        <div class="layui-input-inline">
                            <select name="companyId" id="company-view" lay-filter="department-search">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline" id="add-department">
                        <button class="layui-btn layuiadmin-btn-list department-create"><i class="layui-icon">&#xe654;</i>添加一级部门</button>
                    </div>
                </div>
            </div>
            <div id="department-list" class="demo-tree demo-tree-box" style="width: 500px;margin:10px 0 0 40px;padding-bottom:20px; "></div>
        </div>
    </div>

    <script id="company-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'department'], function () {
            //if (layui.common.getPermission('63369ab96918e5623bad0f65', 'sso.department.all.read')) {
            //    layui.$('#add-department').show();
            //}
            layui.department.initCompany();
            layui.$('.department-create').click(function () {
                layui.common.openIframe('创建部门', 600, 320, 'create.html?companyId=' + layui.$('#company-view').val());
            })
        });
    </script>
</body>
</html>