﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline" id="company-item">
                        <label class="layui-form-label">所属企业</label>
                        <div class="layui-input-inline">
                            <select name="companyId" id="company-view">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关键字</label>
                        <div class="layui-input-inline">
                            <input id="txb_keywords" name="keywords" placeholder="姓名/登录账号" type="text" class="layui-input" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="admin-user-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list admin-user-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="admin-user-list" lay-filter="list"></table>
                <script type="text/html" id="admin-user-bar">
                    <a class="layui-btn layui-btn-xs layui-btn-primary " lay-event="permission">权限</a>
                    <a class="layui-btn layui-btn-xs layui-btn-primary " lay-event="data-permission">数据权限</a>
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>
    <script type="text/html" id="topToolBar">
        <div class="layui-btn-container">
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="frozen">冻结</button>
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="quit">离职</button>
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="recovery">恢复</button>
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="reset">重置密码</button>
            <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="del">删除</button>
        </div>
    </script>
    <script id="company-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/lib/jsencrypt/jsencrypt.min.js"></script>
    <script src="/admin/js/global.js?v=2.3"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'adminUser'], function () {
            layui.adminUser.initCompany('', 'list');
            //监听查询按钮
            layui.form.on('submit(admin-user-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("admin-user-list", {
                    where: field
                });
            });
            //打开添加弹框
            layui.$('.admin-user-create').click(function () {
                var createUrl = 'create.html';
                var companyId = layui.common.getUrlParam('companyId') || '';
                if (companyId != '') {
                    createUrl += '?companyId=' + companyId;
                }
                layui.common.openIframe('创建用户', 650, 600, createUrl);
            });
        });
    </script>
</body>
</html>