﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属企微</label>
                        <div class="layui-input-inline">
                            <select name="workWxAppId" id="workwx-app-view" lay-filter="kf-account-search" lay-search>
                            </select>
                        </div>
                    </div>
                    <!--<div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list kf-account-search" lay-submit lay-filter="">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>-->
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list kf-account-create"><i class="layui-icon">&#xe654;</i>添加</button>
                        <button class="layui-btn layuiadmin-btn-list layui-btn-primary kf-account-sync"><i class="layui-icon">&#xe669;</i>同步账号</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="kf-account-list" lay-filter="list"></table>
                <script type="text/html" id="kf-account-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['kfAccount'], function () {
            layui.kfAccount.query();
        });
    </script>
</body>
</html>