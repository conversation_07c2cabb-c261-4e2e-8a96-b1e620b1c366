﻿layui.define(['laytpl', 'request', 'tableRequest', 'common', 'workWxApp', 'kfCase', 'oceanengineApp', 'oceanengineAccount'], function (exports) {
    var func = {
        /**
        * 渲染推广账户下拉选框
        */
        initAdvertiser: function () {
            layui.oceanengineAccount.getAll(function (res) {
                res.result.unshift({ accountId: '', accountName: '请选择推广账户' });
                var getTpl = document.getElementById("advertiser-tpl").innerHTML
                    , view = document.getElementById('advertiser-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('select');
            });
        },
        /**
         * 获取巨量广告消费数据
         * */
        query: function () {
            var fields = [{ field: 'advertiserName', title: '账户名称', align: 'left', rowspan: 2, minWidth: 180 },
            /* { field: 'kfCaseName', title: '加粉方案名称', align: 'center', rowspan: 2, minWidth: 150 },*/
            {
                field: 'date', title: '数据日期', align: 'center', rowspan: 2, minWidth: 110, templet: function (e) {
                    return layui.common.timeFormat(e.date).split(' ')[0];
                }
            },
            { title: '消耗数据', align: 'center', colspan: 6 },
            { title: '转化数据', align: 'center', colspan: 3 },
            {
                fixed: 'right', field: 'updatedAt', title: '最后更新时间', rowspan: 2, width: 170, templet: function (e) {
                    return layui.common.timeFormat(e.updatedAt);
                }
            }];
            var subFields = [
                {
                    field: 'cost', title: '消耗费用', minWidth: 100, align: 'center', templet: function (e) {
                        return e.cost == 0 ? '-' : e.cost;
                    }
                },
                {
                    field: 'show', title: '展示次数', minWidth: 100, align: 'center', templet: function (e) {
                        return e.show == 0 ? '-' : e.show;
                    }
                },
                {
                    field: 'avgShowCost', title: '平均千次展示费用', minWidth: 100, align: 'center', templet: function (e) {
                        return e.avgShowCost == 0 ? '-' : e.avgShowCost;
                    }
                },
                {
                    field: 'avgClickCost', title: '平均点击单价', minWidth: 100, align: 'center', templet: function (e) {
                        return e.avgClickCost == 0 ? '-' : e.avgClickCost;
                    }
                },
                {
                    field: 'click', title: '点击数', minWidth: 100, align: 'center', templet: function (e) {
                        return e.click == 0 ? '-' : e.click;
                    }
                },
                {
                    field: 'ctr', title: '点击率', minWidth: 100, align: 'center', templet: function (e) {
                        return e.ctr == 0 ? '-' : e.ctr + '%';
                    }
                },
                {
                    field: 'convert', title: '转化数', minWidth: 100, align: 'center', templet: function (e) {
                        return e.convert == 0 ? '-' : e.convert;
                    }
                },
                {
                    field: 'convertCost', title: '转化成本', minWidth: 100, align: 'center', templet: function (e) {
                        return e.convertCost == 0 ? '-' : e.convertCost;
                    }
                },
                {
                    field: 'convertRate', title: '转化率', minWidth: 100, align: 'center', templet: function (e) {
                        return e.convertRate == 0 ? '-' : e.convertRate + '%';
                    }
                }
            ];
            layui.tableRequest.request('resource', true, 'report-list', '/admin/workwx/oceanengine/ad-report/query', 'application/json', fields, {}, null, null, subFields);
        },
    }
    exports('oceanengineReport', func);
});