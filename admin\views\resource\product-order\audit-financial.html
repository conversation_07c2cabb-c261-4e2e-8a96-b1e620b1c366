﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 550px; }
        .layui-upload-drag { width: 100%; box-sizing: border-box; }
        .img-item { display: inline-block; position: relative; margin-right: 5px; }
        .img-item span.remove { position: absolute; cursor: pointer; right: 0; top: 0; display: inline-block; width: 15px; height: 15px; text-align: center; line-height: 15px; background: #000; opacity: 0.5; color: #fff }
        .pay-info { margin-bottom: 10px; margin-bottom: 10px; float: left; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 10px 30px 0 0;">
        <div class="layui-form-item" style="margin-bottom:0">
            <label class="layui-form-label">客户信息</label>
            <div class="layui-input-inline">
                <label class="layui-form-mid layui-word-aux" id="customerInfo"></label>
            </div>
        </div>
        <div class="layui-form-item" style="margin-bottom:0">
            <label class="layui-form-label">产品信息</label>
            <div class="layui-input-inline">
                <label class="layui-form-mid layui-word-aux" id="productInfo"></label>
            </div>
        </div>
        <div class="layui-form-item" style="margin-bottom:0">
            <label class="layui-form-label">订单号</label>
            <div class="layui-input-inline">
                <label class="layui-form-mid layui-word-aux" id="orderInfo"></label>
            </div>
        </div>
        <div class="layui-form-item" style="margin-bottom:0">
            <label class="layui-form-label">支付方式/金额</label>
            <div class="pay-items" style="width:550px;float:left;">
                <div class="pay-item-list">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">支付时间</label>
            <div class="layui-input-inline">
                <input type="datetime" id="payTime" name="payTime" lay-verify="required" placeholder="请选择支付时间" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">审核意见</label>
            <div class="layui-input-inline">
                <select name="financialAuditStatus" id="financialAuditStatus" lay-verify="required">
                    <option value="">请选择审核意见</option>
                    <option value="2">通过</option>
                    <option value="3">驳回</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">审核备注</label>
            <div class="layui-input-inline">
                <textarea type="text" id="financialRemark" name="financialRemark" placeholder="审核备注" autocomplete="off" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="order-audit-submit" value="确认修改">
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=2.2"></script>
    <script type="text/javascript">
        var payTypeXmSel;
        layui.use(['laydate', 'productOrder'], function () {
            layui.productOrder.getBrief('audit-financial');
            //监听提交事件
            layui.form.on('submit(order-audit-submit)', function (data) {
                layui.productOrder.auditForFinancial(data);
                return false; //阻止表单跳转
            });
            layui.laydate.render({
                elem: '#payTime',
                type: 'datetime'
            });

        });
    </script>
</body>
</html>