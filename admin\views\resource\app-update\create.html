﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 100px; }
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">版本号</label>
            <div class="layui-input-inline">
                <input type="text" name="version" lay-verify="required|number" placeholder="请输入版本号" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">更新平台</label>
            <div class="layui-input-inline">
                <input type="radio" name="paltform" value="1" title="Android" checked="checked">
                <input type="radio" name="paltform" value="2" title="Ios">
                <input type="radio" name="paltform" value="3" title="微信小程序">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">文件</label>
            <div class="layui-input-inline">
                <div class="layui-inline">
                    <button type="button" class="layui-btn layui-btn-primary " id="uploadImg_default"><i class="layui-icon"></i>上传文件</button>
                    <p id="filename" style="margin-top:5px;"></p>
                    <input id="url" name="url" value="" type="hidden" />
                </div>
                <div class="layui-inline img-info">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="submit" value="确认添加">
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.1"></script>
    <script type="text/javascript">
        layui.use(['appUpdate'], function () {
            //监听提交事件
            layui.form.on('submit(submit)', function (data) {
                if (data.field.url == '') {
                    layui.common.alertAutoClose("请上传文件");
                    return;
                }
                layui.appUpdate.create(data);
                return false; //阻止表单跳转
            });
            //上传凭证
            layui.uploadFile('resource', 'uploadImg_default', '/common/file/public/put?folder=app', function (res, index, upload) {
                if (res.isSuccess) {
                    layui.$('#filename').text(res.result.split("/").pop());
                    layui.$('#url').val(res.result);
                    layui.common.alertAutoClose("上传成功");
                }
                else {
                    layui.common.alert(res.message, 2);
                }
            });
        })
    </script>
</body>
</html>