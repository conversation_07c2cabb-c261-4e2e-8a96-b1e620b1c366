﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 180px; }
    </style>
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <input id="classify" name="classify" type="hidden" value="" />
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属组织</label>
                        <div class="layui-input-inline">
                            <select name="subsidiaryId" id="subsidiary-view" lay-search></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input"
                                   placeholder="请输入标题/渠道名称" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="status" id="status">
                                <option value="">请选择状态</option>
                                <option value="2">待审核</option>
                                <option value="3">完成</option>
                                <option value="4">驳回</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">送审日期</label>
                        <div class="layui-input-inline" style="width:120px;">
                            <input id="startTime" name="startTime" type="text" class="layui-input" placeholder="开始时间" />
                        </div>
                        <div class="layui-input-inline" style="width:120px;">
                            <input id="endTime" name="endTime" type="text" class="layui-input" placeholder="结束时间" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="media-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                        <button class="layui-btn layuiadmin-btn-list layui-btn-primary" lay-submit
                                lay-filter="media-export">
                            <i class="layui-icon layui-icon-export layuiadmin-button-btn"></i>导出
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="media-list" lay-filter="list"></table>
                <script type="text/html" id="media-bar">
                    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="read">查看</a>
                    {{# if(d.status!=3&&d.status!=4){}}
                    <a class="layui-btn layui-btn-xs" lay-event="audit">审核</a>
                    {{#}}}
                    <!-- {{# if(d.fkAuditStatus!=3&&d.fkAuditStatus!=4){}}
                    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="fk-audit">风控复审</a>
                    {{#}}} -->
                </script>
                <script id="subsidiary-tpl" type="text/html">
                    {{#  layui.each(d, function(index, item){ }}
                    <option value="{{item.id}}">{{item.name}}</option>
                    {{#  }); }}
                </script>
            </div>
        </div>
    </div>
    <script src="https://gosspublic.alicdn.com/aliyun-oss-sdk-6.18.0.min.js"></script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.3"></script>
    <script type="text/javascript">
        layui.use(['form', 'laydate', 'common', 'media'], function () {
            layui.laydate.render({
                elem: '#startTime',
                type: 'date'
            });
            layui.laydate.render({
                elem: '#endTime',
                type: 'date'
            });
            var type = layui.common.getUrlParam('type');
            layui.$('#classify').val(type);
            layui.media.initSubsidiary();
            layui.media.query();
            //监听查询按钮
            layui.form.on('submit(media-search)', function (data) {
                var field = data.field;
                if (field.status == '') {
                    field.status = -1;
                }
                //执行重载
                layui.tableRequest.reload("media-list", {
                    where: field
                });
            });
            //监听导出按钮
            layui.form.on('submit(media-export)', function (data) {
                if (data.field.status == '') {
                    data.field.status = -1;
                }
                data.field.companyId = layui.setter.productCompanyId;
                layui.media.export(data);
            });
        });
    </script>
</body>
</html>