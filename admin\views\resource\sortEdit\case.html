<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        html {
            background-color: #fff;
        }

        html,
        body {
            height: 100%;
        }

        .layui-form-select dl {
            max-height: 116px;
        }

        .layui-form-label {
            width: 100px;
        }

        .layui-form-item .layui-input-inline {
            width: 360px;
        }

        .layui-form-item {
            display: flex;
            align-items: center
        }

        .layui-input,
        .layui-select,
        .layui-textarea {
            height: 32px !important;
        }

        .layui-input-inline {
            margin: 0 !important;
            height: 32px;
            line-height: 32px;
            margin-left: 15px !important;
        }

        .layui-upload-drag {
            width: 100%;
            box-sizing: border-box;
        }

        .img-item {
            display: inline-block;
            position: relative;
            margin-right: 5px;
        }

        .img-item span.remove {
            position: absolute;
            cursor: pointer;
            right: 0;
            top: 0;
            display: inline-block;
            width: 15px;
            height: 15px;
            text-align: center;
            line-height: 15px;
            background: #000;
            opacity: 0.5;
            color: #fff
        }

        .pay-info {
            margin-bottom: 10px;
            margin-bottom: 10px;
            float: left;
        }

        * {
            box-sizing: border-box;
        }

        .layui-form-label {
            width: 70px;
            height: 20px;
            text-align: right;
            padding: 0;
        }

        .layui-form {
            padding: 20px;
            font-size: 14px;
            line-height: 22px;
        }

        .layui-form-item .layui-input-inline {
            width: 300px;
            height: 32px;
        }
    </style>
</head>

<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list">
        <div class="layui-form-item">
            <label class="layui-form-label">落地页名称</label>
            <div class="layui-input-inline">
                <div id="textName"></div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">落地页ID</label>
            <div class="layui-input-inline">
                <div id="sortId"></div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">加粉方案</label>
            <div class="layui-input-inline">
                <select name="caseId" id="case-view" lay-verify="required" lay-search></select>
            </div>
        </div>
        <div class="layui-form-item" style="margin-top: 74px;    text-align: end;">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" style="background-color: #fff; color: #000;border-radius: 4px;
border: 1px solid #DCDFE6;" onclick="cancel()" value="取消">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal"
                    lay-filter="patrol-update-status-submit" value="确认">
            </div>
        </div>
        <script id="case-tpl" type="text/html">
            {{#  layui.each(d, function(index, item){ }}
            <option value="{{item.id}}">{{item.name}}</option>
            {{#  }); }}
        </script>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=2.2"></script>
    <script type="text/javascript">
        let caseList = []
        function caseGetAll(id) {
            let workWxAppId = "62e240813e6ecaa906b2f2c8"
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/by-workwx-app-id/query?workWxAppId=' + workWxAppId,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    caseList = res.result;
                    res.result.unshift({ id: '', name: '请选加粉方案' });
                    var getTpl = document.getElementById("case-tpl").innerHTML
                        , view = document.getElementById('case-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    if (id != undefined) {
                        document.getElementById('case-view').value = id;
                    }
                    layui.form.render('select');
                }
            })
        }
        let cancel;
        layui.use(['form', 'jquery', 'laydate', 'sortEdit', "common"], function () {
            caseGetAll()
            let $ = layui.jquery;
            $("#textName").text(layui.common.getUrlParam('name'))
            $("#sortId").text(layui.common.getUrlParam('id'))
            layui.form.on('submit(patrol-update-status-submit)', function (data) {
                data.field.type = caseList.filter(item => item.id == data.field.caseId)[0].source == 'kf' ? 1 : 2
                layui.sortEdit.updateState(data);
                return false; //阻止表单跳转
            });
            cancel = function () {
                parent.layui.common.closeType('iframe');
            }
        });
    </script>
</body>

</html>