﻿layui.define(['laytpl', 'form', 'xmSelect', 'request', 'tableRequest', 'opTable', 'laydate', 'dropdown', 'common', "jquery",
    'workWxApp', 'productChannel', 'department', 'adminUser', 'product', 'externalContact', 'kfCase', 'callbot', 'subsidiary'], function (exports) {
        var func = {
            /**
            * 上传附件
            * */
            initFile: function () {
                var files = [];
                layui.uploadFile('resource', 'uploadAnnex', '/common/file/public/put?folder=admin/order/annex', function (res, index, upload) {
                    if (res.isSuccess) {
                        for (var i = 0; i < files.length; i++) {
                            if (files[i].index == index) {
                                files[i].url = res.result;
                                layui.$('.file-list').append('<li id="li_' + index + '"><a href="' + files[i].url + '" target="_blank" title="' + files[i].name + '"><i class="layui-icon layui-icon-link"></i> ' + files[i].name + '</a><i class="layui-icon layui-icon-delete remove" onclick="layui.$(this).parent(\'li\').remove()"></i></li>');
                            }
                        }
                        console.log(files);
                    }
                    else {
                        layui.common.alert(res.message, 2);
                    }
                }, function (obj) {
                    obj.preview(function (index, file, result) {
                        files.push({ index: index, name: file.name, size: file.size, url: '' })
                    });
                });
            },
            /**
            * 渲染所属组织下拉选框
            * */
            initSubsidiary: function (id) {
                layui.subsidiary.getAll(function (res) {
                    res.result.unshift({ id: '', name: '请选择所属组织' });
                    var getTpl = document.getElementById("subsidiary-tpl").innerHTML
                        , view = document.getElementById('subsidiary-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    if (id != undefined) {
                        document.getElementById('subsidiary-view').value = id;
                    }
                    layui.form.render('select');
                });
            },
            /**
           * 渲染企微应用下拉选框
           * */
            initApp: function (id) {
                layui.workWxApp.getAll(function (res) {
                    res.result.unshift({ id: '', name: '请选择企微' });
                    var getTpl = document.getElementById("workwx-app-tpl").innerHTML
                        , view = document.getElementById('workwx-app-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    if (id != undefined) {
                        document.getElementById('workwx-app-view').value = id;
                    }
                    layui.form.render('select');
                });
            },
            /**
             * 渲染支付方式搜索框
             * */
            initPayTypeForSerch: function () {
                var arr = layui.setter.payTypeArr;
                arr.unshift({ id: '', name: '请选择支付方式' });
                var getTpl = document.getElementById("paytype-tpl").innerHTML
                    , view = document.getElementById('payType');
                layui.laytpl(getTpl).render(arr, function (html) {
                    view.innerHTML = html;
                });
                layui.form.render('select');
            },
            /**
            * 渲染支付方式选择框
            * */
            initPayTypeForCreate: function () {
                var arr = layui.$.grep(layui.setter.payTypeArr, function (item) {
                    return !item.isOnline && item.id != 300;
                });
                arr.unshift({ id: '', name: '支付方式' });
                var getTpl = document.getElementById("paytype-tpl").innerHTML
                    , view = document.getElementById('payType');
                layui.laytpl(getTpl).render(arr, function (html) {
                    view.innerHTML = html;
                });

                layui.form.render('select');
            },
            /**
           * 初始化加粉方案转化曲线图
           * */
            initChat: function (date) {
                var chartDom = document.getElementById('main');
                myChart = echarts.init(chartDom, 'dark');
                option = {
                    //title: {
                    //    text: '月度订单统计',
                    //},
                    tooltip: {
                        trigger: 'axis'
                    },
                    grid: {
                        left: '0',
                        /*right: '20%',*/
                        bottom: '0',
                        containLabel: true
                    },
                    toolbox: {
                        feature: {
                            saveAsImage: {}
                        }
                    },
                    yAxis: {
                        type: 'value',
                        minInterval: 1
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: []
                    },
                    legend: {
                        data: [],
                        type: 'scroll',
                        orient: 'vertical',
                        right: 0,
                        top: 40,
                        //bottom: 20,
                    },
                    series: []
                };
                myChart.setOption(option);
                layui.productOrder.getChatStat(date);
            },
            /**
             * 渲染订单所属用户（下级用户）
             * */
            initSalerUser: function (id) {
                layui.adminUser.getChildUser(layui.setter.productCompanyId, true, function (res) {
                    res.result.unshift({ id: '', name: '请选择订单所属用户' });
                    var getTpl = document.getElementById("saler-user-tpl").innerHTML
                        , view = document.getElementById('saler-user-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    if (id != undefined) {
                        view.value = id;
                    }
                    layui.form.render('select');
                });
            },
            /**
             * 渲染部门下拉选框
             * */
            initDepartment: function () {
                layui.department.queryByCompanyId(layui.setter.productCompanyId, function (res) {
                    var json = [{ id: '', name: '请选择所属部门' }];
                    for (var i = 0; i < res.result.length; i++) {
                        var level1 = { id: res.result[i].id, name: res.result[i].name };
                        json.push(level1);
                        var child2 = res.result[i].childs;
                        if (child2 != null && child2.length > 0) {
                            for (var j = 0; j < child2.length; j++) {
                                var level2 = { id: child2[j].id, name: res.result[i].name + ' > ' + child2[j].name };
                                json.push(level2);
                                var child3 = child2[j].childs;
                                if (child3 != null && child3.length > 0) {
                                    for (var k = 0; k < child3.length; k++) {
                                        var level3 = { id: child3[k].id, name: res.result[i].name + ' > ' + child2[j].name + ' > ' + child3[k].name };
                                        json.push(level3);
                                        var child4 = child3[k].childs;
                                        if (child4 != null && child4.length > 0) {
                                            for (var m = 0; m < child4.length; m++) {
                                                var level4 = { id: child4[m].id, name: res.result[i].name + ' > ' + child2[j].name + ' > ' + child3[k].name + ' > ' + child4[m].name };
                                                json.push(level4);
                                                var child5 = child4[m].childs;
                                                if (child5 != null && child5.length > 0) {
                                                    for (var n = 0; n < child5.length; n++) {
                                                        var level5 = { id: child5[n].id, name: res.result[i].name + ' > ' + child2[j].name + ' > ' + child3[k].name + ' > ' + child4[l].name + ' > ' + child5[n].name };
                                                        json.push(level5);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    var getTpl = document.getElementById("department-tpl").innerHTML
                        , view = document.getElementById('department-view');
                    layui.laytpl(getTpl).render(json, function (html) {
                        view.innerHTML = html;
                    });
                    layui.form.render('select');
                });
            },
            /**
            * 渲染客户来源渠道下拉列表
            */
            getKfCaseList: function () {
                //证券通资讯企微
                layui.kfCase.queryByWorkWxAppId('62e23f633e6ecaa906b2f2c7', function (res) {
                    if (res.result == null) {
                        res.result = [{ id: '', name: '请选择客户来源渠道' }]
                    }
                    else {
                        res.result.unshift({ id: '', name: '请选择客户来源渠道' });
                    }
                    var getTpl = document.getElementById("kf-case-tpl").innerHTML
                        , view = document.getElementById('kf-case-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.form.render('select');
                }, '');
            },
            /**
             * 通过条件搜索外部联系人列表
             * */
            searchExternalContact: function (id) {
                xmSel = layui.xmSelect.render({
                    el: '#external-contact',
                    language: 'zn',
                    filterable: true,
                    tips: '请选择对应的企微联系人',
                    theme: { color: '#8799a3 ' },
                    toolbar: { show: false },
                    radio: true,
                    clickClose: true,
                    autoRow: true,
                    remoteSearch: true,
                    remoteMethod: function (val, cb, show) {
                        //这里如果val为空, 则不触发搜索
                        if (!val) {
                            return cb([]);
                        }
                        var workWxAppId = layui.$('#workwx-app-view').val();
                        if (workWxAppId == '') {
                            layui.common.alertAutoClose("请选择所属企微");
                            return cb([]);
                        }
                        //调用远程数据
                        layui.externalContact.search(workWxAppId, val, function (res) {
                            if (res.isSuccess) {
                                var data = [];
                                if (res.result.length > 0) {
                                    for (var i = 0; i < res.result.length; i++) {
                                        var item = { name: res.result[i].name, value: res.result[i].id };
                                        data.push(item);
                                    }
                                }
                                cb(data);
                            }
                            else {
                                cb([]);
                            }
                        });
                    },
                });
                if (id != undefined && id != null && id != '') {
                    layui.externalContact.getById(id, function (res) {
                        if (res.isSuccess) {
                            layui.productOrder.initApp(res.result.workWxAppId);
                            var data = [{ name: res.result.name, value: res.result.id }];
                            xmSel.update({
                                data: data,
                                initValue: data
                            })
                        }
                    });
                }
            },
            /**
             * 渲染产品渠道下拉选框
             * */
            initProductChannel: function () {
                layui.productChannel.getAll(function (res) {
                    res.result.unshift({ key: '', name: '请选择来源渠道' });
                    var getTpl = document.getElementById("product-channel-tpl").innerHTML
                        , view = document.getElementById('product-channel-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.form.render('select');
                });
            },
            /**
             * 渲染产品列表
             */
            initProduct: function () {
                layui.product.getAll(function (res) {
                    res.result.unshift({ key: '', name: '请选择购买产品' });
                    var getTpl = document.getElementById("product-tpl").innerHTML
                        , view = document.getElementById('product-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.form.render('select');
                });
            },
            /**
             * 渲染分配用户列表
             * @param {any} id
             */
            initAdminUser: function (from) {
                var salerUserId = layui.common.getUrlParam('sid') || '';
                layui.adminUser.getUserList(layui.setter.productCompanyId, function (res) {
                    res.result.unshift({ id: '', name: '请选择' + (from == 'list' ? '所属' : '分配') + '用户', account: '' });
                    if (from == 'list') {
                        salerUserList = res.result;
                    }
                    var getTpl = document.getElementById("admin-user-tpl").innerHTML
                        , view = document.getElementById('admin-user-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    if (salerUserId != '') {
                        document.getElementById('admin-user-view').value = salerUserId;
                    }
                    layui.form.render('select');
                });
            },
            /**
             * 渲染产品
             * */
            initProduct: function (id, teacherName) {
                layui.product.getAll(function (res) {
                    res.result.unshift({ id: '', name: '请选择购买产品', teacherNames: [] });
                    var getTpl = document.getElementById("product-tpl").innerHTML
                        , view = document.getElementById('product-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    if (id != undefined) {
                        view.value = id;

                        var names = layui.$('#product-view').find('option:selected').data('names');
                        var teacherArr = [{ id: '', name: '请选择投资顾问' }];
                        if (names != '') {
                            var nameList = names.split(',');
                            if (nameList.length > 0) {
                                for (var i = 0; i < nameList.length; i++) {
                                    teacherArr.push({ id: nameList[i], name: nameList[i] })
                                }
                            }
                        }
                        var getTpl2 = document.getElementById("teacher-name-tpl").innerHTML
                            , view2 = document.getElementById('teacher-name-view');
                        layui.laytpl(getTpl2).render(teacherArr, function (html) {
                            view2.innerHTML = html;
                        });
                        if (teacherName != undefined) {
                            view2.value = teacherName;
                        }
                        layui.form.render('select');
                    }
                    layui.form.render('select');
                });
            },
            /**
            * 渲染产品
            * */
            initProduct2: function () {
                layui.product.getAll(function (res) {
                    res.result.unshift({ id: '', name: '请选择购买产品' });
                    var getTpl = document.getElementById("product-tpl").innerHTML
                        , view = document.getElementById('product-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.form.render('select');
                }, '');
            },
            /**
            * 通过日期查询加微统计数
            * */
            getChatStat: function (date) {
                var data = { date: date }
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/stat-chat/get',
                    data: JSON.stringify(data),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        var series = [];
                        if (res.result.series.length > 0) {
                            for (var i = 0; i < res.result.series.length; i++) {
                                series.push({
                                    showSymbol: false,
                                    name: res.result.series[i].name,
                                    //type: 'line',
                                    type: 'bar',
                                    smooth: true,
                                    data: res.result.series[i].data
                                })
                            }
                        }
                        option.series = series;
                        option.legend.data = res.result.kfCaseNames;
                        option.xAxis.data = res.result.times
                        myChart.setOption(option, true);
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                });
            },
            /**
            * 获取外呼任务列表
            * */
            initCallBotTask: function (date) {
                layui.callbot.getAll(function (res) {
                    if (res.isSuccess) {
                        res.result.unshift({ taskId: '', taskName: '请选择任务模板' });
                        var getTpl = document.getElementById("task-tpl").innerHTML
                            , view = document.getElementById('task-view');
                        layui.laytpl(getTpl).render(res.result, function (html) {
                            view.innerHTML = html;
                        });
                        layui.form.render('select');
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                });
            },
            /**
             * 通过id获取产品订单信息
             * */
            get: function () {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    layui.common.alertAutoClose("参数有误");
                    setTimeout(function () { history.back(-1) }, 2000);
                    return;
                }
                layui.$('#hid_id').val(id);
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/get?id=' + id,
                }).then(function (res) {
                    if (res.isSuccess) {
                        //订单信息
                        layui.$('#id').text(res.result.id);
                        layui.$('#orderNo').text(res.result.orderNo);
                        layui.$('#saleUserDepartmentName').text(res.result.departmentName);
                        layui.$('#riskAssessmentScore').text(res.result.riskAssessmentScore);
                        layui.$('#riskLevel').text(res.result.riskLevelDesc);
                        layui.$('#riskTolerance').text(res.result.riskTolerance);
                        layui.$('#contractNo').text(res.result.contractNo);
                        var signStatus = res.result.signFlowStatusDesc;
                        if (res.result.signFlowFinishTime != null) {
                            signStatus += ' - ' + layui.common.timeFormat(res.result.signFlowFinishTime)
                        }
                        layui.$('#signFlowStatus').text(signStatus);
                        if (res.result.signFlowStatus != 3) {
                            layui.$('#order-sign-url').removeClass('layui-hide');
                        }
                        var payStatus = res.result.payStatusDesc;
                        if (res.result.payTime != null) {
                            payStatus += ' - ' + layui.common.timeFormat(res.result.payTime)
                        }
                        layui.$('#payStatus').text(payStatus);
                        layui.$('#refundStatus').text(res.result.refundStatusDesc);
                        layui.$('#payType').text(res.result.payTypeDesc);
                        layui.$('#totalAmount').text(res.result.totalAmount);
                        layui.$('#payableAmount').text(res.result.payableAmount);
                        layui.$('#discountAmount').text(res.result.discountAmount);
                        layui.$('#actuallyPaidAmount').text(res.result.actuallyPaidAmount);
                        layui.$('#thirdPartyOrderNo').text(res.result.thirdPartyOrderNo);
                        var auditStatus = res.result.auditStatusDesc;
                        if (res.result.auditTime != null) {
                            auditStatus += ' - ' + layui.common.timeFormat(res.result.auditTime)
                        }
                        layui.$('#auditStatus').text(auditStatus);
                        layui.$('#auditRemark').text(res.result.auditRemark);

                        var financialAuditStatusDesc = res.result.financialAuditStatusDesc;
                        if (res.result.financialAuditTime != null) {
                            financialAuditStatusDesc += ' - ' + layui.common.timeFormat(res.result.financialAuditTime)
                        }
                        layui.$('#financialAuditStatus').text(financialAuditStatusDesc);
                        layui.$('#financialRemark').text(res.result.financialRemark);

                        var fkAuditStatusDesc = res.result.fkAuditStatusDesc;
                        if (res.result.fkAuditTime != null) {
                            fkAuditStatusDesc += ' - ' + layui.common.timeFormat(res.result.fkAuditTime)
                        }
                        layui.$('#fkAuditStatus').text(fkAuditStatusDesc);
                        layui.$('#fkAuditRemark').text(res.result.fkAuditRemark);
                        if (res.result.fkAuditFileUrl != null && res.result.fkAuditFileUrl != '') {
                            if (res.result.fkAuditFileUrl.indexOf(';') > -1) {
                                layui.$('#fkAuditFileUrl').html('');
                                var fileArr = res.result.fkAuditFileUrl.split(';');
                                for (var i = 0; i < fileArr.length; i++) {
                                    var urlArr = fileArr[i].split('/');
                                    var name = urlArr[urlArr.length - 1];
                                    layui.$('#fkAuditFileUrl').append('<a class="preview-img" href="' + fileArr[i] + '" target="_blank">' + name + '</a><br/>');
                                }
                            }
                            else {
                                var urlArr = res.result.fkAuditFileUrl.split('/');
                                var name = urlArr[urlArr.length - 1];
                                layui.$('#fkAuditFileUrl').html('<a class="preview-img"  href="' + res.result.fkAuditFileUrl + '" target="_blank" >' + name + '</a>');
                            }
                        }

                        layui.$('#status').text(res.result.statusDesc);
                        layui.$('#remark').text(res.result.remark);
                        layui.$('#serviceDate').text(layui.common.timeFormat(res.result.serviceStartTime).split(' ')[0] + ' 至 ' + layui.common.timeFormat(res.result.serviceEndTime).split(' ')[0]);
                        layui.$('#createdAt').text(layui.common.timeFormat(res.result.createdAt));
                        layui.$('#activityName').text(res.result.activityName == '' ? '-' : res.result.activityName + '（' + res.result.activityTypeDesc + '）');
                        if (res.result.activityType == 2) {
                            layui.$('#discountDetail').text('折扣：' + res.result.discount + '折，优惠金额：' + res.result.discountAmount);
                        }
                        else if (res.result.activityType == 3) {
                            layui.$('#discountDetail').text('赠送服务期：' + res.result.giveDuration + '（' + layui.common.timeFormat(res.result.giveServiceStartTime).split(' ')[0] + '至' + layui.common.timeFormat(res.result.giveServiceEndTime).split(' ')[0] + '）');
                        }
                        layui.$('#channelName').text(res.result.channelName);
                        layui.$('#saleUserName').text(res.result.saleUserName);
                        layui.$('#customerChannel').text(res.result.customerChannel);
                        if (res.result.transferVoucher != null && res.result.transferVoucher != '') {
                            if (res.result.transferVoucher.indexOf(';') > -1) {
                                layui.$('#transferVoucher').html('');
                                var imgArr = res.result.transferVoucher.split(';');
                                for (var i = 0; i < imgArr.length; i++) {
                                    layui.$('#transferVoucher').append('<a class="preview-img" data-url="' + imgArr[i] + '"><img style="max-width:500px;max-height:30px;margin-right:10px;cursor: pointer;" src="' + imgArr[i] + '" /></a>');
                                }
                            }
                            else {
                                layui.$('#transferVoucher').html('<a class="preview-img" data-url="' + res.result.transferVoucher + '" target="_blank"><img style="max-width:500px;max-height:30px;cursor: pointer;" src="' + res.result.transferVoucher + '" /></a>');
                            }
                        }
                        layui.$('#buyType').text(res.result.buyTypeDesc);
                        layui.$('.preview-img').bind('click', function () {
                            var imgUrl = layui.$(this).data('url');
                            layui.$('#preview').html('<img id="transferVoucher" src="' + imgUrl + '" style="width:100%;margin-bottom:10px;" />');
                            layui.common.openPage('查看转账凭证', 550, 600, '#preview')
                        })

                        //客户信息
                        layui.$('#customerId').text(res.result.customerId);
                        layui.$('#customerName').text(res.result.customerSnapshot.name);
                        //layui.$('#customerMobile').text(res.result.customerSnapshot.mobile);
                        //layui.$('#customerCertNo').text(res.result.customerSnapshot.certNo);
                        layui.$('#customerAddress').text(res.result.customerSnapshot.address);
                        layui.$('#customerProfession').text(res.result.customerSnapshot.profession);
                        layui.$('#customerEducation').text(res.result.customerSnapshot.education);
                        layui.$('#customerBadIntegrityRecord').text(res.result.customerSnapshot.badIntegrityRecord);
                        layui.$('#customerCompany').text(res.result.customerSnapshot.company);
                        layui.$('#customerCertValidity').text(res.result.customerSnapshot.certValidity);
                        layui.$('#customerAuthStatus').text(res.result.customerSnapshot.authStatusDesc);
                        layui.$('#age').text(res.result.age);
                        layui.$('#certProvince').text(res.result.certProvince);
                        if (res.result.customerSnapshot.role == 2) {
                            var companyInfo = "企业认证";
                            if (res.result.customerSnapshot.managerType == 1) {
                                companyInfo += '(法人)';
                            }
                            if (res.result.customerSnapshot.managerType == 2) {
                                companyInfo += '(代理人)';
                            }
                            if (res.result.customerSnapshot.companyName != null && res.result.customerSnapshot.companyName != '') {
                                companyInfo += '，企业名称：' + res.result.customerSnapshot.companyName;
                            }
                            if (res.result.customerSnapshot.organization != null && res.result.customerSnapshot.organization != '') {
                                companyInfo += '，统信码：' + res.result.customerSnapshot.organization;
                            }
                            if (res.result.customerSnapshot.legalName != null && res.result.customerSnapshot.legalName != '') {
                                companyInfo += '，法人：' + res.result.customerSnapshot.legalName;
                            }
                            layui.$('#authType').text(companyInfo);
                        }
                        else {
                            layui.$('#authType').text('个人认证');
                        }
                        layui.$('.read-customer-btn').bind('click', function () {
                            var type = layui.$(this).data('type');
                            layui.productOrder.readMobile(res.result.customerId, type);
                        })

                        //产品信息
                        layui.$('#productName').text(res.result.productSnapshot.name);
                        layui.$('#productType').text(res.result.productSnapshot.typeDesc);
                        layui.$('#productRiskLevel').text(res.result.productSnapshot.riskLevelDesc);
                        layui.$('#productAmount').text(res.result.productSnapshot.amount);
                        layui.$('#productDuration').text(res.result.productSnapshot.duration + (res.result.productSnapshot.unit == '月' ? '个' : '') + res.result.productSnapshot.unit);
                        layui.$('#teacherName').text(res.result.teacherName);
                        layui.$('#classHour').text(res.result.productSnapshot.classHour);
                        layui.$('#thirdPartyProductId').text(res.result.productSnapshot.thirdPartyProductId);

                        //退款订单
                        layui.$('.refund-content').show();
                        layui.tableRequest.request('resource', false, 'refund-order-list', '/admin/product/refund-order/by-orderno/query?orderNo=' + res.result.orderNo, 'application/json', [
                            {
                                title: '退款订单号', templet: function (e) {
                                    return '<a href="' + e.signUrl + '" target="_blank">' + e.refundOrderNo + '</a>'
                                }
                            },
                            { field: 'amount', title: '退款金额' },
                            { field: 'signFlowStatusDesc', title: '签署状态' },
                            { field: 'auditStatusDesc', title: '审核状态' },
                            { field: 'statusDesc', title: '退款状态' },
                            { field: 'remark', title: '退款备注' },
                            { field: 'opUserName', title: '操作用户' },
                            {
                                field: 'createdAt', title: '申请时间', width: 170, templet: function (e) {
                                    return layui.common.timeFormat(e.createdAt);
                                }
                            },
                            { fixed: 'right', title: '操作', width: 115, align: 'left', toolbar: '#refund-order-bar' }
                        ], {}, null, 'auto');
                        layui.table.on('tool(refund-order-list)', function (obj) {
                            var data = obj.data;
                            if (obj.event === 'copyurl') {
                                let copy = (e) => {
                                    e.preventDefault()
                                    e.clipboardData.setData('text/plain', data.signUrl)
                                    layui.common.alertAutoClose("复制成功");
                                    document.removeEventListener('copy', copy)
                                }
                                document.addEventListener('copy', copy)
                                document.execCommand("Copy");
                                return false; //阻止表单跳转
                            }
                        });
                        if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.call.read')) {
                            //通话记录
                            layui.productOrder.queryCallRecord(res.result.callGuids);
                            //通话记录（阿里云）
                            layui.productOrder.queryAliCallRecord(res.result.id);
                            //沟通记录
                            layui.productOrder.queryFollowUpRecord(res.result.id);
                            layui.$('.call-info').show();
                            layui.$('.create-followup').click(function () {
                                layui.common.openIframe('新增沟通记录', 400, 220, '../product-customer/create-followup.html?orderId=' + res.result.id);
                            });
                        }
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 通过多个id查询订单列表
             * */
            getByIds: function () {
                var ids = layui.common.getUrlParam('ids');
                if (ids == '') {
                    layui.common.alertAutoClose("参数有误");
                    setTimeout(function () { history.back(-1) }, 2000);
                    return;
                }
                layui.$('#hid_id').val(ids);
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/by-ids/query',
                    data: JSON.stringify({
                        ids: ids.split(',')
                    }),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        //订单信息
                        var item = '';
                        var totalPayAmount = 0;
                        for (var i = 0; i < res.result.length; i++) {
                            totalPayAmount += res.result[i].actuallyPaidAmount;
                            item += ' <label class="layui-form-mid layui-word-aux">' + res.result[i].orderNo + ' - ¥' + res.result[i].actuallyPaidAmount + ' - ' + res.result[i].productName + '</label>';
                        }
                        layui.$('#orderInfo').html(item);
                        layui.$('#totalPayAmount').text(totalPayAmount + '元');
                        layui.$('#certNo').val(res.result.certNo);
                        layui.$('#orderNo').val(res.result.thirdPartyOrderNo);
                        layui.$('#actuallyPaidAmount').val(res.result.actuallyPaidAmount);
                        layui.$('#serviceStartTime').val(layui.common.timeFormat(res.result.serviceStartTime).split(' ')[0]);
                        layui.$('#serviceEndTime').val(layui.common.timeFormat(res.result.serviceEndTime).split(' ')[0]);
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 通过id获取线下产品订单信息
             * */
            getOffline: function () {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    layui.common.alertAutoClose("参数有误");
                    setTimeout(function () { history.back(-1) }, 2000);
                    return;
                }
                layui.$('#hid_id').val(id);
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/offline/get?id=' + id,
                }).then(function (res) {
                    if (res.isSuccess) {
                        //订单信息
                        layui.$('#id').val(res.result.id);
                        layui.$('#name').val(res.result.name);
                        layui.$('#mobile').val(res.result.mobile);
                        layui.$('#certNo').val(res.result.certNo);
                        layui.$('#orderNo').val(res.result.thirdPartyOrderNo);
                        layui.$('#actuallyPaidAmount').val(res.result.actuallyPaidAmount);
                        layui.$('#serviceStartTime').val(layui.common.timeFormat(res.result.serviceStartTime).split(' ')[0]);
                        layui.$('#serviceEndTime').val(layui.common.timeFormat(res.result.serviceEndTime).split(' ')[0]);
                        if (res.result.transferVoucher != '') {
                            var imgList = res.result.transferVoucher.split(';');
                            for (var i = 0; i < imgList.length; i++) {
                                layui.$('.img-list').append('<div class="img-item"><span class="remove" onclick="removeImg(this)">x</span><a href="' + imgList[i] + '" target="_blank"> <img src="' + imgList[i] + '" style="max-width: 38px;max-height:35px;"></a></div>');
                            }
                        }
                        var payTypeArr = layui.$.grep(layui.setter.payTypeArr, function (item) {
                            return !item.isOnline && item.id != 300;
                        });
                        payTypeArr.unshift({ id: '', name: '请选择支付方式' });
                        if (res.result.payType == 300 && res.result.payItems != null) {
                            for (var i = 0; i < res.result.payItems.length; i++) {
                                var uploadId = layui.common.generateGuid();
                                if (layui.$('#uploadImg_' + uploadId).length > 0) {
                                    uploadId = layui.common.generateGuid();
                                }
                                var menuTpl = '<div class="pay-info pay-' + uploadId + '">';
                                menuTpl += '<div class="layui-input-inline" style="width:100px;">';
                                menuTpl += '<select name="payType" class="payType" lay-verify="required" lay-search>';
                                menuTpl += '<option value="">支付方式</option>';
                                for (var j = 0; j < payTypeArr.length; j++) {
                                    menuTpl += '<option value="' + payTypeArr[j].id + '" ' + (res.result.payItems[i].payType == payTypeArr[j].id ? 'selected="true"' : '') + '>' + payTypeArr[j].name + '</option>';
                                }
                                menuTpl += '</select>';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:90px;">';
                                menuTpl += '<input type="text" name="thirdPartyOrderNo" lay-verify="required" value="' + res.result.payItems[i].thirdPartyOrderNo + '"  placeholder="支付单号" autocomplete="off" class="layui-input thirdPartyOrderNo">';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:80px;">';
                                menuTpl += '<input type="text" name="payAmount" lay-verify="required" value=' + res.result.payItems[i].payAmount + ' placeholder="实付金额" autocomplete="off" class="layui-input payAmount">';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:auto;">';
                                menuTpl += '<button type="button" class="layui-btn layui-btn-primary " id="uploadImg_' + uploadId + '"><i class="layui-icon"></i>凭证</button>';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline remove-pay-item" style="width:auto;">';
                                menuTpl += '<button type="button" class="layui-btn layui-btn-primary remove-pay"><i class="layui-icon"></i></button>';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline img-list" style="width:auto;">';
                                if (res.result.payItems[i].transferVoucher != '') {
                                    var imgList = res.result.payItems[i].transferVoucher.split(';');
                                    for (var j = 0; j < imgList.length; j++) {
                                        menuTpl += '<div class="img-item"><span class="remove" onclick="removeImg(this)">x</span><a href="' + imgList[j] + '" target="_blank"> <img src="' + imgList[j] + '" style="max-width: 38px;max-height:35px;"></a></div>';
                                    }
                                }
                                menuTpl += '</div>';
                                menuTpl += '</div>';

                                layui.$('.pay-item-list').append(menuTpl);

                                layui.uploadFile('resource', 'uploadImg_' + uploadId, '/common/file/public/put?folder=admin/order/transfer-voucher', function (res2) {
                                    if (res2.isSuccess) {
                                        layui.$('.pay-' + uploadId).find('.img-list').append('<div class="img-item"><span class="remove" onclick="removeImg(this)">x</span><a href="' + res2.result + '" target="_blank"> <img src="' + res2.result + '" style="max-width: 38px;max-height:35px;"></a></div>');
                                        layui.common.alertAutoClose("上传成功");
                                    }
                                    else {
                                        layui.common.alert(res2.message, 2);
                                    }
                                });
                            }
                        }
                        else {
                            var uploadId = layui.common.generateGuid();
                            if (layui.$('#uploadImg_' + uploadId).length > 0) {
                                uploadId = layui.common.generateGuid();
                            }
                            var menuTpl = '<div class="pay-info pay-' + uploadId + '">';
                            menuTpl += '<div class="layui-input-inline" style="width:100px;">';
                            menuTpl += '<select name="payType" class="payType" lay-verify="required" lay-search>';
                            menuTpl += '<option value="">支付方式</option>';
                            for (var j = 0; j < payTypeArr.length; j++) {
                                menuTpl += '<option value="' + payTypeArr[j].id + '" ' + (res.result.payType == payTypeArr[j].id ? 'selected="true"' : '') + '>' + payTypeArr[j].name + '</option>';
                            }
                            menuTpl += '</select>';
                            menuTpl += '</div>';
                            menuTpl += '<div class="layui-input-inline" style="width:90px;">';
                            menuTpl += '<input type="text" name="thirdPartyOrderNo" lay-verify="required" value="' + res.result.thirdPartyOrderNo + '"  placeholder="支付单号" autocomplete="off" class="layui-input thirdPartyOrderNo">';
                            menuTpl += '</div>';
                            menuTpl += '<div class="layui-input-inline" style="width:80px;">';
                            menuTpl += '<input type="text" name="payAmount" lay-verify="required" value=' + res.result.actuallyPaidAmount + ' placeholder="实付金额" autocomplete="off" class="layui-input payAmount">';
                            menuTpl += '</div>';
                            menuTpl += '<div class="layui-input-inline" style="width:auto;">';
                            menuTpl += '<button type="button" class="layui-btn layui-btn-primary " id="uploadImg_' + uploadId + '"><i class="layui-icon"></i>凭证</button>';
                            menuTpl += '</div>';
                            menuTpl += '<div class="layui-input-inline remove-pay-item" style="width:auto;">';
                            menuTpl += '<button type="button" class="layui-btn layui-btn-primary remove-pay"><i class="layui-icon"></i></button>';
                            menuTpl += '</div>';
                            menuTpl += '<div class="layui-input-inline img-list" style="width:auto;">';
                            if (res.result.transferVoucher != '') {
                                var imgList = res.result.transferVoucher.split(';');
                                for (var i = 0; i < imgList.length; i++) {
                                    menuTpl += '<div class="img-item"><span class="remove" onclick="removeImg(this)">x</span><a href="' + imgList[i] + '" target="_blank"> <img src="' + imgList[i] + '" style="max-width: 38px;max-height:35px;"></a></div>';
                                }
                            }
                            menuTpl += '</div>';
                            menuTpl += '</div>';

                            layui.$('.pay-item-list').append(menuTpl);

                            layui.uploadFile('resource', 'uploadImg_' + uploadId, '/common/file/public/put?folder=admin/order/transfer-voucher', function (res2) {
                                if (res2.isSuccess) {
                                    layui.$('.pay-' + uploadId).find('.img-list').append('<div class="img-item"><span class="remove" onclick="removeImg(this)">x</span><a href="' + res2.result + '" target="_blank"> <img src="' + res2.result + '" style="max-width: 38px;max-height:35px;"></a></div>');
                                    layui.common.alertAutoClose("上传成功");
                                }
                                else {
                                    layui.common.alert(res2.message, 2);
                                }
                            });
                        }
                        layui.form.render('select');
                        layui.productOrder.initProduct(res.result.productId, res.result.teacherName);
                        layui.productOrder.initSalerUser(res.result.saleUserId);

                        layui.productOrder.searchExternalContact(res.result.externalContactUserId);
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 获取产品订单简要信息
             * */
            getBrief: function (page) {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    setTimeout(function () { history.back(-1) }, 2000);
                    return;
                }
                layui.$('#orderId').val(id);
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/brief/get?id=' + id,
                }).then(function (res) {
                    if (res.isSuccess) {
                        //订单信息
                        if (layui.$('input[name=orderNo]').length > 0) {
                            //订单退款页
                            layui.$('input[name=orderNo]').val(res.result.orderNo);
                            layui.$('input[name=amount]').val(res.result.actuallyPaidAmount);
                        }
                        if (layui.$('#status').length > 0) {
                            //修改订单状态页
                            layui.$('#status').val(res.result.status);
                            layui.$('#remark').val(res.result.remark);
                            layui.form.render('select');
                        }
                        if (layui.$('#signFlowStatus').length > 0) {
                            //审核订单页
                            layui.$('#signFlowStatus').val(res.result.signFlowStatus);
                            layui.$('#auditStatus').val(res.result.auditStatus);
                            layui.$('#auditRemark').val(res.result.auditRemark);
                            layui.form.render('select');
                        }
                        if (page == 'update-pay') {
                            //修改支付状态订单页
                            layui.$('input[name=orderNo]').val(res.result.orderNo);
                            layui.$('#financialRemark').val(res.result.financialRemark);
                            //layui.$('#payType').val(res.result.payType);
                            if (res.result.actuallyPaidAmount > 0) {
                                layui.$('#payableAmount').val(res.result.actuallyPaidAmount);
                            }
                            else {
                                layui.$('#payableAmount').val(res.result.payableAmount);
                            }
                            if (res.result.payTime != '') {
                                layui.$('#payTime').val(layui.common.timeFormat(res.result.payTime));
                            }
                            layui.form.render('select');

                            var payTypeArr = layui.$.grep(layui.setter.payTypeArr, function (item) {
                                return item.id != 300;
                            });
                            payTypeArr.unshift({ id: '', name: '请选择支付方式' });

                            if (res.result.payType == 300 && res.result.payItems != null) {
                                for (var i = 0; i < res.result.payItems.length; i++) {
                                    var uploadId = layui.common.generateGuid();
                                    if (layui.$('#uploadImg_' + uploadId).length > 0) {
                                        uploadId = layui.common.generateGuid();
                                    }
                                    var menuTpl = '<div class="pay-info pay-' + uploadId + '">';
                                    menuTpl += '<div class="layui-input-inline" style="width:100px;">';
                                    menuTpl += '<select name="payType" class="payType" lay-verify="required" lay-search>';
                                    menuTpl += '<option value="">支付方式</option>';
                                    for (var j = 0; j < payTypeArr.length; j++) {
                                        menuTpl += '<option value="' + payTypeArr[j].id + '" ' + (res.result.payItems[i].payType == payTypeArr[j].id ? 'selected="true"' : '') + '>' + payTypeArr[j].name + '</option>';
                                    }
                                    menuTpl += '</select>';
                                    menuTpl += '</div>';
                                    menuTpl += '<div class="layui-input-inline" style="width:90px;">';
                                    menuTpl += '<input type="text" name="thirdPartyOrderNo" lay-verify="required" value="' + res.result.payItems[i].thirdPartyOrderNo + '"  placeholder="支付单号" autocomplete="off" class="layui-input thirdPartyOrderNo">';
                                    menuTpl += '</div>';
                                    menuTpl += '<div class="layui-input-inline" style="width:80px;">';
                                    menuTpl += '<input type="text" name="payAmount" lay-verify="required" value=' + res.result.payItems[i].payAmount + ' placeholder="实付金额" autocomplete="off" class="layui-input payAmount">';
                                    menuTpl += '</div>';
                                    menuTpl += '<div class="layui-input-inline" style="width:auto;">';
                                    menuTpl += '<button type="button" class="layui-btn layui-btn-primary " id="uploadImg_' + uploadId + '"><i class="layui-icon"></i>凭证</button>';
                                    menuTpl += '</div>';
                                    menuTpl += '<div class="layui-input-inline remove-pay-item" style="width:auto;">';
                                    menuTpl += '<button type="button" class="layui-btn layui-btn-primary remove-pay"><i class="layui-icon"></i></button>';
                                    menuTpl += '</div>';
                                    menuTpl += '<div class="layui-input-inline img-list" style="width:auto;">';
                                    if (res.result.payItems[i].transferVoucher != '') {
                                        var imgList = res.result.payItems[i].transferVoucher.split(';');
                                        for (var j = 0; j < imgList.length; j++) {
                                            menuTpl += '<div class="img-item"><span class="remove" onclick="removeImg(this)">x</span><a href="' + imgList[j] + '" target="_blank"> <img src="' + imgList[j] + '" style="max-width: 38px;max-height:35px;"></a></div>';
                                        }
                                    }
                                    menuTpl += '</div>';
                                    menuTpl += '</div>';

                                    layui.$('.pay-item-list').append(menuTpl);

                                    layui.uploadFile('resource', 'uploadImg_' + uploadId, '/common/file/public/put?folder=admin/order/transfer-voucher', function (res2) {
                                        if (res2.isSuccess) {
                                            layui.$('.pay-' + uploadId).find('.img-list').append('<div class="img-item"><span class="remove" onclick="removeImg(this)">x</span><a href="' + res2.result + '" target="_blank"> <img src="' + res2.result + '" style="max-width: 38px;max-height:35px;"></a></div>');
                                            layui.common.alertAutoClose("上传成功");
                                        }
                                        else {
                                            layui.common.alert(res2.message, 2);
                                        }
                                    });
                                }
                                layui.form.render('select');
                            }
                            else {
                                var uploadId = layui.common.generateGuid();
                                if (layui.$('#uploadImg_' + uploadId).length > 0) {
                                    uploadId = layui.common.generateGuid();
                                }
                                var menuTpl = '<div class="pay-info pay-' + uploadId + '">';
                                menuTpl += '<div class="layui-input-inline" style="width:100px;">';
                                menuTpl += '<select name="payType" class="payType" lay-verify="required" lay-search>';
                                menuTpl += '<option value="">支付方式</option>';
                                for (var j = 0; j < payTypeArr.length; j++) {
                                    menuTpl += '<option value="' + payTypeArr[j].id + '" ' + (res.result.payType == payTypeArr[j].id ? 'selected="true"' : '') + '>' + payTypeArr[j].name + '</option>';
                                }
                                menuTpl += '</select>';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:90px;">';
                                menuTpl += '<input type="text" name="thirdPartyOrderNo" lay-verify="required" value="' + res.result.thirdPartyOrderNo + '"  placeholder="支付单号" autocomplete="off" class="layui-input thirdPartyOrderNo">';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:80px;">';
                                menuTpl += '<input type="text" name="payAmount" lay-verify="required" value=' + res.result.actuallyPaidAmount + ' placeholder="实付金额" autocomplete="off" class="layui-input payAmount">';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:auto;">';
                                menuTpl += '<button type="button" class="layui-btn layui-btn-primary " id="uploadImg_' + uploadId + '"><i class="layui-icon"></i>凭证</button>';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline remove-pay-item" style="width:auto;">';
                                menuTpl += '<button type="button" class="layui-btn layui-btn-primary remove-pay"><i class="layui-icon"></i></button>';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline img-list" style="width:auto;">';
                                if (res.result.transferVoucher != null && res.result.transferVoucher != '') {
                                    var imgList = res.result.transferVoucher.split(';');
                                    for (var i = 0; i < imgList.length; i++) {
                                        menuTpl += '<div class="img-item"><span class="remove" onclick="removeImg(this)">x</span><a href="' + imgList[i] + '" target="_blank"> <img src="' + imgList[i] + '" style="max-width: 38px;max-height:35px;"></a></div>';
                                    }
                                }
                                menuTpl += '</div>';
                                menuTpl += '</div>';

                                layui.$('.pay-item-list').append(menuTpl);
                                layui.form.render('select');

                                layui.uploadFile('resource', 'uploadImg_' + uploadId, '/common/file/public/put?folder=admin/order/transfer-voucher', function (res2) {
                                    if (res2.isSuccess) {
                                        layui.$('.pay-' + uploadId).find('.img-list').append('<div class="img-item"><span class="remove" onclick="removeImg(this)">x</span><a href="' + res2.result + '" target="_blank"> <img src="' + res2.result + '" style="max-width: 38px;max-height:35px;"></a></div>');
                                        layui.common.alertAutoClose("上传成功");
                                    }
                                    else {
                                        layui.common.alert(res2.message, 2);
                                    }
                                });
                            }
                        }
                        if (page == 'audit-financial') {
                            layui.$('#customerInfo').text(res.result.customerName + '（' + res.result.customerMobile + '）');
                            layui.$('#productInfo').text(res.result.productName);
                            layui.$('#orderInfo').text(res.result.orderNo + ' - ¥' + res.result.actuallyPaidAmount);

                            if (res.result.payTime != '') {
                                layui.$('#payTime').val(layui.common.timeFormat(res.result.payTime));
                            }
                            layui.$('#financialRemark').val(res.result.financialRemark);
                            var payTypeArr = layui.$.grep(layui.setter.payTypeArr, function (item) {
                                return item.id != 300;
                            });
                            payTypeArr.unshift({ id: '', name: '请选择支付方式' });

                            if (res.result.payType == 300 && res.result.payItems != null) {
                                for (var i = 0; i < res.result.payItems.length; i++) {
                                    var menuTpl = '<div class="pay-info">';
                                    menuTpl += '<div class="layui-input-inline" style="width:150px;">';
                                    menuTpl += '<select name="payType" class="payType" disabled="disabled" lay-search>';
                                    menuTpl += '<option value="">请选择支付方式</option>';
                                    for (var j = 0; j < payTypeArr.length; j++) {
                                        var isChecked = false;
                                        if (res.result.payType != 300) {
                                            if (res.result.payType == payTypeArr[j].id) {
                                                isChecked = true;
                                            }
                                        }
                                        else {
                                            if (res.result.payItems[i].payType == payTypeArr[j].id) {
                                                isChecked = true;
                                            }
                                        }
                                        menuTpl += '<option value="' + payTypeArr[j].id + '" ' + (isChecked ? 'selected="true"' : '') + '>' + payTypeArr[j].name + '</option>';
                                    }
                                    menuTpl += '</select>';
                                    menuTpl += '</div>';
                                    menuTpl += '<div class="layui-input-inline" style="width:240px;">';
                                    menuTpl += '<input type="text" name="thirdPartyOrderNo" disabled="disabled" value="' + res.payItems[i].thirdPartyOrderNo + '"  placeholder="支付单号" autocomplete="off" class="layui-input thirdPartyOrderNo">';
                                    menuTpl += '</div>';
                                    menuTpl += '<div class="layui-input-inline" style="width:80px;">';
                                    menuTpl += '<input type="text" name="payAmount" disabled="disabled" value=' + res.result.payItems[i].payAmount + ' placeholder="实付金额" autocomplete="off" class="layui-input payAmount">';
                                    menuTpl += '</div>';
                                    menuTpl += '<div class="layui-input-inline img-list" style="width:auto;">';
                                    if (res.result.payItems[i].transferVoucher != '') {
                                        var imgList = res.result.payItems[i].transferVoucher.split(';');
                                        for (var j = 0; j < imgList.length; j++) {
                                            menuTpl += '<div class="img-item"><a href="' + imgList[j] + '" target="_blank"> <img src="' + imgList[j] + '" style="max-width: 38px;max-height:35px;"></a></div>';
                                        }
                                    }
                                    menuTpl += '</div>';
                                    menuTpl += '</div>';

                                    layui.$('.pay-item-list').append(menuTpl);
                                }
                                layui.form.render('select');
                            }
                            else {
                                var menuTpl = '<div class="pay-info">';
                                menuTpl += '<div class="layui-input-inline" style="width:150px;">';
                                menuTpl += '<select name="payType" class="payType" disabled="disabled" lay-search>';
                                menuTpl += '<option value="">请选择支付方式</option>';
                                for (var j = 0; j < payTypeArr.length; j++) {
                                    menuTpl += '<option value="' + payTypeArr[j].id + '" ' + (res.result.payType == payTypeArr[j].id ? 'selected="true"' : '') + '>' + payTypeArr[j].name + '</option>';
                                }
                                menuTpl += '</select>';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:240px;">';
                                menuTpl += '<input type="text" name="thirdPartyOrderNo" disabled="disabled" value="' + res.result.thirdPartyOrderNo + '"  placeholder="支付单号" autocomplete="off" class="layui-input thirdPartyOrderNo">';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="width:80px;">';
                                menuTpl += '<input type="text" name="payAmount" disabled="disabled" value=' + res.result.actuallyPaidAmount + ' placeholder="实付金额" autocomplete="off" class="layui-input payAmount">';
                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline img-list" style="width:auto;">';
                                if (res.result.transferVoucher != null && res.result.transferVoucher != '') {
                                    var imgList = res.result.transferVoucher.split(';');
                                    for (var i = 0; i < imgList.length; i++) {
                                        menuTpl += '<div class="img-item"><a href="' + imgList[i] + '" target="_blank"> <img src="' + imgList[i] + '" style="max-width: 38px;max-height:35px;"></a></div>';
                                    }
                                }
                                menuTpl += '</div>';
                                menuTpl += '</div>';

                                layui.$('.pay-item-list').append(menuTpl);
                                layui.form.render('select');
                            }
                        }
                        if (page == 'audit-compliance') {
                            layui.$('#customerInfo').text(res.result.customerName + '（' + res.result.customerMobile + '）');
                            layui.$('#productInfo').text(res.result.productName);
                            layui.$('#orderInfo').text(res.result.orderNo + ' - ¥' + res.result.actuallyPaidAmount);
                            layui.$('#auditRemark').val(res.result.auditRemark);
                        }
                        if (page == 'audit-fengkong') {
                            layui.$('#customerInfo').text(res.result.customerName + '（' + res.result.customerMobile + '）');
                            layui.$('#productInfo').text(res.result.productName);
                            layui.$('#orderInfo').text(res.result.orderNo + ' - ¥' + res.result.actuallyPaidAmount);
                            layui.$('#auditRemark').val(res.result.fkAuditRemark);
                            if (res.result.fkAuditFileUrl != null && res.result.fkAuditFileUrl != '') {
                                var fileArr = res.result.fkAuditFileUrl.split(';');
                                if (fileArr.length > 0) {
                                    for (var i = 0; i < fileArr.length; i++) {
                                        var urlArr = fileArr[i].split('/');
                                        var name = urlArr[urlArr.length - 1];
                                        layui.$('.file-list').append('<li id="li_' + i + '"><a href="' + fileArr[i] + '" target="_blank" title="' + name + '"><i class="layui-icon layui-icon-link"></i> ' + name + '</a><i class="layui-icon layui-icon-delete remove" onclick="layui.$(this).parent(\'li\').remove()"></i></li>');
                                    }
                                }
                            }
                        }
                        if (page == 'apply-refund') {
                            layui.$('#customerInfo').text(res.result.customerName + '（' + res.result.customerMobile + '）');
                            layui.$('#productInfo').text(res.result.productName);
                            layui.$('#orderInfo').text(res.result.orderNo + ' - ¥' + res.result.actuallyPaidAmount);
                            layui.$('#amount').val(res.result.actuallyPaidAmount);
                        }
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
            * 通过id获取待审核的退款订单信息
            * */
            getAuditOrder: function () {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    setTimeout(function () { history.back(-1) }, 2000);
                    return;
                }
                layui.$('#orderId').val(id);
                layui.request({
                    method: 'post',
                    url: '/admin/product/refund-order/audit/get?orderId=' + id,
                }).then(function (res) {
                    if (res.isSuccess) {
                        //订单信息
                        layui.$('input[name=orderNo]').val(res.result.orderNo);
                        layui.$('input[name=payAmount]').val(res.result.payAmount);
                        layui.$('input[name=amount]').val(res.result.refundAmount);
                        layui.$('#remark').val(res.result.remark);
                    }
                    else {
                        parent.layui.common.closeType('iframe');
                        parent.layui.common.alertAutoClose(res.message);
                    }
                })
            },
            /**
            * 通过id获取退款订单信息
            * */
            getRefundOrder: function () {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    setTimeout(function () {
                        parent.location.reload();
                    }, 2000);
                    return;
                }
                layui.request({
                    method: 'post',
                    url: '/admin/product/refund-order/get?id=' + id,
                }).then(function (res) {
                    if (res.isSuccess) {
                        layui.$('#customerInfo').text(res.result.customerName + '（' + res.result.customerMobile + '）');
                        layui.$('#productInfo').text(res.result.productName);
                        layui.$('#orderInfo').text(res.result.productOrderNo + ' - ¥' + res.result.oriAmount);
                        if (location.href.indexOf('audit-refund-financial.html') > -1) {
                            layui.$('#amount').text(res.result.amount.toFixed(2));
                            layui.$('#bankCardNo').text(res.result.bankCardNo);
                            layui.$('#openBankBranch').text(res.result.openBankBranch);
                            layui.$('#refundType').val(res.result.refundType);
                            if (res.result.refundType == 10 || res.result.refundType == 11 || res.result.refundType == 20) {
                                if (res.result.subsidiaryId.indexOf('667a54cdde066ed3a180a342') <= -1) {
                                    layui.$('.auto-refund').hide();
                                    layui.$('.sg-refund').show();
                                    layui.$('#status').val(3);
                                }
                            }
                            layui.form.render('select');
                        }
                        else {
                            layui.$('#amount').val(res.result.amount);
                            layui.$('#bankCardNo').val(res.result.bankCardNo);
                            layui.$('#openBankBranch').val(res.result.openBankBranch);
                            layui.$('#remark').val(res.result.remark);
                            layui.$('#auditRemark').val(res.result.auditRemark);
                        }
                    }
                    else {
                        parent.layui.common.closeType('iframe');
                        parent.layui.common.alertAutoClose(res.message);
                    }
                })
            },

            /**
             * 获取支付/签署地址
             * @param {any} type
             */
            getUrl: function (type) {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    layui.common.alertAutoClose("参数有误");
                    setTimeout(function () { history.back(-1) }, 2000);
                    return;
                }
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/url/get?id=' + id,
                    data: JSON.stringify({
                        id: id,
                        type: type
                    }),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        layui.common.alert((type == 1 ? '支付' : '签署') + '地址：' + res.result, -1);
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 获取订单金额
             * @param {any} data
             */
            getAmonut: function (data) {
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/amount/get',
                    data: JSON.stringify(data),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        layui.$('.totalAmount').text(res.result.totalAmount);
                        layui.$('.payAmount').text(res.result.payAmount);
                        layui.$('.refundAmount').text(res.result.refundAmount);
                        layui.$('.waitConfirmAmount').text(res.result.waitConfirmAmount);
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 获取退款订单金额
             * @param {any} data
             */
            getRefundOrderAmount: function (data) {
                layui.request({
                    method: 'post',
                    url: '/admin/product/refund-order/amount/get',
                    data: JSON.stringify(data),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        layui.$('.refundAmount').text(res.result);
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 获取订单支付金额统计列表（产品维度）
             * */
            queryStatForProduct: function () {
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/stat-product/query',
                    data: JSON.stringify({ startTime: layui.$('#startTime').val(), endTime: layui.$('#endTime').val() }),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        var opTable = layui.opTable.render({
                            elem: '#stat-list'
                            , id: '#stat-list'
                            , limit: 500
                            , data: res.result
                            , cols: [[
                                {
                                    field: 'teacherName', title: '投顾名称', templet: function (e) {
                                        return e.teacherName == '' ? '-' : e.teacherName;
                                    }
                                },
                                { field: 'successCount', title: '支付成功订单数' },
                                { field: 'confirmCount', title: '待确认订单数' },
                                { field: 'successAmount', title: '支付成功总金额' },
                                { field: 'confirmAmount', title: '待确认总金额' },
                                { field: 'auditSuccessCount', title: '支付成功已审核订单数' }
                            ]],
                            openTable: function (itemData) {
                                return {
                                    elem: '#child_1_' + itemData.LAY_INDEX
                                    , id: 'child_1_' + itemData.LAY_INDEX
                                    , skin: 'nob'
                                    , even: 'true'
                                    , limit: 500
                                    , data: itemData.childs
                                    , page: false
                                    , openVisible: false
                                    , cols: [[
                                        { field: 'productName', title: '产品名称' },
                                        { field: 'successCount', title: '支付成功订单数' },
                                        { field: 'confirmCount', title: '待确认订单数' },
                                        { field: 'successAmount', title: '支付成功总金额' },
                                        { field: 'confirmAmount', title: '待确认总金额' },
                                        { field: 'auditSuccessCount', title: '支付成功已审核订单数' }
                                    ]]
                                }
                            }
                        });
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
           * 获取订单支付金额统计列表（加粉方案维度）
           * */
            queryStatForKfCase: function () {
                var fields = [
                    { field: 'kfCaseName', title: '加粉方案名称' },
                    { field: 'successCount', title: '支付成功订单数' },
                    { field: 'confirmCount', title: '待确认订单数' },
                    { field: 'successAmount', title: '支付成功总金额' },
                    { field: 'confirmAmount', title: '待确认总金额' },
                    { field: 'auditSuccessCount', title: '支付成功已审核订单数' }
                ];
                layui.tableRequest.request('resource', false, 'stat-kfcase-list', '/admin/product/order/stat-kfcase/query', 'application/json', fields, { startTime: layui.$('#startTime').val(), endTime: layui.$('#endTime').val() });
            },
            /**
              * 获取订单数据统计详情（加粉方案维度）
              * */
            queryStatDetailForKfCase: function () {
                var workWxAppId = layui.common.getUrlParam('workWxAppId') || '';
                var fields = [
                    { field: 'kfCaseName', title: '渠道', minWidth: 120, rowspan: 2 },
                    {
                        field: 'consumptionAmount', title: '消费总金额', minWidth: 100, align: 'center', rowspan: 2, templet: function (e) {
                            return e.consumptionAmount == 0 ? '-' : e.consumptionAmount;
                        }
                    },
                    { title: '资源信息', align: 'center', colspan: 3 },
                    { title: '首单', align: 'center', colspan: 4 },
                    { title: '续费', align: 'center', colspan: 4 },

                    {
                        field: 'totalAmount', title: '总开单金额', minWidth: 110, align: 'center', rowspan: 2, templet: function (e) {
                            return e.totalAmount == 0 ? '-' : e.totalAmount;
                        }
                    },
                    {
                        field: 'refundAmount', title: '总退款金额', minWidth: 110, align: 'center', rowspan: 2, templet: function (e) {
                            return e.refundAmount == 0 ? '-' : e.refundAmount;
                        }
                    },
                    {
                        field: 'refundRate', title: '退款率', minWidth: 110, align: 'center', rowspan: 2, templet: function (e) {
                            return e.refundRate == 0 ? '-' : e.refundRate + '%';
                        }
                    },
                    {
                        field: 'netProfit', title: '净业绩', minWidth: 110, align: 'center', rowspan: 2, templet: function (e) {
                            return e.netProfit == 0 ? '-' : e.netProfit;
                        }
                    },
                    {
                        field: 'roi', title: 'ROI', minWidth: 60, align: 'center', rowspan: 2, templet: function (e) {
                            return e.roi == 0 ? '-' : e.roi;
                        }
                    }
                ];
                var subFields = [
                    {
                        field: 'resourceCount', title: '资源数', minWidth: 100, align: 'center', templet: function (e) {
                            return e.resourceCount == 0 ? '-' : e.resourceCount;
                        }
                    },
                    {
                        field: 'deleteRate', title: '24小时删除率', minWidth: 120, align: 'center', templet: function (e) {
                            return e.deleteRate == 0 ? '-' : e.deleteRate + '%';
                        }
                    },
                    {
                        field: 'replyRate', title: '24小时回复率', minWidth: 120, align: 'center', templet: function (e) {
                            return e.replyRate == 0 ? '-' : e.replyRate + '%';
                        }
                    }, {
                        field: 'firstCount', title: '开单数', minWidth: 80, align: 'center', templet: function (e) {
                            return e.firstCount == 0 ? '-' : e.firstCount;
                        }
                    },
                    {
                        field: 'firstRate', title: '开发率', minWidth: 80, align: 'center', templet: function (e) {
                            return e.firstRate == 0 ? '-' : e.firstRate + '%';
                        }
                    },
                    {
                        field: 'firstAmount', title: '开单金额', minWidth: 90, align: 'center', templet: function (e) {
                            return e.firstAmount == 0 ? '-' : e.firstAmount;
                        }
                    },
                    {
                        field: 'firstAverageAmount', title: '均价', minWidth: 90, align: 'center', templet: function (e) {
                            return e.firstAverageAmount == 0 ? '-' : e.firstAverageAmount;
                        }
                    },
                    {
                        field: 'renewalCount', title: '开单数', minWidth: 80, align: 'center', templet: function (e) {
                            return e.renewalCount == 0 ? '-' : e.renewalCount;
                        }
                    },
                    {
                        field: 'renewalRate', title: '开发率', minWidth: 80, align: 'center', templet: function (e) {
                            return e.renewalRate == 0 ? '-' : e.renewalRate + '%';
                        }
                    },
                    {
                        field: 'renewalAmount', title: '开单金额', minWidth: 90, templet: function (e) {
                            return e.renewalAmount == 0 ? '-' : e.renewalAmount;
                        }
                    },
                    {
                        field: 'renewalAverageAmount', title: '均价', minWidth: 90, align: 'center', templet: function (e) {
                            return e.renewalAverageAmount == 0 ? '-' : e.renewalAverageAmount;
                        }
                    }];

                layui.tableRequest.request('resource', false, 'stat-kfcase-list', '/admin/product/order/stat-detail/get', 'application/json', fields,
                    { companyId: layui.setter.productCompanyId, workWxAppId: workWxAppId, startTime: layui.$('#startTime').val(), endTime: layui.$('#endTime').val() }, null, null, subFields);
            },
            /**
             * 获取产品订单列表
             * */
            query: function () {
                var aUserStr = localStorage.getItem('ly-admin-user');
                if (aUserStr != null) {
                    var aUserObj = JSON.parse(aUserStr);
                    curUser = aUserObj.id;
                }
                var data = { companyId: layui.setter.productCompanyId };
                //合规账号/财务账号
                if (curUser == '640afb7aaa622188d712b115' || curUser == '640afa5caa622188d712b114') {
                    //只显示已支付
                    layui.$('#payStatus').val(100);
                    data.payStatus = 100;
                    layui.$('.pay-status-item').hide();
                    layui.$('.amount-desc').hide();
                    if (curUser == '640afb7aaa622188d712b115') {
                        //合规显示三月份以后订单
                        data.startTime = '2023-04-01 00:00:00';
                        layui.$('#startTime').val(data.startTime);
                    }
                }
                layui.tableRequest.request('resource', true, 'product-order-list', '/admin/product/order/query', 'application/json', [
                    { type: 'checkbox', fixed: 'left' },
                    { field: 'id', title: 'ID', fixed: 'left' },
                    { field: 'orderNo', title: '订单编号', width: 210 },
                    { field: 'customerName', title: '客户姓名', width: 100, edit: layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.customername.update') },
                    { field: 'productName', title: '购买产品', minWidth: 190 },
                    {
                        field: 'payTime', title: '支付状态', width: 110, sort: true, templet: function (e) {
                            var res = e.payStatus;
                            if (e.payStatus == '支付成功' && e.refundStatus != '未退款') {
                                res = e.refundStatus;
                                if (e.refundTime != null) {
                                    res += '-' + layui.common.timeFormat(e.refundTime);
                                }
                                else {
                                    if (e.payTime != null) {
                                        res += '-' + layui.common.timeFormat(e.payTime);
                                    }
                                }
                            }
                            if (e.payStatus == "驳回" && e.financialRemark != null && e.financialRemark != '') {
                                res += '（' + e.financialRemark + '）'
                            }
                            return (curUser == '640afb7aaa622188d712b115' && e.payStatus == '待确认') ? '<span style="color:#f00">' + res + '</span>' : res;
                        }
                    },
                    { field: 'signFlowStatus', title: '签署状态', width: 100 },
                    { field: 'teacherName', title: '投资顾问', minWidth: 90 },
                    { field: 'channelName', title: '来源渠道', width: 100 },
                    { field: 'buyType', title: '类型', width: 60 },
                    {
                        title: '分配用户', width: 100, templet: function (e) {
                            return e.saleUserName != "" ? e.saleUserName : '-';
                        }
                    },
                    { field: 'departmentName', title: '所属部门', minWidth: 150 },
                    { field: 'progress', title: '当前进度', minWidth: 100 },
                    { field: 'payableAmount', title: '应付', width: 100 },
                    { field: 'actuallyPaidAmount', title: '实付', width: 100 },
                    {
                        field: 'payType', title: '支付方式', width: 180, templet: function (e) {
                            var payType = e.payType;
                            if (e.transferVoucher != null && e.transferVoucher != '') {
                                payType += ' <a style="color:#3c92d5" onclick="layui.productOrder.openPreview(\'' + e.transferVoucher + '\')" href="javascript:;">[转账凭证]</a>'
                            }
                            return payType;
                        }
                    },
                    //{
                    //    field: 'refundTime', title: '退款状态', width: 110, sort: true, templet: function (e) {
                    //        var res = e.refundStatus;
                    //        if (e.refundTime != null) {
                    //            res += '-' + layui.common.timeFormat(e.refundTime)
                    //        }
                    //        return res;
                    //    }
                    //},
                    { field: 'callStatusDesc', title: '智能拨打状态', width: 120 },
                    { field: 'intentionName', title: '智能拨打结果', width: 120 },
                    { field: 'financialAuditStatus', title: '财务审核', width: 120 },
                    { field: 'auditStatus', title: '合规审核', width: 120 },
                    { field: 'fkAuditStatus', title: '质检意见', width: 120 },
                    {
                        field: 'serviceTime', title: '服务期限', width: 200, sort: true, templet: function (e) {
                            return layui.common.timeFormat(e.serviceStartTime).split(' ')[0] + ' 至 ' + layui.common.timeFormat(e.serviceEndTime).split(' ')[0];
                        }
                    },
                    {
                        field: 'surplusValue', title: '剩余价值', width: 100, templet: function (e) {
                            return e.surplusValue == 0 ? '-' : e.surplusValue;
                        }
                    },
                    {
                        fixed: 'right', field: 'createdAt', title: '创建时间', width: 170, sort: true, templet: function (e) {
                            return layui.common.timeFormat(e.createdAt);
                        }
                    },
                    { fixed: 'right', title: '操作', width: 140, align: 'left', toolbar: layui.setter.productCompanyId == '6336a5e0e23662d2a9cafec5' ? '#product-order-bar' : '#product-order-bar-dn' }
                ], data, '#topToolBar', 'full-250', null, function (res, curr, count) {
                    layui.dropdown.suite();
                });
                //监听表格事件
                layui.productOrder.tableEvent();
                layui.productOrder.getAmonut(data);
            },
            /**
             * 获取产品订单列表（合规审核）
             * */
            queryForCompliance: function () {
                var auditType = layui.common.getUrlParam('auditType');
                if (auditType == 2) {
                    layui.$('#auditStatus').append('<option value="2">通过</option><option value="3">不通过</option><option value="6">无法接通</option>');
                }
                else {
                    layui.$('#auditStatus').append('<option value="1">待处理</option><option value="4">驳回</option><option value="5">未接通</option>');
                }
                layui.form.render('select');
                layui.tableRequest.request('resource', true, 'product-order-list', '/admin/product/order/compliance/query', 'application/json', [
                    { type: 'checkbox', fixed: 'left' },
                    { field: 'id', title: 'ID', fixed: 'left' },
                    { field: 'orderNo', title: '订单编号', width: 210 },
                    { field: 'customerName', title: '客户姓名', width: 100 },
                    { field: 'productName', title: '购买产品', minWidth: 190 },
                    {
                        field: 'payTime', title: '支付状态', width: 110, sort: true, templet: function (e) {
                            var res = e.payStatus;
                            if (e.payStatus == '支付成功' && e.refundStatus != '未退款') {
                                res = e.refundStatus;
                                if (e.refundTime != null) {
                                    res += '-' + layui.common.timeFormat(e.refundTime);
                                }
                                else {
                                    if (e.payTime != null) {
                                        res += '-' + layui.common.timeFormat(e.payTime);
                                    }
                                }
                            }

                            if (e.payStatus == "驳回" && e.financialRemark != null && e.financialRemark != '') {
                                res += '（' + e.financialRemark + '）'
                            }
                            return res;
                        }
                    },
                    { field: 'signFlowStatus', title: '签署状态', width: 100 },
                    {
                        title: '分配用户', width: 100, templet: function (e) {
                            return e.saleUserName != "" ? e.saleUserName : '-';
                        }
                    },
                    { field: 'departmentName', title: '所属部门', minWidth: 150 },
                    { field: 'payableAmount', title: '应付', width: 100 },
                    { field: 'actuallyPaidAmount', title: '实付', width: 100 },
                    {
                        field: 'payType', title: '支付方式', width: 180, templet: function (e) {
                            var payType = e.payType;
                            if (e.transferVoucher != null && e.transferVoucher != '') {
                                payType += ' <a style="color:#3c92d5" onclick="layui.productOrder.openPreview(\'' + e.transferVoucher + '\')" href="javascript:;">[转账凭证]</a>'
                            }
                            return payType;
                        }
                    },
                    {
                        field: 'questionUrl', title: '回访链接', width: 120, templet: function (e) {
                            var questionUrl = e.questionUrl || "-";
                            if (e.questionUrl != null && e.questionUrl != '') {
                                questionUrl = ' <a style="color:#3c92d5" href="' + e.questionUrl + '" target="_blank">回访链接</a>'
                            }
                            return questionUrl;
                        }
                    },
                    { field: 'callStatusDesc', title: '智能拨打状态', width: 120 },
                    { field: 'intentionName', title: '智能拨打结果', width: 120 },
                    {
                        field: 'callTime', title: '智能拨打时间', width: 170, templet: function (e) {
                            return e.callTime != null ? layui.common.timeFormat(e.callTime) : '-';
                        }
                    },
                    { field: 'auditStatusDesc', title: '合规审核', width: 120 },
                    { field: 'callUserName', title: '回访人', width: 90 },
                    { field: 'fkAuditStatusDesc', title: '质检意见', width: 120 },
                    {
                        title: '服务期限', width: 200, templet: function (e) {
                            return layui.common.timeFormat(e.serviceStartTime).split(' ')[0] + ' 至 ' + layui.common.timeFormat(e.serviceEndTime).split(' ')[0];
                        }
                    },
                    {
                        fixed: 'right', field: 'createdAt', title: '创建时间', width: 170, sort: true, templet: function (e) {
                            return layui.common.timeFormat(e.createdAt);
                        }
                    },
                    { fixed: 'right', title: '操作', width: auditType == 2 ? 220 : 270, align: 'left', toolbar: '#product-order-bar' }
                ], { companyId: layui.setter.productCompanyId, auditType: auditType }, null, 'full-145');
            },
            /**
           * 获取产品订单列表（风控审核）
           * */
            queryForFK: function () {
                var auditType = layui.common.getUrlParam('auditType');
                if (auditType != 2) {
                    layui.$('#fkAuditStatusItem').hide();
                }
                layui.tableRequest.request('resource', true, 'product-order-list', '/admin/product/order/fk/query', 'application/json', [
                    { type: 'checkbox', fixed: 'left' },
                    { field: 'id', title: 'ID', fixed: 'left' },
                    { field: 'orderNo', title: '订单编号', width: 210 },
                    { field: 'customerName', title: '客户姓名', width: 100 },
                    { field: 'productName', title: '购买产品', minWidth: 190 },
                    {
                        field: 'payTime', title: '支付状态', width: 110, sort: true, templet: function (e) {
                            var res = e.payStatus;
                            if (e.payStatus == '支付成功' && e.refundStatus != '未退款') {
                                res = e.refundStatus;
                                if (e.refundTime != null) {
                                    res += '-' + layui.common.timeFormat(e.refundTime);
                                }
                                else {
                                    if (e.payTime != null) {
                                        res += '-' + layui.common.timeFormat(e.payTime);
                                    }
                                }
                            }

                            if (e.payStatus == "驳回" && e.financialRemark != null && e.financialRemark != '') {
                                res += '（' + e.financialRemark + '）'
                            }
                            return res;
                        }
                    },
                    { field: 'signFlowStatus', title: '签署状态', width: 100 },
                    {
                        title: '分配用户', width: 100, templet: function (e) {
                            return e.saleUserName != "" ? e.saleUserName : '-';
                        }
                    },
                    { field: 'departmentName', title: '所属部门', minWidth: 150 },
                    { field: 'payableAmount', title: '应付', width: 100 },
                    { field: 'actuallyPaidAmount', title: '实付', width: 100 },
                    {
                        field: 'payType', title: '支付方式', width: 180, templet: function (e) {
                            var payType = e.payType;
                            if (e.transferVoucher != null && e.transferVoucher != '') {
                                payType += ' <a style="color:#3c92d5" onclick="layui.productOrder.openPreview(\'' + e.transferVoucher + '\')" href="javascript:;">[转账凭证]</a>'
                            }
                            return payType;
                        }
                    },
                    { field: 'callStatusDesc', title: '智能拨打状态', width: 120 },
                    { field: 'intentionName', title: '智能拨打结果', width: 120 },
                    { field: 'callUserName', title: '回访人', width: 90 },
                    { field: 'fkAuditStatusDesc', title: '质检意见', width: 120 },
                    {
                        title: '服务期限', width: 200, templet: function (e) {
                            return layui.common.timeFormat(e.serviceStartTime).split(' ')[0] + ' 至 ' + layui.common.timeFormat(e.serviceEndTime).split(' ')[0];
                        }
                    },
                    {
                        fixed: 'right', field: 'createdAt', title: '创建时间', width: 170, sort: true, templet: function (e) {
                            return layui.common.timeFormat(e.createdAt);
                        }
                    },
                    { fixed: 'right', title: '操作', width: 120, align: 'left', toolbar: '#product-order-bar' }
                ], { companyId: layui.setter.productCompanyId, auditType: auditType }, null, 'full-145');
            },
            /**
            * 获取产品订单列表（财务审核）
            * */
            queryForFinancial: function () {
                var auditType = layui.common.getUrlParam('auditType');
                layui.tableRequest.request('resource', true, 'product-order-list', '/admin/product/order/financial/query', 'application/json', [
                    { type: 'checkbox', fixed: 'left' },
                    { field: 'id', title: 'ID', fixed: 'left' },
                    { field: 'orderNo', title: '订单编号', width: 210 },
                    { field: 'customerName', title: '客户姓名', width: 100 },
                    { field: 'productName', title: '购买产品', minWidth: 190 },
                    {
                        field: 'payTime', title: '支付状态', width: 110, sort: true, templet: function (e) {
                            var res = e.payStatus;
                            if (e.payStatus == '支付成功' && e.refundStatus != '未退款') {
                                res = e.refundStatus;
                                if (e.refundTime != null) {
                                    res += '-' + layui.common.timeFormat(e.refundTime);
                                }
                                else {
                                    if (e.payTime != null) {
                                        res += '-' + layui.common.timeFormat(e.payTime);
                                    }
                                }
                            }
                            if (e.payStatus == "驳回" && e.financialRemark != null && e.financialRemark != '') {
                                res += '（' + e.financialRemark + '）'
                            }
                            return res;
                        }
                    },
                    {
                        title: '分配用户', width: 100, templet: function (e) {
                            return e.saleUserName != "" ? e.saleUserName : '-';
                        }
                    },
                    { field: 'departmentName', title: '所属部门', minWidth: 150 },
                    { field: 'payableAmount', title: '应付', width: 100 },
                    { field: 'actuallyPaidAmount', title: '实付', width: 100 },
                    {
                        field: 'payType', title: '支付方式', width: 180, templet: function (e) {
                            var payType = e.payType;
                            if (e.transferVoucher != null && e.transferVoucher != '') {
                                payType += ' <a style="color:#3c92d5" onclick="layui.productOrder.openPreview(\'' + e.transferVoucher + '\')" href="javascript:;">[转账凭证]</a>'
                            }
                            return payType;
                        }
                    },
                    { field: 'financialAuditStatusDesc', title: '财务审核状态', width: 120 },
                    {
                        fixed: 'right', field: 'createdAt', title: '创建时间', width: 170, sort: true, templet: function (e) {
                            return layui.common.timeFormat(e.createdAt);
                        }
                    },
                    { fixed: 'right', title: '操作', width: 120, align: 'left', toolbar: '#product-order-bar' }
                ], { companyId: layui.setter.productCompanyId, auditType: auditType }, auditType == 1 ? '#topToolBar' : '', 'full-145');
                //监听表格事件
                layui.productOrder.tableEvent();
            },
            /**
             * 获取产品订单列表（客户回访）
             * */
            queryForFollow: function () {
                var type = layui.common.getUrlParam('type');
                var fields = [
                    { type: 'checkbox', fixed: 'left' },
                    { field: 'id', title: 'ID', fixed: 'left' },
                    { field: 'orderNo', title: '订单编号', width: 210 },
                    { field: 'customerName', title: '客户姓名', width: 100 },
                    { field: 'productName', title: '购买产品', minWidth: 190 },
                    { field: 'payableAmount', title: '应付', width: 100 },
                    { field: 'actuallyPaidAmount', title: '实付', width: 100 },
                    {
                        title: '服务期限', width: 200, templet: function (e) {
                            return layui.common.timeFormat(e.serviceStartTime).split(' ')[0] + ' 至 ' + layui.common.timeFormat(e.serviceEndTime).split(' ')[0];
                        }
                    }
                ]
                if (type == 2) {
                    fields.push({ fixed: 'right', field: 'callStatusDesc', title: '智能拨打状态', width: 120 });
                    fields.push({ fixed: 'right', field: 'intentionName', title: '智能拨打结果', width: 120 });
                    layui.$('.handled').removeClass('layui-hide');
                }
                fields.push({
                    fixed: 'right', field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                });
                fields.push({ fixed: 'right', title: '操作', width: 120, align: 'left', toolbar: '#product-order-bar' + (type == 1 ? '' : '2') })
                layui.tableRequest.request('resource', true, 'product-order-list', '/admin/product/order/followup/query', 'application/json', fields,
                    { type: type, callStatus: -1 }, type == 1 ? '#topToolBar' : '', 'full-145');
            },
            /**
             * 获取退款订单列表
             * */
            queryRefundOrder: function () {
                var data = { companyId: layui.setter.productCompanyId };
                layui.tableRequest.request('resource', true, 'refund-order-list', '/admin/product/refund-order/query', 'application/json', [
                    { field: 'id', title: 'ID', fixed: 'left' },
                    { field: 'productOrderNo', title: '支付单号', width: 210 },
                    { field: 'productName', title: '购买产品', minWidth: 150 },
                    { field: 'refundOrderNo', title: '退款单号', width: 210 },
                    { field: 'thirdPartyOrderNo', title: '第三方单号(微信/支付宝)', width: 210 },
                    { field: 'oriAmount', title: '订单金额', width: 100 },
                    { field: 'amount', title: '退款金额', width: 100 },
                    {
                        title: '退款状态', width: 100, templet: function (e) {
                            var desc = e.statusDesc;
                            if (e.successTime != null) {
                                desc += ' - ' + layui.common.timeFormat(e.successTime);
                            }
                            return desc;
                        }
                    },
                    { field: 'refundTypeDesc', title: '退款方式', width: 150 },
                    { field: 'reason', title: '退款原因', width: 100 },
                    { field: 'remark', title: '备注', width: 140 },
                    { field: 'opUserName', title: '操作用户', width: 100 },

                    {
                        fixed: 'right', field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                            return layui.common.timeFormat(e.createdAt);
                        }
                    }
                ], data, '#topToolBar');
                layui.productOrder.getRefundOrderAmount(data);
            },
            /**
            * 获取退款订单列表（风控审核）
            * */
            queryRefundOrderForAudit: function () {
                var auditType = layui.common.getUrlParam('auditType');
                var fields = [
                    { field: 'id', title: 'ID', fixed: 'left' },
                    { field: 'productOrderNo', title: '支付单号', width: 210 },
                    { field: 'customerName', title: '姓名', minWidth: 80 },
                    { field: 'signFlowStatusDesc', title: '签署状态', width: 100 },
                    { field: 'productName', title: '购买产品', minWidth: 150 },
                    { field: 'oriAmount', title: '订单金额', width: 100 },
                    { field: 'amount', title: '退款金额', width: 100 },
                    {
                        title: '退款状态', width: 100, templet: function (e) {
                            var desc = e.statusDesc;
                            if (e.successTime != null) {
                                desc += ' - ' + layui.common.timeFormat(e.successTime);
                            }
                            return desc;
                        }
                    },
                    {
                        field: 'refundOrderNo', title: '退款单号', width: 210, templet: function (e) {
                            return e.refundOrderNo == '' ? '-' : e.refundOrderNo;
                        }
                    },
                    {
                        field: 'thirdPartyOrderNo', title: '第三方单号(微信/支付宝)', width: 210, templet: function (e) {
                            return e.thirdPartyOrderNo == null || e.thirdPartyOrderNo == '' ? '-' : e.thirdPartyOrderNo;
                        }
                    },
                    { field: 'refundTypeDesc', title: '退款方式', width: 150 },
                    { field: 'auditStatusDesc', title: '审核状态', width: 100 },
                    { field: 'reason', title: '退款原因', width: 100 },
                    { field: 'remark', title: '备注', width: 140 },
                    { field: 'opUserName', title: '操作用户', width: 100 }
                ];
                if (auditType == 2) {
                    fields.push({ field: 'auditRemark', title: '质检备注', width: 140 });
                    fields.push({
                        field: 'auditTime', title: '质检时间', width: 170, templet: function (e) {
                            return layui.common.timeFormat(e.auditTime);
                        }
                    });
                }
                fields.push({
                    fixed: 'right', field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                });
                fields.push({ fixed: 'right', title: '操作', width: auditType == 1 ? 220 : 170, align: 'left', toolbar: '#refund-order-bar' });
                layui.tableRequest.request('resource', true, 'refund-order-list', '/admin/product/refund-order/query', 'application/json', fields, { companyId: layui.setter.productCompanyId, auditType: auditType }, null, 'full-150');
            },
            /**
             *  获取退款订单列表（财务退款）
             * */
            queryRefundOrderForFinancial: function () {
                var type = layui.common.getUrlParam('type');
                var fields = [
                    { field: 'id', title: 'ID', fixed: 'left' },
                    { field: 'productOrderNo', title: '支付单号', width: 210 },
                    { field: 'customerName', title: '姓名', minWidth: 80 },
                    { field: 'productName', title: '购买产品', minWidth: 150 },
                    { field: 'oriAmount', title: '订单金额', width: 100 },
                    { field: 'amount', title: '退款金额', width: 100 },
                    {
                        title: '退款状态', width: 100, templet: function (e) {
                            var desc = e.statusDesc;
                            if (e.successTime != null) {
                                desc += ' - ' + layui.common.timeFormat(e.successTime);
                            }
                            return desc;
                        }
                    },
                    {
                        field: 'refundOrderNo', title: '退款单号', width: 210, templet: function (e) {
                            return e.refundOrderNo == '' ? '-' : e.refundOrderNo;
                        }
                    },
                    {
                        field: 'thirdPartyOrderNo', title: '第三方单号(微信/支付宝)', width: 210, templet: function (e) {
                            return e.thirdPartyOrderNo == null || e.thirdPartyOrderNo == '' ? '-' : e.thirdPartyOrderNo;
                        }
                    },
                    { field: 'refundTypeDesc', title: '退款方式', width: 150 },
                    { field: 'auditStatusDesc', title: '审核状态', width: 100 },
                    { field: 'remark', title: '退款备注', width: 140 },
                ];
                fields.push({
                    fixed: 'right', field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                });
                if (type == 1) {
                    fields.push({ fixed: 'right', title: '操作', width: 80, align: 'left', toolbar: '#refund-order-bar' });
                }
                layui.tableRequest.request('resource', true, 'refund-order-list', '/admin/product/refund-order/finance/query', 'application/json', fields, { companyId: layui.setter.productCompanyId, type: type }, null, 'full-150');
            },
            /**
           * 获取通话记录列表
           * @param {any} customerId
           */
            queryCallRecord: function (guids) {
                layui.tableRequest.request('resource', false, 'call-list', '/admin/callcenter/callrecord/by-guid/query', 'application/json', [
                    { field: 'userName', title: '通话人' },
                    {
                        field: 'beginTime', title: '通话开始时间', templet: function (e) {
                            return layui.common.timeFormat(e.beginTime);
                        }
                    },
                    {
                        field: 'finishedTime', title: '通话结束时间', templet: function (e) {
                            return e.finishedTime != null ? layui.common.timeFormat(e.finishedTime) : '-';
                        }
                    },
                    {
                        field: 'establishedLength', title: '接通时长', templet: function (e) {
                            if (e.establishedLength === 0) return '-';
                            var hours = Math.floor((e.establishedLength % 86400000) / 3600000); //时
                            var minutes = Math.floor((e.establishedLength % 3600000) / 60000); //分
                            var seconds = Math.floor((e.establishedLength % 60000) / 1000); //秒

                            var text = '';
                            if (hours > 0) {
                                text += hours + '小时';
                            }
                            if (minutes > 0) {
                                text += minutes + '分';
                            }
                            if (seconds > 0) {
                                text += seconds + '秒';
                            }
                            return text;
                        }
                    },
                    {
                        field: 'userCallType', title: '呼叫类型', templet: function (e) {
                            if (e.userCallType == 11) {
                                return '来电';
                            }
                            else if (e.userCallType == 12) {
                                return '去电';
                            }
                            else if (e.userCallType == 13) {
                                return '绑定分机对呼去电';
                            }
                            else if (e.userCallType == 14) {
                                return '监听';
                            }
                            else if (e.userCallType == 15) {
                                return '强插';
                            }
                            else if (e.userCallType == 16) {
                                return '会议';
                            }
                            else if (e.userCallType == 17) {
                                return '软转接';
                            }
                            else if (e.userCallType == 20) {
                                return '硬转接C';
                            }
                            else if (e.userCallType == 21) {
                                return '硬转接B';
                            }
                            else if (e.userCallType == 30) {
                                return '来电抢接者';
                            }
                            else if (e.userCallType == 31) {
                                return '来电被抢接';
                            }
                            else if (e.userCallType == 32) {
                                return '呼叫转移过来';
                            }
                            else {
                                return '-';
                            }
                        }
                    },
                    {
                        field: 'finishedReason', title: '结束原因', templet: function (e) {
                            if (e.finishedReason == 2) {
                                return '请求超时';
                            }
                            else if (e.finishedReason == 3) {
                                return '发送命令超时';
                            }
                            else if (e.finishedReason == 4) {
                                return '音频长时间静音,无语音包';
                            }
                            else if (e.finishedReason == 5) {
                                return '回铃超时';
                            }
                            else if (e.finishedReason == 6) {
                                return '未回铃';
                            }
                            else if (e.finishedReason == 7) {
                                return '通话超时';
                            }
                            else if (e.finishedReason == 16) {
                                return '呼叫失败';
                            }
                            else if (e.finishedReason == 17) {
                                return '呼叫拒接';
                            }
                            else if (e.finishedReason == 18) {
                                return '本地挂机';
                            }
                            else if (e.finishedReason == 19) {
                                return '本地取消';
                            }
                            else if (e.finishedReason == 20) {
                                return '对方挂机';
                            }
                            else if (e.finishedReason == 21) {
                                return '抢接服务器挂机';
                            }
                            else {
                                return '-';
                            }
                        }
                    },
                    { fixed: 'right', title: '操作', width: 120, align: 'left', toolbar: '#call-record-bar' }
                ], { guids: guids }, null, 'auto');

                layui.table.on('tool(call-list)', function (obj) {
                    var data = obj.data;
                    var fileUrl = '//dn-sound-record.oss-cn-shanghai.aliyuncs.com/' + data.recordFileUrl;
                    if (obj.event === 'download') {
                        if (data.establishedLength == 0) {
                            layui.common.alertAutoClose("当前无可下载的录音文件或录音暂未同步");
                            return;
                        }
                        window.open(fileUrl);
                    }
                    else if (obj.event == 'player') {
                        if (data.establishedLength == 0) {
                            layui.common.alertAutoClose("当前无可播放的录音文件或录音暂未同步");
                            return;
                        }
                        document.getElementById('audio_dom').src = fileUrl;
                        layui.common.openPage('播放录音', 500, 100, '#audio-dialog', function () {
                            document.getElementById('audio_dom').src = '';
                        });
                    }
                });
            },
            /**
            * 获取通话录音列表（阿里云）
            * @param {any} customerId
            */
            queryAliCallRecord: function (orderId) {
                layui.tableRequest.request('resource', true, 'ali-call-list', '/admin/callcenter/ali/callrecord/query', 'application/json', [
                    { field: 'userName', title: '通话人' },
                    { field: 'contactType', title: '通话类型' },
                    { field: 'startTime', title: '开始时间' },
                    { field: 'releaseTime', title: '结束时间' },
                    { field: 'contactDisposition', title: '结束原因' },
                    { field: 'releaseInitiator', title: '挂断方' },
                    {
                        field: 'callDuration', title: '通话时长', templet: function (e) {
                            if (e.callDuration == '') return '-';

                            var hours = Math.floor(parseInt(e.callDuration) / 3600);
                            var minutes = Math.floor((parseInt(e.callDuration) - (hours * 3600)) / 60);
                            var seconds = parseInt(e.callDuration) % 60;

                            return [hours, minutes, seconds]
                                .map(num => num < 10 ? '0' + num : num.toString())
                                .filter(num => num)
                                .join(':');
                        }
                    },
                    { fixed: 'right', title: '操作', width: 120, align: 'left', toolbar: '#ali-call-record-bar' }
                ], { orderId: orderId }, null, 'auto');

                layui.table.on('tool(ali-call-list)', function (obj) {
                    var data = obj.data;
                    if (obj.event === 'download') {
                        if (!data.recordingReady) {
                            layui.common.alertAutoClose("当前无可下载的录音文件或录音暂未同步");
                            return;
                        }
                        layui.productOrder.getAliCallRecordFileUrl(data.id, 1);
                    }
                    else if (obj.event == 'player') {
                        if (!data.recordingReady) {
                            layui.common.alertAutoClose("当前无可播放的录音文件或录音暂未同步");
                            return;
                        }
                        layui.productOrder.getAliCallRecordFileUrl(data.id, 2);
                    }
                });
            },
            /**
             * 获取通话记录录音文件地址
             * @param {any} id
             * @param {any} type
             */
            getAliCallRecordFileUrl: function (id, type) {
                layui.request({
                    method: 'post',
                    url: '/admin/callcenter/ali/callrecord/file/get?id=' + id,
                }).then(function (res) {
                    if (res.isSuccess) {
                        if (type == 1) {
                            window.open(res.result.fileUrl);
                        }
                        else {
                            document.getElementById('audio_dom').src = res.result.fileUrl;
                            layui.common.openPage('播放录音', 500, 100, '#audio-dialog', function () {
                                document.getElementById('audio_dom').src = '';
                            });
                        }
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
           * 获取沟通记录列表
           * @param {any} customerId
           */
            queryFollowUpRecord: function (orderId) {
                layui.tableRequest.request('resource', false, 'followup-list', '/admin/callcenter/followuprecord/query', 'application/json', [
                    { field: 'content', title: '沟通内容' },
                    { field: 'uesrName', title: '用户', width: 100 },
                    {
                        field: 'createdAt', title: '沟通时间', width: 160, templet: function (e) {
                            return layui.common.timeFormat(e.createdAt);
                        }
                    }

                ], { orderId: orderId }, null, 'auto');
            },
            /**
             * 创建订单
             * @param {any} data
             */
            create: function (data) {
                data.field.channelKey = 'sgluru';
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/offline/create',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        layui.common.alertAutoClose("产品订单创建成功");
                        setTimeout(function () {
                            layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                            parent.location.reload();
                        }, 3000);
                    }
                    else {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 编辑线下订单
             * @param {any} data
             */
            update: function (data) {
                data.field.channelKey = 'sgluru';
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/offline/update',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        layui.common.alertAutoClose("产品订单编辑成功");
                        setTimeout(function () {
                            layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                            parent.location.reload();
                        }, 3000);
                    }
                    else {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
            * 删除订单
            * @param {any} data
            */
            delete: function (id) {
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/delete?id=' + id,
                }).then(function (res) {
                    if (res.isSuccess) {
                        layui.tableRequest.reload("product-order-list");
                        layui.common.alertAutoClose("订单删除成功");
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
            * 删除手工录入订单订单
            * @param {any} data
            */
            deleteOffline: function (id) {
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/offline/delete?id=' + id,
                }).then(function (res) {
                    if (res.isSuccess) {
                        layui.tableRequest.reload("product-order-list");
                        layui.common.alertAutoClose("订单删除成功");
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 预览转账凭证
             * @param {any} url
             */
            openPreview: function (url) {
                layui.$('#preview').html('');
                if (url.indexOf(';') > -1) {
                    var imgArr = url.split(';');
                    for (var i = 0; i < imgArr.length; i++) {
                        layui.$('#preview').append('<img id="transferVoucher" src="' + imgArr[i] + '" style="width:100%;margin-bottom:10px;" />');
                    }
                }
                else {
                    layui.$('#preview').append('<img id="transferVoucher" src="' + url + '" style="width:100%" />');
                }
                layui.common.openPage('查看转账凭证', 450, 500, '#preview')
            },
            /**
             * 获取合同预览链接
             */
            getPreviewUrl: function () {
                var id = layui.$('#hid_id').val();
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/preview-url/get?id=' + id,
                }).then(function (res) {
                    if (res.isSuccess) {
                        window.open(res.result)
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 获取下载合同链接
             * */
            downloadContract: function () {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    setTimeout(function () { history.back(-1) }, 2000);
                    return;
                }
                var url = '/admin/product/order/document/get?id=' + id;
                if (layui.setter.productCompanyId == "652395f02a089a92386eedc9" || layui.setter.productCompanyId == "6340ecbf4d7e5a21ed46e996") {
                    url = '/admin/product/order/fdd/document/get?id=' + id;
                }
                layui.request({
                    method: 'post',
                    url: url,
                }).then(function (res) {
                    if (res.isSuccess) {
                        var getTpl = document.getElementById("doc-tpl").innerHTML
                            , view = document.getElementById('doc-view');
                        layui.laytpl(getTpl).render(res.result.docs, function (html) {
                            view.innerHTML = html;
                        });
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 【法大大】预览合同
             * */
            viewContract: function () {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    setTimeout(function () { history.back(-1) }, 2000);
                    return;
                }
                var url = '/admin/product/order/fdd/preview-url/get?id=' + id;
                layui.request({
                    method: 'post',
                    url: url,
                }).then(function (res) {
                    if (res.isSuccess) {
                        var getTpl = document.getElementById("doc-tpl").innerHTML
                            , view = document.getElementById('doc-view');
                        layui.laytpl(getTpl).render(res.result.docs, function (html) {
                            view.innerHTML = html;
                        });
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 修改订单状态
             * @param {any} data
             */
            updateStatus: function (data) {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    return;
                }
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/status/update',
                    data: JSON.stringify({
                        id: id,
                        status: data.field.status,
                        remark: data.field.remark
                    }),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        parent.layui.common.closeType('iframe');
                        parent.layui.productOrder.get();
                        parent.layui.common.alertAutoClose("订单状态修改成功");
                    }
                    else {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 通过id修改产品订单客户姓名
             * @param {any} old
             * @param {any} obj
             */
            updateCustomerName: function (old, obj) {
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/customer-name/update',
                    data: JSON.stringify({
                        id: obj.data.id,
                        userName: obj.value
                    }),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        layui.common.alertAutoClose("客户姓名修改成功");
                    }
                    else {
                        obj.update({
                            customerName: old,
                        });
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 通过id修改产品订单客户身份证号
             * @param {any} old
             * @param {any} obj
             */
            updateCustomerCertNo: function (old, obj) {
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/customer-certno/update',
                    data: JSON.stringify({
                        id: obj.data.id,
                        certNo: obj.value
                    }),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        layui.common.alertAutoClose("客户身份证号修改成功");
                    }
                    else {
                        obj.update({
                            customerCertNo: old,
                        });
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 审核订单
             * @param {any} data
             */
            updateAuditStatus: function (data) {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    return;
                }
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/audit-status/update',
                    data: JSON.stringify({
                        id: id,
                        auditStatus: data.field.auditStatus,
                        auditRemark: data.field.auditRemark
                    }),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        var confirmIndex = parent.layui.layer.confirm('订单审核完成，是否继续审核下一支付单？', {
                            icon: 3,
                            title: '提示',
                            btn: ['确定', '取消']
                        }, function () {
                            layui.request({
                                method: 'post',
                                url: '/admin/product/order/newxt-audit/get?id=' + id,
                                headers: { 'Content-Type': 'application/json' },
                            }).then(function (res2) {
                                if (res2.isSuccess) {
                                    if (res2.result != '') {
                                        parent.location.href = 'detail.html?id=' + res2.result;
                                    }
                                    else {
                                        parent.layui.common.alert('不存在未审核的订单', 0);
                                    }
                                }
                                else {
                                    parent.layui.common.alert(res2.message, 0);
                                }
                            })
                        }, function () {
                            parent.layui.productOrder.get();
                            parent.layui.layer.close(confirmIndex);
                            parent.layui.common.closeType('iframe');
                        });
                    }
                    else {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 修改订单支付状态(财务编辑订单信息且审核)
             * @param {any} data
             */
            updatePayStatus: function (data) {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    return;
                }
                data.field.id = id;
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/pay-status/update',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        var confirmIndex = parent.layui.layer.confirm('订单审核完成，是否继续审核下一支付单？', {
                            icon: 3,
                            title: '提示',
                            btn: ['确定', '取消']
                        }, function () {
                            layui.request({
                                method: 'post',
                                url: '/admin/product/order/newxt-confirm/get?id=' + id,
                                headers: { 'Content-Type': 'application/json' },
                            }).then(function (res2) {
                                if (res2.isSuccess) {
                                    if (res2.result != '') {
                                        parent.location.href = 'detail.html?id=' + res2.result;
                                    }
                                    else {
                                        parent.layui.common.alert('不存在未确认的订单', 0);
                                    }
                                }
                                else {
                                    parent.layui.common.alert(res2.message, 0);
                                }
                            })
                        }, function () {
                            parent.layui.productOrder.get();
                            parent.layui.layer.close(confirmIndex);
                            parent.layui.common.closeType('iframe');
                        });
                    }
                    else {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 修改订单支付状态(客服人工完善支付信息)
             * @param {any} data
             */
            updatePayStatusForKf: function (data) {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    return;
                }
                data.field.id = id;
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/pay-status/kf/update',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        parent.layui.common.alertAutoClose('编辑成功');
                        parent.layui.productOrder.get();
                        parent.layui.common.closeType('iframe');
                    }
                    else {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 用户绑定订单
             * @param {any} data
             */
            bind: function (data) {
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/salers-userid/bind',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    if (res.isSuccess) {
                        parent.layui.common.closeType('iframe');
                        parent.layui.tableRequest.reload("product-order-list");
                        parent.layui.common.alertAutoClose("订单绑定成功");
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 分配产品订单
             * @param {any} data
             */
            updateSalerUserId: function (data) {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    return;
                }
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/salers-userid/update',
                    data: JSON.stringify({
                        id: id,
                        salerUserId: data.field.saleUserId
                    }),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        parent.layui.common.closeType('iframe');
                        parent.layui.tableRequest.reload("product-order-list");
                        parent.layui.common.alertAutoClose("订单分配成功");
                    }
                    else {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 批量分配产品订单
             * @param {any} data
             */
            batchUpdateSalerUserId: function (data) {
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/salers-userid/batch-update',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    if (res.isSuccess) {
                        layui.common.closeType('page');
                        layui.tableRequest.reload("product-order-list");
                        layui.common.alertAutoClose("订单分配成功");
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 订单申请退款
             * @param {any} data
             */
            refund: function (data) {
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/refund-order/submit',
                    data: JSON.stringify({
                        orderId: data.field.orderId,
                        reason: data.field.reason,
                        remark: data.field.remark
                    }),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        parent.layui.common.closeType('iframe');
                        parent.layui.productOrder.get();
                        parent.layui.common.alertAutoClose("订单退款申请提交成功");
                    }
                    else {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 订单申请退款(线下)
             * @param {any} data
             */
            refundOffline: function (data) {
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/refund-order/offline/submit',
                    data: JSON.stringify({
                        orderId: data.field.orderId,
                        reason: data.field.reason,
                        amount: data.field.amount
                    }),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        parent.layui.common.closeType('iframe');
                        parent.layui.productOrder.get();
                        parent.layui.common.alertAutoClose("订单退款申请提交成功");
                    }
                    else {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 审核订单退款（线下）
             * @param {any} data
             */
            auditOfflineRefund: function (data) {
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/refund-order/offline/audit',
                    data: JSON.stringify({
                        orderId: data.field.orderId,
                        status: data.field.status,
                        amount: data.field.amount,
                        remark: data.field.remark
                    }),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        parent.layui.common.closeType('iframe');
                        parent.layui.productOrder.get();
                        parent.layui.common.alertAutoClose("订单退款审核成功");
                    }
                    else {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
            * 财务审核支付订单
            * @param {any} data
            */
            auditForFinancial: function (data) {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    return;
                }
                data.field.id = id;
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/financial-audit-status/update',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    if (res.isSuccess) {
                        if (document.referrer.indexOf('list-financial-child.html') > -1) {
                            parent.layui.tableRequest.reload("product-order-list");
                        }
                        else if (document.referrer.indexOf('audit-financial.html') > -1) {
                            parent.layui.productOrder.get();
                        }
                        parent.layui.common.closeType('iframe');
                        parent.layui.common.alertAutoClose("审核成功");
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
            * 批量财务审核
            * @param {any} data
            */
            auditStatusUpdate: function (data) {
                var id = layui.common.getUrlParam('ids');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    return;
                }
                let ids = id.split(",").map(item => {
                    return {
                        id: item
                    }
                })
                data.field.ids = ids;
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/batch-financial-audit-status/update',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    if (res.isSuccess) {
                        if (document.referrer.indexOf('list-financial-child.html') > -1) {
                            parent.layui.tableRequest.reload("product-order-list");
                        }
                        else if (document.referrer.indexOf('audit-financial.html') > -1) {
                            parent.layui.productOrder.get();
                        }
                        parent.layui.common.closeType('iframe');
                        parent.layui.common.alertAutoClose("审核成功");
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
           * 合规审核支付订单
           * @param {any} data
           */
            auditForCompliance: function (data) {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    return;
                }
                data.field.id = id;
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/audit-status/update',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    if (res.isSuccess) {
                        if (document.referrer.indexOf('list-compliance-child.html') > -1) {
                            parent.layui.tableRequest.reload("product-order-list");
                        }
                        else if (document.referrer.indexOf('detail.html') > -1) {
                            parent.layui.productOrder.get();
                        }
                        parent.layui.common.closeType('iframe');
                        parent.layui.common.alertAutoClose("审核成功");
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 风控审核状态
             * @param {any} data
             */
            auditForFengKong: function (data) {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    return;
                }
                data.field.id = id;
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/fk-audit-status/update',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    if (res.isSuccess) {
                        if (document.referrer.indexOf('list-fengkong-child.html') > -1) {
                            parent.layui.tableRequest.reload("product-order-list");
                        }
                        else if (document.referrer.indexOf('detail.html') > -1) {
                            parent.layui.productOrder.get();
                        }
                        parent.layui.common.closeType('iframe');
                        parent.layui.common.alertAutoClose("编辑成功");
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
            * 批量添加拨打记录
            * @param {any} data
            */
            addCallRecord: function (data) {
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/callbot/callrecord/create',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    if (res.isSuccess) {
                        layui.common.closeType('page');
                        layui.$('#task-view').val('');
                        layui.form.render('select');
                        layui.tableRequest.reload("product-order-list");
                        layui.common.alertAutoClose("添加拨打数据成功");
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 申请退款
             * @param {any} data
             */
            applyRefund: function (data) {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    return;
                }
                data.field.id = id;
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/refund-order/create',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    if (res.isSuccess) {
                        parent.layui.productOrder.get();
                        parent.layui.common.closeType('iframe');
                        parent.layui.common.alertAutoClose("申请提交成功");
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 申请退款
             * @param {any} data
             */
            batchApplyRefund: function (data) {
                var ids = layui.common.getUrlParam('ids');
                if (ids == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    return;
                }
                data.field.orderIds = ids.split(',');
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/refund-order/batch-create',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    if (res.isSuccess) {
                        parent.layui.tableRequest.reload("product-order-list");
                        parent.layui.common.closeType('iframe');
                        parent.layui.common.alert('退款协议签署地址：' + res.result);
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
            * 风控审核退款订单
            * @param {any} data
            */
            auditRefund: function (data) {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    return;
                }
                data.field.id = id;
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/refund-order/audit',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    if (res.isSuccess) {
                        parent.layui.tableRequest.reload("refund-order-list");
                        parent.layui.common.closeType('iframe');
                        parent.layui.common.alertAutoClose("审核成功");
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
            * 财务确认退款订单
            * @param {any} data
            */
            auditRefundForFinancial: function (data) {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    parent.layui.common.alertAutoClose("参数有误");
                    return;
                }
                data.field.id = id;
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/refund-order/finance/audit',
                    data: JSON.stringify(data.field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    if (res.isSuccess) {
                        parent.layui.tableRequest.reload("refund-order-list");
                        parent.layui.common.closeType('iframe');
                        parent.layui.common.alertAutoClose("操作成功");
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 导入分配订单
             * @param {any} data
             */
            import: function (data) {
                if (data.field.file == '') {
                    layui.common.alertAutoClose("请选择关键词文件");
                    return;
                }
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                var formData = new FormData(layui.$('#uploadForm')[0]);
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/saleruserid/import',
                    data: formData
                }).then(function (res) {
                    if (res.isSuccess) {
                        layui.common.alertAutoClose(res.result);
                        setTimeout(function () {
                            layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                            parent.location.reload();
                        }, 3000);
                    }
                    else {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 导出产品订单
             * */
            export: function (data) {
                var payType = layui.$('#payType').val();
                var payStatus = layui.$('#payStatus').val();
                var refundStatus = layui.$('#refundStatus').val();
                var auditStatus = layui.$('#auditStatus').val();
                var signFlowStatus = layui.$('#signFlowStatus').val();
                var channelKey = layui.$('#product-channel-view').val();
                var startTime = layui.$('#startTime').val();
                var endTime = layui.$('#endTime').val();
                var payStartTime = layui.$('#payStartTime').val();
                var payEndTime = layui.$('#payEndTime').val();
                var auditStartTime = layui.$('#auditStartTime').val();
                var auditEndTime = layui.$('#auditEndTime').val();
                var serviceStartTime = layui.$('#serviceStartTime').val();
                var serviceEndTime = layui.$('#serviceEndTime').val();
                var type = layui.$('#type').val();
                var buyType = layui.$('#buyType').val();
                var fkAuditStatus = layui.$('#fkAuditStatus').val();
                var saleUserId = layui.$('#admin-user-view').val();
                var teacherName = layui.$('#teacherName').val();
                var customerChannel = layui.$('#customerChannel').val();
                var subsidiaryId = layui.$('#subsidiary-view').val();
                var sort = layui.$('#sort').val();
                var field = {
                    companyId: layui.setter.productCompanyId,
                    keywords: layui.$('#keywords').val(),
                    payType: payType == '' ? -1 : payType,
                    payStatus: payStatus == '' ? -1 : payStatus,
                    refundStatus: refundStatus == '' ? -1 : refundStatus,
                    auditStatus: auditStatus == '' ? -1 : auditStatus,
                    fkAuditStatus: fkAuditStatus == '' ? -1 : fkAuditStatus,
                    signFlowStatus: signFlowStatus == '' ? -1 : signFlowStatus,
                    type: type == '' ? -1 : type,
                    channelKey: channelKey,
                    startTime: startTime,
                    endTime: endTime,
                    payStartTime: payStartTime,
                    payEndTime: payEndTime,
                    auditStartTime: auditStartTime,
                    auditEndTime: auditEndTime,
                    serviceStartTime: serviceStartTime,
                    serviceEndTime: serviceEndTime,
                    buyType: buyType == '' ? -1 : buyType,
                    saleUserId: saleUserId,
                    teacherName: teacherName,
                    customerChannel, customerChannel,
                    departmentId: layui.$('#department-view').val(),
                    subsidiaryId: subsidiaryId,
                    sort: sort,
                };
                var aUserStr = localStorage.getItem('ly-admin-user');
                var curUser = '';
                if (aUserStr != null) {
                    var aUserObj = JSON.parse(aUserStr);
                    curUser = aUserObj.id;
                }
                if (curUser == '640afb7aaa622188d712b115' || curUser == '640afa5caa622188d712b114') {
                    //只显示已支付/待确认
                    layui.$('#payStatus').val(100);
                    field.payStatus = 100;
                }
                layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/export',
                    data: JSON.stringify(field),
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    if (res.isSuccess) {
                        location.href = res.result;
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 呼叫客户
             * */
            callCustomer: function () {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    layui.common.alertAutoClose("参数有误");
                }
                layui.request({
                    method: 'post',
                    url: '/admin/product/order/ali/call?id=' + id,
                }).then(function (res) {
                    if (res.isSuccess) {
                        if (res.result != null && res.result.length > 0) {
                            layui.$('.file-item-z').find('.z-mobile').text(res.result.encMobile);
                            var getTpl = document.getElementById("b-mobile-tpl").innerHTML
                                , view = document.getElementById('b-mobile-view');
                            layui.laytpl(getTpl).render(res.result, function (html) {
                                view.innerHTML = html;
                            });
                            layui.$('.call-item-btn').click(function () {
                                layui.common.closeType('page');
                                var $this = layui.$(this);
                                //显示弹框
                                window.workbenchChild.changeUIConfig({ mainContentVisible: true });
                                //执行呼叫
                                var callId = 'o_' + id;
                                if ($this.data('type') != 1) {
                                    callId = 'o_b_' + id;
                                }
                                window.workbenchChild.call({ callee: callId });
                            });
                            layui.common.openPage('选择手机号', 370, 320, '#mobile-dialog');
                        }
                        else {
                            //显示弹框
                            window.workbenchChild.changeUIConfig({ mainContentVisible: true });
                            //执行呼叫
                            window.workbenchChild.call({ callee: 'o_' + id });
                        }
                    }
                    else {
                        layui.common.alertAutoClose(res.message);
                    }
                })
            },

            /**
             * 查看手机号/身份证号
             * @param {any} customerId
             * @param {any} type
             */
            readMobile: function (customerId, type) {
                var id = layui.common.getUrlParam('id');
                if (id == '') {
                    layui.common.alertAutoClose("参数有误");
                    return;
                }
                layui.request({
                    method: 'post',
                    url: '/admin/product/customer/mobile/read?id=' + customerId + '&type=' + type,
                    headers: { 'Content-Type': 'application/json' },
                }).then(function (res) {
                    if (res.isSuccess) {
                        if (type == 2) {
                            layui.$('#customerCertNo').html(res.result);
                        }
                        else {
                            layui.$('#customerMobile').html(res.result);
                        }
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
             * 获取退款合同链接
             */
            getRefundOrderContractUrl: function (id, type) {
                var url = '/admin/product/refund-order/contract-url/preview?id=' + id;
                if (type == 2) {
                    url = '/admin/product/refund-order/contract-url/download?id=' + id;
                }
                layui.request({
                    method: 'post',
                    url: url,
                }).then(function (res) {
                    if (res.isSuccess) {
                        window.open(res.result)
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            },
            /**
            * 监听表格单击事件
            * */
            tableEvent: function () {
                layui.table.on('toolbar(product-order-list)', function (obj) {
                    if (obj.event === 'batch-distribution') {
                        layui.common.openIframe('批量分配用户', 500, 300, 'import.html');
                    }
                    else if (obj.event === 'batch-distribution2') {
                        var checkedList = layui.table.checkStatus(obj.config.id);
                        if (checkedList.data.length == 0) {
                            layui.common.alertAutoClose("请选择需要操作的记录");
                            return;
                        }
                        var ids = [];
                        for (var i = 0; i < checkedList.data.length; i++) {
                            ids.push(checkedList.data[i].id);
                        }
                        layui.$('.distribution-desc').text('已选择分配订单数共计' + ids.length + '个');
                        layui.$('#ids').val(ids.toString());
                        var initHtml = '<div class="config-input-item" style="margin-bottom:10px;float:left">';
                        initHtml += '<div class="layui-input-inline" style="width:210px">';
                        initHtml += '<select lay-filter="salerUserId" class="salerUserId" lay-verify="required" lay-search>';
                        for (var i = 0; i < salerUserList.length; i++) {
                            initHtml += '<option value="' + salerUserList[i].id + '">' + salerUserList[i].name.replace('所属用户', '分配用户') + '</option>';
                        }
                        initHtml += '</select>';
                        initHtml += '</div>';
                        initHtml += '<div class="layui-input-inline" style="margin-right:0 ">';
                        initHtml += '<div class="layui-input-inline" style="width: 120px">';
                        initHtml += '<input type="text" lay-verify="required|number" placeholder="分配数量数" autocomplete="off" class="layui-input totalCount">';
                        initHtml += '</div>';
                        initHtml += '<div class="layui-input-inline" style="width:50px;">';
                        initHtml += '<button type="button" class="layui-btn layui-btn-primary remove-config">';
                        initHtml += '<i class="layui-icon">&#xe640;</i>';
                        initHtml += '</button>';
                        initHtml += '</div>';
                        initHtml += '</div>';
                        initHtml += '</div>';
                        layui.$('.config-input-list').html(initHtml);
                        layui.form.render('select');
                        layui.common.openPage('批量分配资源', 480, 450, '#distribution-dialog');
                    }

                    else if (obj.event === 'product-order-refund') {
                        var checkedList = layui.table.checkStatus(obj.config.id);
                        if (checkedList.data.length == 0) {
                            layui.common.alertAutoClose("请选择需要操作的记录");
                            return;
                        }
                        var ids = [];
                        for (var i = 0; i < checkedList.data.length; i++) {
                            ids.push(checkedList.data[i].id);
                        }
                        layui.common.openIframe('订单批量申请退款', 600, 500, 'batch-apply-refund.html?ids=' + ids.toString(','));
                    } else if (obj.event === 'product-order-financial') {
                        var checkedList = layui.table.checkStatus(obj.config.id);
                        if (checkedList.data.length == 0) {
                            layui.common.alertAutoClose("请选择需要操作的记录");
                            return;
                        }
                        var ids = [];
                        for (var i = 0; i < checkedList.data.length; i++) {
                            ids.push(checkedList.data[i].id);
                        }
                        layui.common.openIframe('批量财务审核', 600, 340, 'batch-apply-financial.html?ids=' + ids.toString(','));
                    }
                });
                layui.table.on('tool(product-order-list)', function (obj) {
                    var data = obj.data;
                    if (obj.event === 'detail') {
                        if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.detail')) {
                            location.href = 'detail.html?id=' + data.id;
                        }
                        else {
                            layui.common.alertAutoClose("暂无查看权限");
                        }
                    }
                    else if (obj.event === 'del') {
                        if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.delete')) {
                            var confirmIndex = layui.layer.confirm('确定删除该订单吗？', {
                                icon: 3,
                                title: '提示',
                                btn: ['确定', '取消']
                            }, function () {
                                layui.productOrder.delete(data.id);
                            }, function () {
                                layui.layer.close(confirmIndex);
                            });
                        }
                        else {
                            layui.common.alertAutoClose("暂无操作权限");
                        }
                    }
                    else if (obj.event === 'del-offline') {
                        if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.offline.delete') && !layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.delete')) {
                            var confirmIndex = layui.layer.confirm('确定删除该订单吗？', {
                                icon: 3,
                                title: '提示',
                                btn: ['确定', '取消']
                            }, function () {
                                layui.productOrder.deleteOffline(data.id);
                            }, function () {
                                layui.layer.close(confirmIndex);
                            });
                        }
                        else {
                            layui.common.alertAutoClose("暂无操作权限");
                        }
                    }
                    else if (obj.event == 'update-offline') {
                        if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.offline.update')) {
                            layui.common.openIframe('编辑线下订单', 720, 700, 'update.html?id=' + data.id);
                        }
                        else {
                            layui.common.alertAutoClose("暂无操作权限");
                        }
                    }
                    else if (obj.event == 'distribution') {
                        if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.salersuserid')) {
                            layui.common.openIframe('分配订单', 550, 400, 'update-saleruserid.html?id=' + data.id + '&sid=' + data.saleUserId);
                        }
                        else {
                            layui.common.alertAutoClose("暂无操作权限");
                        }
                    }
                    else if (obj.event == 'refund-offline') {
                        if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.refund.offline.submit')) {
                            layui.common.openIframe('线下订单申请退款', 550, 400, 'refund-offline.html?id=' + data.id);
                        }
                        else {
                            layui.common.alertAutoClose("暂无操作权限");
                        }
                    }
                });
            },
            /**
             * 绑定事件
             * */
            bindEvent: function () {
                //财务审核
                if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.financial.audit')) {
                    layui.$('#order-audit-financial').click(function () {
                        var id = layui.$('#hid_id').val();
                        layui.common.openIframe('审核订单', 570, 540, 'audit-financial.html?id=' + id);
                    });
                    layui.$('#order-audit-financial').removeClass('layui-hide');
                }
                //财务确认线下订单
                if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.paystatus.update')) {
                    layui.$('#order-pay-status').click(function () {
                        var id = layui.$('#hid_id').val();
                        layui.common.openIframe('支付订单', 720, 550, 'update-paystatus.html?id=' + id);
                    });
                    layui.$('#order-pay-status').removeClass('layui-hide');
                }
                //客服修改支付状态
                if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.paystatus.kf.update')) {
                    layui.$('#order-pay-status-kf').click(function () {
                        var id = layui.$('#hid_id').val();
                        layui.common.openIframe('支付订单', 720, 550, 'update-paystatus-kf.html?id=' + id);
                    });
                    layui.$('#order-pay-status-kf').removeClass('layui-hide');
                }
                //线上订单退款
                if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.refund')) {
                    layui.$('#order-refund').click(function () {
                        var id = layui.$('#hid_id').val();
                        layui.common.openIframe('线上订单退款', 480, 480, 'refund.html?id=' + id);
                    });
                    layui.$('#order-refund').removeClass('layui-hide');
                }
                //审核线下退款
                if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.refund.offline.audit')) {
                    layui.$('#order-refund-offline-audit').click(function () {
                        var id = layui.$('#hid_id').val();
                        layui.common.openIframe('审核线下退款订单申请', 500, 470, 'audit-offline-refund.html?id=' + id);
                    });
                    layui.$('#order-refund-offline-audit').removeClass('layui-hide');
                }
                //合规审核
                if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.compliance.audit')) {
                    layui.$('#order-audit').click(function () {
                        var id = layui.$('#hid_id').val();
                        layui.common.openIframe('审核订单', 570, 500, 'audit-compliance.html?id=' + id);
                    });
                    layui.$('#order-audit').removeClass('layui-hide');
                }
                //风控审核
                if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.fk.audit')) {
                    layui.$('#order-audit-fk').click(function () {
                        var id = layui.$('#hid_id').val();
                        layui.common.openIframe('订单质检', 570, 500, 'audit-fengkong.html?id=' + id);
                    });
                    layui.$('#order-audit-fk').removeClass('layui-hide');
                }
                //预览合同
                if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.preview')) {
                    layui.$('#order-preview').click(function () {
                        var id = layui.$('#hid_id').val();
                        if (layui.setter.productCompanyId == "652395f02a089a92386eedc9" || layui.setter.productCompanyId == "6340ecbf4d7e5a21ed46e996") {
                            layui.common.openIframe('预览合同文件', 332, 270, 'preview.html?id=' + id + '&type=preview&contractNo=' + layui.$('#contractNo').text());
                        }
                        else {
                            layui.productOrder.getPreviewUrl();
                        }
                    });
                    layui.$('#order-preview').removeClass('layui-hide');
                }
                //下载合同
                if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.download')) {
                    layui.$('#order-download').click(function () {
                        var id = layui.$('#hid_id').val();
                        layui.common.openIframe('下载合同文件', 332, 270, 'download.html?id=' + id + '&contractNo=' + layui.$('#contractNo').text());
                    });
                    layui.$('#order-download').removeClass('layui-hide');
                }
                //订单作废
                if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.status')) {
                    layui.$('#order-status').click(function () {
                        var id = layui.$('#hid_id').val();
                        layui.common.openIframe('修改订单状态', 460, 340, 'update-status.html?id=' + id);
                    });
                    layui.$('#order-status').removeClass('layui-hide');
                }
                //申请退款
                if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.order.refund.create')) {
                    layui.$('#apply-refund').click(function () {
                        var id = layui.$('#hid_id').val();
                        layui.common.openIframe('申请退款', 480, 520, 'apply-refund.html?id=' + id);
                    });
                    layui.$('#apply-refund').removeClass('layui-hide');
                }
                //呼叫
                if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.customer.call')) {
                    layui.$('#call-customer').click(function () {
                        layui.productOrder.callCustomer();
                    });
                    layui.$('#call-customer').removeClass('layui-hide');
                }
                //获取支付地址
                layui.$('#order-pay-url').click(function () {
                    layui.productOrder.getUrl(1);
                });
                //获取签署地址
                layui.$('#order-sign-url').click(function () {
                    layui.productOrder.getUrl(2);
                });

                var aUserStr = localStorage.getItem('ly-admin-user');
                if (aUserStr != null) {
                    var aUserObj = JSON.parse(aUserStr);
                    curUser = aUserObj.id;
                }
                //合规账号
                var dateConfig = {
                    elem: '#startTime',
                    type: 'datetime'
                };
                if (curUser == '640afb7aaa622188d712b115') {
                    //合规显示三月份以后订单
                    dateConfig.min = '2023-04-01 00:00:00';
                    dateConfig.btns = ['now', 'confirm']
                }

                layui.laydate.render(dateConfig);
                layui.laydate.render({
                    elem: '#endTime',
                    type: 'datetime'
                });
                layui.laydate.render({
                    elem: '#payStartTime',
                    type: 'datetime'
                });
                layui.laydate.render({
                    elem: '#payEndTime',
                    type: 'datetime'
                });
                layui.laydate.render({
                    elem: '#auditStartTime',
                    type: 'datetime'
                });
                layui.laydate.render({
                    elem: '#auditEndTime',
                    type: 'datetime'
                });
                layui.laydate.render({
                    elem: '#serviceStartTime',
                    type: 'datetime'
                });
                layui.laydate.render({
                    elem: '#serviceEndTime',
                    type: 'datetime'
                });
                //产品订单搜索
                layui.form.on('submit(product-order-search)', function (data) {
                    data.field.companyId = layui.setter.productCompanyId;
                    if (data.field.auditStatus == '') {
                        data.field.auditStatus = -1;
                    }
                    if (data.field.fkAuditStatus == '') {
                        data.field.fkAuditStatus = -1;
                    }
                    if (data.field.fkAuditStatus == '') {
                        data.field.fkAuditStatus = -1;
                    }
                    if (data.field.payStatus == '') {
                        data.field.payStatus = -1;
                    }
                    if (data.field.payType == '') {
                        data.field.payType = -1;
                    }
                    if (data.field.refundStatus == '') {
                        data.field.refundStatus = -1;
                    }
                    if (data.field.signFlowStatus == '') {
                        data.field.signFlowStatus = -1;
                    }
                    if (data.field.type == '') {
                        data.field.type = -1;
                    }
                    if (data.field.buyType == '') {
                        data.field.buyType = -1;
                    }
                    if (curUser == '640afb7aaa622188d712b115' || curUser == '640afa5caa622188d712b114') {
                        //只显示已支付/待确认
                        layui.$('#payStatus').val(100);
                        data.field.payStatus = 100;
                    }
                    //执行重载
                    layui.tableRequest.reload("product-order-list", {
                        where: data.field,
                        page: {
                            curr: 1
                        }
                    });
                    layui.productOrder.getAmonut(data.field);
                });
                //产品订单导出
                layui.form.on('submit(product-order-export)', function (data) {
                    var confirmIndex = layui.layer.confirm('确定导出产品订单吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.productOrder.export(data);
                        layui.layer.close(confirmIndex);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                });

                //手工录入
                layui.form.on('submit(product-order-create)', function (data) {
                    layui.common.openIframe('手工录入线下订单', 750, 650, 'create.html');
                });
                //绑定订单
                layui.form.on('submit(product-order-bind)', function (data) {
                    layui.common.openIframe('绑定订单', 700, 500, 'bind.html');
                });

                //触发排序事件
                layui.table.on('sort(product-order-list)', function (obj) {
                    if (obj.field == 'payTime' && obj.type == 'desc') {
                        layui.$('#sort').val(2);
                    }
                    else if (obj.field == 'payTime' && obj.type == 'asc') {
                        layui.$('#sort').val(3);
                    }
                    else if (obj.field == 'refundTime' && obj.type == 'desc') {
                        layui.$('#sort').val(4);
                    }
                    else if (obj.field == 'refundTime' && obj.type == 'asc') {
                        layui.$('#sort').val(5);
                    }
                    else if (obj.field == 'serviceTime' && obj.type == 'desc') {
                        layui.$('#sort').val(6);
                    }
                    else if (obj.field == 'serviceTime' && obj.type == 'asc') {
                        layui.$('#sort').val(7);
                    }
                    else if (obj.field == 'createdAt' && obj.type == 'asc') {
                        layui.$('#sort').val(1);
                    }
                    else {
                        layui.$('#sort').val(0);
                    }
                    var payType = layui.$('#payType').val();
                    var payStatus = layui.$('#payStatus').val();
                    var refundStatus = layui.$('#refundStatus').val();
                    var auditStatus = layui.$('#auditStatus').val();
                    var signFlowStatus = layui.$('#signFlowStatus').val();
                    var channelKey = layui.$('#product-channel-view').val();
                    var startTime = layui.$('#startTime').val();
                    var endTime = layui.$('#endTime').val();
                    var payStartTime = layui.$('#payStartTime').val();
                    var payEndTime = layui.$('#payEndTime').val();
                    var auditStartTime = layui.$('#auditStartTime').val();
                    var auditEndTime = layui.$('#auditEndTime').val();
                    var serviceStartTime = layui.$('#serviceStartTime').val();
                    var serviceEndTime = layui.$('#serviceEndTime').val();
                    var type = layui.$('#type').val();
                    var teacherName = layui.$('#teacherName').val();
                    var customerChannel = layui.$('#customerChannel').val();
                    var saleUserId = layui.$('#admin-user-view').val();
                    var buyType = layui.$('#buyType').val();
                    var fkAuditStatus = layui.$('#fkAuditStatus').val();
                    var subsidiaryId = layui.$('#subsidiary-view').val();
                    var field = {
                        companyId: layui.setter.productCompanyId,
                        keywords: layui.$('#keywords').val(),
                        payType: payType == '' ? -1 : payType,
                        payStatus: payStatus == '' ? -1 : payStatus,
                        refundStatus: refundStatus == '' ? -1 : refundStatus,
                        auditStatus: auditStatus == '' ? -1 : auditStatus,
                        fkAuditStatus: fkAuditStatus == '' ? -1 : fkAuditStatus,
                        signFlowStatus: signFlowStatus == '' ? -1 : signFlowStatus,
                        type: type == '' ? -1 : type,
                        channelKey: channelKey,
                        startTime: startTime,
                        endTime: endTime,
                        payStartTime: payStartTime,
                        payEndTime: payEndTime,
                        auditStartTime: auditStartTime,
                        auditEndTime: auditEndTime,
                        serviceStartTime: serviceStartTime,
                        serviceEndTime: serviceEndTime,
                        buyType: buyType == '' ? -1 : buyType,
                        saleUserId: saleUserId,
                        teacherName: teacherName,
                        customerChannel: customerChannel,
                        departmentId: layui.$('#department-view').val(),
                        subsidiaryId: subsidiaryId,
                        sort: layui.$('#sort').val(),
                    };
                    //执行重载
                    layui.tableRequest.reload("product-order-list", {
                        initSort: obj,
                        where: field
                    });
                    layui.productOrder.getAmonut(field);
                });
                // 单元格编辑后的事件
                layui.tableRequest.on('edit(product-order-list)', function (obj) {
                    var old = layui.$(this).prev().text();
                    if (obj.field == 'customerName') {
                        layui.productOrder.updateCustomerName(old, obj);
                    }
                    else if (obj.field == 'customerCertNo') {
                        layui.productOrder.updateCustomerCertNo(old, obj);
                    }
                });
                //退款订单搜索
                layui.form.on('submit(refund-order-search)', function (data) {
                    var field = data.field;
                    field.companyId = layui.setter.productCompanyId;
                    if (data.field.status == '') {
                        field.status = -1;
                    }
                    //执行重载
                    layui.tableRequest.reload("refund-order-list", {
                        where: field,
                        page: {
                            curr: 1
                        }
                    });
                    layui.productOrder.getRefundOrderAmount(field);
                });

                //添加行
                layui.$(document).on("click", '.add-config', function () {
                    var initHtml = '<div class="config-input-item" style="margin-bottom:10px;float:left">';
                    initHtml += '<div class="layui-input-inline" style="width:210px">';
                    initHtml += '<select lay-filter="salerUserId" class="salerUserId" lay-verify="required" lay-search>';
                    for (var i = 0; i < salerUserList.length; i++) {
                        initHtml += '<option value="' + salerUserList[i].id + '">' + salerUserList[i].name.replace('所属用户', '分配用户') + '</option>';
                    }
                    initHtml += '</select>';
                    initHtml += '</div>';
                    initHtml += '<div class="layui-input-inline" style="margin-right:0 ">';
                    initHtml += '<div class="layui-input-inline" style="width: 120px">';
                    initHtml += '<input type="text" lay-verify="required|number" placeholder="分配数量数" autocomplete="off" class="layui-input totalCount">';
                    initHtml += '</div>';
                    initHtml += '<div class="layui-input-inline" style="width:50px;">';
                    initHtml += '<button type="button" class="layui-btn layui-btn-primary remove-config">';
                    initHtml += '<i class="layui-icon">&#xe640;</i>';
                    initHtml += '</button>';
                    initHtml += '</div>';
                    initHtml += '</div>';
                    initHtml += '</div>';
                    layui.$('.config-input-list').append(initHtml);
                    layui.form.render('select');
                });
                //删除行
                layui.$(document).on("click", '.remove-config', function () {
                    layui.$(this).parent().parent().parent('.config-input-item').remove();
                });
            },
            getAliStatis: function (date = new Date().toLocaleDateString()
                .split("/")
                .join("-")) {

                layui.request({
                    method: 'get',
                    url: '/ali/statis?date=' + date,
                    headers: { 'Content-Type': 'application/json' }
                }).then(function (res) {
                    if (res.result) {
                        let data = res.result;
                        console.log(data.failed)
                        layui.jquery("#totalJobNum")[0].innerHTML = data.totalJobNum;
                        layui.jquery("#totalCompleted")[0].innerHTML = data.totalCompleted;
                        layui.jquery("#completionRate")[0].innerHTML = data.completionRate + "%";
                        layui.jquery("#passNum")[0].innerHTML = data.passNum;
                        layui.jquery("#passRate")[0].innerHTML = data.passRate + "%";
                        layui.jquery("#failed")[0].innerHTML = data.failed;
                        layui.jquery("#executing")[0].innerHTML = data.executing;
                        layui.jquery("#scheduling")[0].innerHTML = data.scheduling;
                    }
                })

            }
        }

        exports('productOrder', func);
    });