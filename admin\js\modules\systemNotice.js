﻿layui.define(['laytpl', 'form', 'request', 'laydate', 'tableRequest', 'common'], function (exports) {
    var func = {
        /**
         * 获取站内信配置列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'notice-list', '/admin/notification/system-notice/query', 'application/json', [
                {
                    field: 'typeDesc', title: '类型', width: 150, templet: function (e) {
                        var desc = e.typeDesc;
                        if (!e.isRead) {
                            desc = '<span class="layui-badge-dot"></span>' + desc;
                        }
                        return desc;
                    }
                },
                {
                    field: 'content', title: '消息内容', templet: function (e) {
                        if (e.type == 20 || e.type == 21) {
                            //素材
                            return '<a lay-href="views/resource/media/list-hg.html?type=1" href="javascript:;" lay-text="素材审核">' + e.content + '</a>'
                        }
                        else if (e.type == 30 || e.type == 31) {
                            //研报
                            return '<a lay-href="views/resource/media/list-hg.html?type=2" href="javascript:;" lay-text="研报审核">' + e.content + '</a>'
                        }
                        else if (e.type == 40 || e.type == 41) {
                            //荐股
                            return '<a lay-href="views/resource/recommend-stock/list-audit.html" href="javascript:;" lay-text="荐股审核">' + e.content + '</a>'
                        }
                        else if (e.type == 50) {
                            //产品
                            return '<a lay-href="views/resource/product/list.html" href="javascript:;" lay-text="产品列表">' + e.content + '</a>'
                        }
                        else if (e.type == 60) {
                            //产品活动
                            return '<a lay-href="views/resource/activity/list.html" href="javascript:;" lay-text="产品活动">' + e.content + '</a>'
                        }
                        else if (e.type == 70) {
                            //落地页
                            return '<a lay-href="views/resource/landing-page/list.html" href="javascript:;" lay-text="落地页管理">' + e.content + '</a>'
                        }
                        else if (e.type == 80) {
                            //退款订单审核
                            return '<a lay-href="views/resource/product-order/list-refund-audit.html" href="javascript:;" lay-text="退款审核">' + e.content + '</a>'
                        }
                        else if (e.type == 90) {
                            //退款订单财务审核
                            return '<a lay-href="views/resource/product-order/list-refund-financial.html" href="javascript:;" lay-text="财务退款">' + e.content + '</a>'
                        }
                        return e.content;
                    }
                },
                {
                    field: 'createdAt', title: '操作时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
            ], {}, '', null, null, function (res, curr, count) {
                console.log(curr);
                if (curr == 1) {
                    layui.systemNotice.updateIsRead();
                }
            });
        },
        /**
         * 查询未读消息列表
         * */
        queryUnRead: function () {
            var noticeId = localStorage.getItem('ly-admin-noticeid') || '';
            layui.request({
                method: 'post',
                url: '/admin/notification/system-notice/unread/query?lastId=' + noticeId,
                lodding: false,
            }).then(function (res) {
                if (res.isSuccess) {
                    if (res.result.length > 0) {
                        localStorage.setItem('ly-admin-noticeid', res.result[0].id);
                        if (typeof Notification !== 'undefined' && typeof window.Notification !== 'undefined') {
                            var notificationstatus = Notification.permission;
                            if (notificationstatus == "default") {
                                //浏览器默认设置时请求授权
                                Notification.requestPermission().then(function (permission) { });
                            }
                            var noticeTitle = '站内信';
                            var noticeBody = '你有' + res.result.length + '条未读消息，请及时处理！';
                            if (res.result.length == 1) {
                                noticeTitle = res.result[0].typeDesc + '通知';
                                noticeBody = res.result[0].content;
                            }
                            console.log(currentNotification)
                            if (notificationstatus == "granted") {
                                if (currentNotification) {
                                    currentNotification.close();
                                }
                                //浏览器已授权时，调用浏览器推送消息
                                currentNotification = new Notification(noticeTitle, {
                                    body: noticeBody,
                                    tag: res.result[0].id,
                                    icon: 'images/default_logo.jpg',
                                    requireInteraction: true,
                                });
                                currentNotification.onclick = function (event) {
                                    layui.$('#notice_link_btn').attr('lay-href', 'notice.html');
                                    layui.$('#notice_link_btn').click();
                                    this.close();
                                };
                            }
                            else {
                                //浏览器未授权时，推送站内通知
                                if (noticeDialog != null) {
                                    layui.layer.close(noticeDialog);
                                }
                                noticeDialog = layui.layer.open({
                                    type: 1,
                                    title: noticeTitle,
                                    shadeClose: false,
                                    offset: 'rb',
                                    btn: '前往查看',
                                    content: '<div style="padding: 20px 20px 0 20px;">' + noticeBody + '</div>',
                                    shade: 0,
                                    yes: function () {
                                        layui.$('#notice_link_btn').attr('lay-href', 'notice.html');
                                        layui.$('#notice_link_btn').click();
                                        layui.layer.close(noticeDialog);
                                        noticeDialog = null;
                                    }
                                });
                            }
                        }
                    }
                }
            })
        },
        /**
         * 更新站内信已读状态
         * */
        updateIsRead: function () {
            layui.request({
                method: 'post',
                url: '/admin/notification/system-notice/isread/update',
            }).then(function (res) {
            })
        },
    }

    exports('systemNotice', func);
});