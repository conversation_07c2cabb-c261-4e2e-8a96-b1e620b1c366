﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键字</label>
                        <div class="layui-input-inline">
                            <input id="txb_keywords" name="keywords" placeholder="标题/页面路径" type="text" class="layui-input" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="page-config-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list page-config-create"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="page-config-list" lay-filter="list"></table>
                <script type="text/html" id="page-config-bar">
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['common', 'pageConfig'], function () {
            layui.pageConfig.query();
            //监听查询按钮
            layui.form.on('submit(page-config-search)', function (data) {
                var field = data.field;
                layui.tableRequest.reload("page-config-list", {
                    where: field
                });
            });
            layui.$('.page-config-create').click(function () {
                layui.common.openIframe('创建落地页配置', 500, 300, 'create.html');
            });
        })
    </script>
</body>
</html>