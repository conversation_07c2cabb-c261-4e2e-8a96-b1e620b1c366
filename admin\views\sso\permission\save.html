﻿<!DOCTYPE html>
<html style="background:#fff;">
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/lib/font-awesome/font-awesome.min.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-form">
        <div class="layui-card" style="margin-bottom:80px;">
            <input id="objId" name="objId" type="hidden" />
            <input id="type" name="type" type="hidden" />
            <blockquote class="layui-elem-quote" style="display:none;">
                <div class="layui-row">
                    <div>
                        权限类型：<span class="type-name">-</span>，设置对象：<span class="obj-name">-</span>
                    </div>
                </div>
            </blockquote>
            <div>
                <table id="menu-list" lay-filter="list" class="layui-table">
                    <thead>
                        <tr>
                            <th style="width:110px;text-align:center;">应用</th>
                            <th style="min-width:220px"> <input type="checkbox" name="" lay-skin="primary" value="" title="&nbsp;菜单权限" lay-filter="allChoose"></th>
                            <th>功能权限</th>
                        </tr>
                    </thead>
                    <tbody id="menu-view">
                    </tbody>
                </table>
            </div>
        </div>
        <div class="layui-form-item" style="margin-top: 20px; position: fixed; bottom: 0; text-align: center; background: #fff; width: 100%; margin: 0; padding: 15px 0; border-top: 1px solid #e6e6e6">
            <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="permission-save-submit" value="保存设置">
            <input type="button" lay-submit="" class="layui-btn layui-btn-primary" lay-filter="cancel" value="返回上一页">
        </div>
    </div>
    <script id="menu-tpl" type="text/html">
        {{# if(d.length==0){}}
        <tr>
            <td colspan="3" style="text-align:center">无数据</td>
        </tr>
        {{# }}}

        {{#  layui.each(d, function(appIndex, item){ }}
        <tbody>
            {{# layui.each(item.menus, function(indexMenus, itemMenus){ }}
            <tr>
                {{# if(indexMenus==0){}}
                <td rowspan="{{item.rowspan}}" style="text-align:center">{{item.appName}}</td>
                {{# }}}
                <td> <input type="checkbox" class="cbk menu_cbk  menu_{{itemMenus.id}}" value="{{itemMenus.id}}" data-appid="{{item.appId}}" data-id="{{itemMenus.id}}" title="&nbsp;&nbsp;{{itemMenus.name}}" {{# if(itemMenus.isChecked){}} checked {{# }}} lay-skin="primary" lay-filter="parent-menu"></td>
                <td>
                    {{# layui.each(itemMenus.permissions, function(indexPermissions, itemPermissions){ }}
                    <input type="checkbox" class="cbk permission_{{itemMenus.id}}" value="{{itemPermissions.key}}" title="{{itemPermissions.name}}" {{# if(itemPermissions.isChecked){}} checked {{# }}} lay-skin="primary">
                    {{# }); }}
                </td>
            </tr>
            {{#  layui.each(itemMenus.childs, function(indexChild, itemChild){ }}
            <tr>
                <td>&nbsp;└─&nbsp;&nbsp;<input type="checkbox" class="cbk menu_cbk menu_child_{{itemMenus.id}} menu_child_2_{{itemChild.id}}" data-appid="{{item.appId}}" data-parentid="{{itemMenus.id}}" value="{{itemChild.id}}" title="{{itemChild.name}}" {{# if(itemChild.isChecked){}} checked {{# }}} lay-skin="primary" lay-filter="child-menu"></td>
                <td>
                    {{# layui.each(itemChild.permissions, function(indexChildPermissions, itemChildPermissions){ }}
                    <span style="display:inline-block;min-width:100px;">
                        <input type="checkbox" class="cbk permission_{{itemChild.id}}" value="{{itemChildPermissions.key}}" title="{{itemChildPermissions.name}}" {{# if(itemChildPermissions.isChecked){}} checked {{# }}} data-parentid="{{itemChild.id}}" lay-skin="primary" lay-filter="child-menu-permission">
                    </span>
                    {{# }); }}
                </td>
            </tr>
            {{#  }); }}
            {{#  }); }}
        </tbody>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['permission'], function () {
            layui.permission.init();

        });
    </script>
</body>
</html>