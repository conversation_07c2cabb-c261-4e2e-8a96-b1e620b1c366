﻿layui.define(['laytpl', 'form', 'request', 'tableRequest', 'common'], function (exports) {
    var func = {
        /**
         * 通过id获取企业信息
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                layui.common.alertAutoClose("参数有误");
                setTimeout(function () {
                    parent.location.reload();
                }, 3000);
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/sso/company/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    var getTpl = document.getElementById("company-update-tpl").innerHTML
                        , view = document.getElementById('company-update-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    })
                    layui.form.render('select');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取所有企业列表
         * */
        getAll: function (callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/sso/company/get-all',
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 获取企业列表
         * */
        query: function () {
            layui.tableRequest.request('resource', false, 'company-list', '/admin/sso/company/query', 'application/json', [
                { field: 'id', title: 'ID', fixed: 'left' },
                { field: 'name', title: '企业名称' },
                { field: 'corpid', title: '企业微信ID' },
                {
                    field: 'createdAt', title: '创建时间', templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', align: 'left', toolbar: '#company-bar' }
            ]);
            //监听表格事件
            layui.company.tableEvent();
        },
        /**
        * 创建企业
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/sso/company/create',
                data: JSON.stringify({
                    name: data.field.name,
                    corpid: data.field.corpid,
                    loginAgentSecret: data.field.loginAgentSecret
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("企业创建成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑企业
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/sso/company/update',
                data: JSON.stringify({
                    id: data.field.id,
                    name: data.field.name,
                    corpid: data.field.corpid,
                    loginAgentSecret: data.field.loginAgentSecret,
                    status: data.field.status
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("企业编辑成功");
                    setTimeout(function () {
                        layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除企业
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/sso/company/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("企业删除成功");
                    layui.company.query();
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑企业', 550, 350, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该企业吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.company.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('company', func);
});