﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-tab-content { padding: 0; }
    </style>
</head>
<body>
    <div class="layui-card layui-form">
        <input id="objId" name="objId" type="hidden" />
        <input id="type" name="type" type="hidden" />
        <div class="layui-card-body">
            <blockquote class="layui-elem-quote">
                <div class="layui-row">
                    <div>
                        权限类型：<span class="type-name">-</span>，设置对象：<span class="obj-name">-</span>
                    </div>
                </div>
            </blockquote>
            <div class="layui-tab" lay-filter="permission-tab">
                <ul class="layui-tab-title">
                    <li lay-id="1" class="layui-this">菜单功能权限</li>
                    <li lay-id="2">企微加粉方案权限</li>
                    <li lay-id="3">订单权限（产品包）</li>
                    <li lay-id="4">订单权限（产品）</li>
                    <li lay-id="5">订单权限（组织）</li>
                    <li lay-id="6">产品权限</li>
                    <li lay-id="7">素材/研报权限</li>
                    <li lay-id="8">直播管理</li>
                    <li lay-id="9">荐股中心</li>
                    <li lay-id="10">退款订单</li>
                </ul>
                <div class="layui-tab-content">
                    <iframe id="ifr" src="" style="border:none;width:100%;height:100%;"></iframe>
                </div>
            </div>
        </div>
    </div>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>

    <script type="text/javascript">
        layui.use(['jquery', 'common', 'element', 'laytpl'], function () {
            layui.$("iframe").height(layui.$(window).height() - 145);
            layui.$(window).resize(function () {
                layui.$("iframe").height(layui.$(window).height() - 145);
            });
            var id = layui.common.getUrlParam('id') || '';
            if (id == '') {
                layui.common.alert('对象id有误', 0);
                return;
            }
            layui.$('#objId').val(id);
            var type = layui.common.getUrlParam('type');
            var permissionType = 0;
            if (type == 'user') {
                layui.$('.type-name').text('用户');
                permissionType = 1;

            }
            else if (type == 'role') {
                layui.$('.type-name').text('角色');
                permissionType = 2;
            }
            else if (type == 'dep') {
                layui.$('.type-name').text('部门');
                permissionType = 3;
            }
            else {
                layui.common.alert('权限类型有误', 0);
                return;
            }
            if (!layui.common.getPermission('63369ab96918e5623bad0f65', 'sso.user.data.permission')) {
                layui.element.tabDelete('permission-tab', 2);
            }
            if (!layui.common.getPermission('63369ab96918e5623bad0f65', 'sso.user.data.order.permission')) {
                layui.element.tabDelete('permission-tab', 3);
            }
            if (!layui.common.getPermission('63369ab96918e5623bad0f65', 'sso.user.data.product.permission.order')) {
                layui.element.tabDelete('permission-tab', 4);
            }
            if (!layui.common.getPermission('63369ab96918e5623bad0f65', 'sso.user.data.subsidiary.permission.order')) {
                layui.element.tabDelete('permission-tab', 5);
            }
            if (!layui.common.getPermission('63369ab96918e5623bad0f65', 'sso.user.data.subsidiary.permission.product')) {
                layui.element.tabDelete('permission-tab', 6);
            }
            if (!layui.common.getPermission('63369ab96918e5623bad0f65', 'sso.user.data.subsidiary.permission.media')) {
                layui.element.tabDelete('permission-tab', 7);
            }
            if (!layui.common.getPermission('63369ab96918e5623bad0f65', 'sso.user.data.subsidiary.permission.live')) {
                layui.element.tabDelete('permission-tab', 8);
            }
            if (!layui.common.getPermission('63369ab96918e5623bad0f65', 'sso.user.data.subsidiary.permission.stock')) {
                layui.element.tabDelete('permission-tab', 9);
            }
            if (!layui.common.getPermission('63369ab96918e5623bad0f65', 'sso.user.data.subsidiary.permission.refundorder')) {
                layui.element.tabDelete('permission-tab', 10);
            }
            layui.$('#type').val(type);
            var objName = layui.common.getUrlParam('name') || '-';
            layui.$('.obj-name').text(objName);
            var page = layui.common.getUrlParam('page') || '';
            if (page == '') {
                layui.$('#ifr').attr('src', '/admin/views/sso/permission/save.html?id=' + id + '&type=' + type);
            }

            layui.element.on('tab(permission-tab)', function () {
                var layId = this.getAttribute('lay-id');
                if (layId == 2) {
                    layui.$('#ifr').attr('src', '../../resource/external-contact/set-permission.html?permissionType=' + permissionType + '&id=' + id + '&name=' + name);
                }
                else if (layId == 3) {
                    layui.$('#ifr').attr('src', '../../resource/product-order/set-package-permission.html?permissionType=' + permissionType + '&id=' + id + '&name=' + name);
                }
                else if (layId == 4) {
                    layui.$('#ifr').attr('src', '../../resource/product-order/set-product-permission.html?permissionType=' + permissionType + '&id=' + id + '&name=' + name);
                }
                else if (layId == 5) {
                    layui.$('#ifr').attr('src', '../../resource/data-permission/save.html?permissionType=' + permissionType + '&id=' + id + '&type=order&name=' + name);
                }
                else if (layId == 6) {
                    layui.$('#ifr').attr('src', '../../resource/data-permission/save.html?permissionType=' + permissionType + '&id=' + id + '&type=product&name=' + name);
                }
                else if (layId == 7) {
                    layui.$('#ifr').attr('src', '../../resource/data-permission/save.html?permissionType=' + permissionType + '&id=' + id + '&type=media&name=' + name);
                }
                else if (layId == 8) {
                    layui.$('#ifr').attr('src', '../../resource/data-permission/save.html?permissionType=' + permissionType + '&id=' + id + '&type=live&name=' + name);
                }
                else if (layId == 9) {
                    layui.$('#ifr').attr('src', '../../resource/data-permission/save.html?permissionType=' + permissionType + '&id=' + id + '&type=stock&name=' + name);
                }
                else if (layId == 10) {
                    layui.$('#ifr').attr('src', '../../resource/data-permission/save.html?permissionType=' + permissionType + '&id=' + id + '&type=refundorder&name=' + name);
                }
                else {
                    layui.$('#ifr').attr('src', '/admin/views/sso/permission/save.html?permissionType=' + permissionType + '&id=' + id + '&type=' + type);
                }
            });
        });
        function historyBack() {
            var referrerUrl = document.referrer;
            location.href = referrerUrl;
        }
    </script>
</body>
</html>