﻿layui.define(['jquery', 'laytpl', 'form', 'request', 'tableRequest', 'common', 'workWxApp'], function (exports) {
    var func = {
        /**
         * 通过企微应用id获取所有客服账号
         * @param {any} workWxAppId
         * @param {any} callbackFunc
         */
        getAll: function (workWxAppId, callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-account/get-all?workWxAppId=' + workWxAppId,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                callbackFunc(res);
            })
        },
        /**
         * 获取客服列表
         * */
        query: function () {
            var workWxAppId = layui.common.getUrlParam('workWxAppId');
            layui.workWxApp.getAll(function (res) {
                var getTpl = document.getElementById("workwx-app-tpl").innerHTML
                    , view = document.getElementById('workwx-app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                //首次进入默认显示第一个企微应用的客服账号
                if (workWxAppId == '') {
                    layui.$("#workwx-app-view").val(res.result[0].id);
                    workWxAppId = res.result[0].id;
                }
                else {
                    layui.$("#workwx-app-view").val(workWxAppId);
                }
                layui.form.render('select');

                //加载微信客服账号列表
                layui.tableRequest.request('resource', false, 'kf-account-list', '/admin/workwx/kf-account/query?workWxAppId=' + workWxAppId, 'application/json', [
                    { field: 'open_kfid', title: '客服ID', fixed: 'left' },
                    {
                        field: 'avatar', title: '头像', templet: function (e) {
                            return '<a target="_blank" href="' + e.avatar + '"><img src="' + e.avatar + '" style="height:25px"/></a>';
                        }
                    },
                    { field: 'name', title: '名称' },
                    { fixed: 'right', title: '操作', width: 150, align: 'left', toolbar: '#kf-account-bar' }
                ]);

                //绑定搜索事件
                layui.form.on('select(kf-account-search)', function (data) {
                    location.href = 'list.html?workWxAppId=' + layui.$("#workwx-app-view").val();
                });

                //监听表格事件
                layui.kfAccount.tableEvent(workWxAppId);

                //绑定创建客服账号事件
                layui.$('.kf-account-create').click(function () {
                    layui.common.openIframe('创建客服账号', 650, 560, 'create.html?workWxAppId=' + workWxAppId);
                });
                layui.$('.kf-account-sync').click(function () {
                    layui.kfAccount.sync();
                });
            })
        },
        /**
        * 创建客服列表
        * */
        create: function (data) {
            layui.jquery(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-account/create',
                data: JSON.stringify({
                    name: data.field.name,
                    mediaId: data.field.mediaId,
                    workWxAppId: data.field.workWxAppId
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("客服账号创建成功");
                    setTimeout(function () {
                        layui.jquery(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.jquery(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑客服列表
         * @param {any} data
         */
        update: function (data) {
            layui.jquery(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-account/update',
                data: JSON.stringify({
                    openKfId: data.field.openKfId,
                    name: data.field.name,
                    mediaId: data.field.mediaId,
                    workWxAppId: data.field.workWxAppId
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("客服账号编辑成功");
                    setTimeout(function () {
                        layui.jquery(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                        parent.location.reload();
                    }, 3000);
                }
                else {
                    layui.jquery(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除客服列表
         * @param {any} data
         */
        delete: function (workWxAppId, openKfId) {
            layui.request({
                method: 'post',
                data: JSON.stringify({
                    workWxAppId: workWxAppId,
                    openKfId: openKfId
                }),
                headers: { 'Content-Type': 'application/json' },
                url: '/admin/workwx/kf-account/delete',
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.common.alertAutoClose("客服账号删除成功");
                    layui.tableRequest.reload('kf-account-list');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 同步客服列表
         * @param {any} data
         */
        sync: function () {
            var confirmIndex = layui.layer.confirm('确定同步微信客服账号吗？', {
                icon: 3,
                title: '提示',
                btn: ['确定', '取消']
            }, function () {
                layui.request({
                    method: 'post',
                    headers: { 'Content-Type': 'application/json' },
                    url: '/admin/workwx/kf-account/sync?workWxAppId=' + layui.$('#workwx-app-view').val(),
                }).then(function (res) {
                    if (res.isSuccess) {
                        layui.common.alertAutoClose(res.result);
                        layui.tableRequest.reload('kf-account-list');
                    }
                    else {
                        layui.common.alert(res.message, 0);
                    }
                })
            }, function () {
                layui.layer.close(confirmIndex);
            });
        },
        /**
        * 监听表格单击事件
        * */
        tableEvent: function (workWxAppId) {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑客服账号', 750, 560, 'update.html?id=' + data.open_kfid + '&workWxAppId=' + workWxAppId + '&name=' + data.name + '&avatar=' + data.avatar);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('删除客服账号后，历史企微加粉方案将失效，确定删除该账号吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.kfAccount.delete(workWxAppId, data.open_kfid);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        }
    }

    exports('kfAccount', func);
});