﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 80px; }

        .layui-form-item .layui-input-inline { width: 600px; }

        .file-list { width: 100%; margin-top: 5px; }

        .file-list li { width: 100%; line-height: 24px; padding: 3px 10px; box-sizing: border-box; float: left; display: flex; }

        .file-list li:hover { background: #eee; }

        .file-list li a { font-size: 14px; color: #0094ff; display: inline-block; width: 300px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden; }

        .file-list li i.layui-icon-link { font-size: 14px; }

        .file-list li i.remove { float: right; cursor: pointer; display: none; }
    </style>
</head>

<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list"
         style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">所属组织</label>
            <div class="layui-input-inline">
                <select name="subsidiaryId" disabled="disabled" id="subsidiary-view" lay-search></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">标题</label>
            <div class="layui-input-inline">
                <input type="text" name="title" readonly="readonly" lay-verify="required" placeholder="请输入标题"
                       autocomplete="off" class="layui-input layui-disabled">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">渠道名称</label>
            <div class="layui-input-inline" style="width:230px">
                <input type="text" name="channelName" readonly="readonly" lay-verify="required" placeholder="请输入渠道名称"
                       autocomplete="off" class="layui-input layui-disabled">
            </div>
            <label class="layui-form-label" style="width:100px;">对外内容路径</label>
            <div class="layui-input-inline" style="width:230px">
                <select name="contentPath" disabled="disabled" id="contentPath" lay-verify="required"
                        class=" layui-disabled">
                    <option value="">请选择对外内容路径</option>
                    <option value="企业微信">企业微信</option>
                    <option value="新媒体">新媒体</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">附件</label>
            <div class="layui-input-inline">
                <div style="float: left; width: 480px;">
                    <ul class="file-list">
                    </ul>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">内容</label>
            <div class="layui-input-inline">
                <textarea type="text" name="content" readonly="readonly" id="content" placeholder="请输入送审内容"
                          autocomplete="off" class="layui-textarea layui-disabled"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-inline">
                <input type="text" name="remark" readonly="readonly" placeholder="请输入备注" autocomplete="off"
                       class="layui-input layui-disabled">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-inline">
                <input type="radio" name="status" value="3" title="通过">
                <input type="radio" name="status" value="4" title="驳回">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">审核意见</label>
            <div class="layui-input-inline">
                <input type="text" name="auditRemark" id="auditRemark" placeholder="请输入审核意见" autocomplete="off"
                       class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" id="btn" class="layui-btn layui-btn-normal"
                       lay-filter="audit-status-submit" value="确认审核">
            </div>
        </div>
    </div>
    <script id="subsidiary-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/tinymce/tinymce.min.js"></script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js?v=1.3"></script>
    <script type="text/javascript">
        layui.use(['media'], function () {
            layui.media.get();
            var isRead = layui.common.getUrlParam('isRead');
            if (isRead == 1) {
                layui.$('#btn').hide();
                layui.$('#auditRemark').attr('readonly', 'readonly');
                layui.$('#auditRemark').addClass('layui-disabled');
                layui.$('input[name=status]').attr('disabled', 'disabled');
            }
            //监听提交事件
            layui.form.on('submit(audit-status-submit)', function (data) {
                var checked = layui.$('input[name=status]:checked').val();
                if (checked == undefined || checked == '') {
                    layui.common.alertAutoClose('请选择审核状态');
                    return;
                }
                layui.media.updateStatusForFk(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>