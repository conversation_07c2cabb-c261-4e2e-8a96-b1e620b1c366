﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 400px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">绑定应用</label>
            <div class="layui-input-inline">
                <select name="oceanengineAppId" id="oceanengine-app-view" lay-search>
                    <option value="">请选择巨量应用</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">账户ID</label>
            <div class="layui-input-inline">
                <input type="text" name="accountId" lay-verify="required" placeholder="请输入巨量账户ID" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">账户名称</label>
            <div class="layui-input-inline">
                <input type="text" name="accountName" lay-verify="required" placeholder="请输入巨量账户名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">所属企微</label>
            <div class="layui-input-inline">
                <select name="workWxAppId" id="workwx-app-view" lay-filter="workwx-app-id">
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">加粉方案</label>
            <div class="layui-input-inline">
                <select name="kfCaseId" id="kf-case-view" lay-search>
                    <option value="">请选择加粉方案</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="account-create-submit" value="确认添加">
            </div>
        </div>
    </div>
    <script id="oceanengine-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script id="kf-case-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'oceanengineAccount'], function () {
            layui.oceanengineAccount.initOceanengineApp();
            layui.oceanengineAccount.initWorkWxApp();
            //监听提交事件
            layui.form.on('submit(account-create-submit)', function (data) {
                layui.oceanengineAccount.create(data);
                return false; //阻止表单跳转
            });
            //切换企微应用时加载加粉方案
            layui.form.on('select(workwx-app-id)', function (data) {
                layui.kfCase.queryByWorkWxAppId(data.value, function (res) {
                    if (res.result == null) {
                        res.result = [{ id: '', name: '请选择加粉方案' }]
                    }
                    else {
                        res.result.unshift({ id: '', name: '请选择加粉方案' });
                    }
                    var getTpl = document.getElementById("kf-case-tpl").innerHTML
                        , view = document.getElementById('kf-case-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.form.render('select');
                });
            });
        })
    </script>
</body>
</html>