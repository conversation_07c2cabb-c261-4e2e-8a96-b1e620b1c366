﻿layui.define(['laytpl', 'request', 'tableRequest', 'common', 'setter', 'uploadFile', 'xmSelect', 'dic', 'workWxApp', 'kfAccount', 'tencentAccount', 'qrcode'], function (exports) {
    var func = {
        /**
         * 渲染企微应用下拉选框
         * */
        initApp: function (id) {
            layui.workWxApp.getAll(function (res) {
                res.result.unshift({ id: '', name: '请选择企微' });
                var getTpl = document.getElementById("workwx-app-tpl").innerHTML
                    , view = document.getElementById('workwx-app-view');
                layui.laytpl(getTpl).render(res.result, function (html) {
                    view.innerHTML = html;
                });
                if (id != undefined) {
                    document.getElementById('workwx-app-view').value = id;
                }
                layui.form.render('select');
            });
        },
        /**
         * 通过应用id获取企微成员列表
         * @param {any} workWxAppId
         * @param {any} selectIds
         */
        initWorkUser: function (workWxAppId, selectIds) {
            layui.request({
                requestBase: 'resource',
                method: 'post',
                url: '/admin/workwx/department-user/get-all?workWxAppId=' + workWxAppId,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    var data = [];
                    if (res.result.length > 0) {
                        for (var i = 0; i < res.result.length; i++) {
                            var item = { name: res.result[i].name, value: res.result[i].userId };
                            data.push(item);
                        }
                    }
                    xmSelUser = layui.xmSelect.render({
                        el: '#workwx-user',
                        language: 'zn',
                        filterable: true,
                        tips: '请选择承接用户',
                        theme: { color: '#0081ff ' },
                        data: data,
                        toolbar: { show: true },
                        autoRow: true,
                    });
                    var selectItems = [];
                    if (selectIds.length > 0) {
                        for (var i = 0; i < selectIds.length; i++) {
                            selectItems.push(data.find(function (item) {
                                return item.value === selectIds[i];
                            }))
                        }
                    }
                    if (selectItems.length > 0) {
                        xmSelUser.setValue(selectItems);
                    }
                }
            })
        },
        /**
         * 获取所有企微加粉方案列表
         * */
        getAll: function (callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/get-all',
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 通过id获取客服加粉方案
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('#welcomeStartMessage').val(res.result.welcomeStartMessage);
                    layui.$('#welcomeEndMessage').val(res.result.welcomeEndMessage);
                    layui.$('input[name=state]').val(res.result.state);
                    layui.$('input[name=qrCodeUrl]').val(res.result.qrCodeUrl);
                    layui.$('input[name=tencentAdPageId]').val(res.result.tencentAdPageId);
                    layui.$('input[name=baiDuToken]').val(res.result.baiDuToken);
                    layui.$('input[name=invalidRate]').val(res.result.invalidRate);
                    layui.$('input[name=xiaoetongRuleId]').val(res.result.xiaoetongRuleId);
                    layui.$('input[name=xiaoetongKfUrl]').val(res.result.xiaoetongKfUrl);

                    layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.result.qrCodeUrl);
                    //加载企微应用列表
                    layui.kfCase.initApp(res.result.workWxAppId);
                    //加载客服列表
                    layui.kfAccount.getAll(res.result.workWxAppId, function (resKf) {
                        var options = '<option value="">请选择微信客服</option>'
                        if (resKf.isSuccess) {
                            for (var i = 0; i < resKf.result.length; i++) {
                                options += '<option ' + (res.result.openKfId == resKf.result[i].open_kfid ? 'selected="true"' : '') + ' value="' + resKf.result[i].open_kfid + '">' + resKf.result[i].name + '</option>';
                            }
                        }
                        layui.$('#openkf-id').html(options);
                        layui.form.render('select');
                    });
                    //加载使用场景
                    layui.dic.query('workwx_scene', function (resScene) {
                        var options = '<option value="">请选择使用场景</option>'
                        if (resScene.isSuccess) {
                            for (var i = 0; i < resScene.result.length; i++) {
                                options += '<option ' + (res.result.scene == resScene.result[i].key ? 'selected="true"' : '') + ' value="' + resScene.result[i].key + '">' + resScene.result[i].value + '</option>';
                            }
                        }
                        if (res.result.scene == 'tenxun_sph') {
                            layui.$(".tencent-ad-account").show();
                            //加载腾讯推广账户
                            layui.tencentAccount.getAll(res.result.workWxAppId, function (resTencent) {
                                var optionsTencent = '<option value="">请选择推广账户</option>'
                                if (resTencent.isSuccess) {
                                    for (var i = 0; i < resTencent.result.length; i++) {
                                        optionsTencent += '<option ' + (res.result.tencentAdAccountId == resTencent.result[i].id ? 'selected="true"' : '') + ' value="' + resTencent.result[i].id + '">' + resTencent.result[i].name + '</option>';
                                    }
                                }
                                layui.$('#tencentAdAccountId').html(optionsTencent);
                                layui.form.render('select');
                            })
                        }
                        layui.$('#scene').html(options);
                        layui.form.render('select');
                    });
                    //加载菜单项配置
                    if (res.result.kfCaseMenu.length > 0) {
                        var imgIds = [];
                        for (var i = 0; i < res.result.kfCaseMenu.length; i++) {
                            var menuTpl = '<div class="menu-input-item" style="margin-bottom:10px;float:left">';
                            menuTpl += '<div class="layui-input-inline" style="width:120px">';
                            menuTpl += '<select lay-filter="menu-type" class="menu-type" lay-verify="required">';
                            menuTpl += '<option value="">菜单类型</option>';
                            menuTpl += '<option ' + (res.result.kfCaseMenu[i].type == 'click' ? 'selected="true"' : '') + ' value="click">回复菜单</option>';
                            menuTpl += '<option ' + (res.result.kfCaseMenu[i].type == 'view' ? 'selected="true"' : '') + ' value="view">超链接菜单</option>';
                            menuTpl += '</select>';
                            menuTpl += '</div>';
                            menuTpl += '<div class="layui-input-inline" style="width: 420px; ">';
                            menuTpl += '<div class="layui-input-inline" style="width:350px">';
                            menuTpl += '<input type="text" lay-verify="required" placeholder="菜单名称，多个相同回复的不同菜单名称使用 | 分隔" value="' + res.result.kfCaseMenu[i].name + '" autocomplete="off" class="layui-input menu-name">';
                            menuTpl += '</div>';
                            menuTpl += '<div class="layui-input-inline" style="width:50px;">';
                            menuTpl += '<button type="button" class="layui-btn layui-btn-primary remove-menu">';
                            menuTpl += '<i class="layui-icon">&#xe640;</i>';
                            menuTpl += '</button>';
                            menuTpl += '</div>';
                            menuTpl += '<div class="reply-container">';
                            if (res.result.kfCaseMenu[i].type == 'click') {
                                menuTpl += '<div class="reply-list">';
                                var autoReplyMsgList = res.result.kfCaseMenu[i].autoReplyMsgList;
                                if (autoReplyMsgList != null && autoReplyMsgList.length > 0) {
                                    for (var j = 0; j < autoReplyMsgList.length; j++) {
                                        menuTpl += '<div class="layui-input-inline reply-item" style="width: 420px; margin-top: 10px;">';
                                        menuTpl += '<div class="layui-input-inline" style="width:350px;">';
                                        menuTpl += '<div class="layui-input-inline" style="width:100px;">';
                                        menuTpl += '<select lay-filter="reply-type" class="reply-type" lay-verify="required">';
                                        menuTpl += '<option value="">消息类型</option>';
                                        menuTpl += '<option ' + (autoReplyMsgList[j].type == 'image' ? 'selected="true"' : '') + ' value="image">图片消息</option>';
                                        menuTpl += '<option ' + (autoReplyMsgList[j].type == 'text' ? 'selected="true"' : '') + ' value="text">文本消息</option>';
                                        menuTpl += '</select>';
                                        menuTpl += '</div>';
                                        menuTpl += '<div class="layui-input-inline" style="width:240px;margin-right:0;">';
                                        menuTpl += '<div class="reply-content">';
                                        if (autoReplyMsgList[j].type == 'text') {
                                            menuTpl += '<textarea type="text" lay-verify="required" placeholder="请输入回复文本消息内容" autocomplete="off" class="layui-textarea reply-text">' + autoReplyMsgList[j].content + '</textarea>';
                                        }
                                        else if (autoReplyMsgList[j].type == 'image') {
                                            imgIds.push(autoReplyMsgList[j].imageId);
                                            menuTpl += '<button type="button" class="layui-btn layui-btn-primary" id="' + autoReplyMsgList[j].imageId + '"><i class="layui-icon"></i>上传图片</button>';
                                            menuTpl += '<img class="reply-img" id="img_' + autoReplyMsgList[j].imageId + '" src="' + autoReplyMsgList[j].imageUrl + '" style="width: 35px; height: 35px;margin-left:10px;" />';
                                            menuTpl += '<input class="imageUrl" id="imgUrl_' + autoReplyMsgList[j].imageId + '" value="' + autoReplyMsgList[j].imageUrl + '" type="hidden" /><input class="imageId" id="imgId_' + autoReplyMsgList[j].imageId + '" value="' + autoReplyMsgList[j].imageId + '" type="hidden" /><input class="imagePath" id="imgPath_' + autoReplyMsgList[j].imageId + '" value="' + autoReplyMsgList[j].imagePath + '" type="hidden" />';
                                        }
                                        menuTpl += '</div>';
                                        menuTpl += '</div>';
                                        menuTpl += '</div>';
                                        menuTpl += '<div class="layui-input-inline" style="width:50px;">';
                                        menuTpl += '<button type="button" class="layui-btn layui-btn-primary remove-reply">';
                                        menuTpl += '<i class="layui-icon">&#xe640;</i>';
                                        menuTpl += '</button>';
                                        menuTpl += '</div>';
                                        menuTpl += '</div>';
                                    }
                                }

                                menuTpl += '</div>';
                                menuTpl += '<div class="layui-input-inline" style="margin-bottom:10px; margin-top:10px;width:100px;">';
                                menuTpl += '<button type="button" class="layui-btn layui-btn-primary add-reply">';
                                menuTpl += '<i class="layui-icon">&#xe654;</i> 添加回复';
                                menuTpl += '</button>';
                                menuTpl += '</div>';
                            }
                            else if (res.result.kfCaseMenu[i].type == 'view') {
                                menuTpl += '<div class="layui-input-inline" style="margin-bottom:10px; margin-top:10px;width:350px;">';
                                menuTpl += '<input type="text" lay-verify="required" placeholder="请输入跳转地址" autocomplete="off" value="' + res.result.kfCaseMenu[i].url + '" class="layui-input menu-link">';
                                menuTpl += '</div>';
                            }
                            menuTpl += '</div>';
                            menuTpl += '</div>';
                            menuTpl += '</div>';
                            layui.$('.menu-input-list').append(menuTpl);
                        }
                        layui.form.render('select');
                        //渲染上传控件
                        for (var i = 0; i < imgIds.length; i++) {
                            var imgId = imgIds[i];
                            layui.uploadFile('Resource', imgId, '/admin/workwx/kf-case/qrcode/upload', function (res) {
                                if (res.isSuccess) {
                                    layui.$("#imgId_" + imgId).val(res.result.imageId);
                                    console.log(layui.$("#imgId_" + imgId).val())
                                    layui.$("#imgPath_" + imgId).val(res.result.imagePath);
                                    layui.$("#imgUrl_" + imgId).val(res.result.url);
                                    layui.$('#img_' + imgId).attr('src', res.result.url);
                                    layui.common.alertAutoClose("上传成功");
                                }
                                else {
                                    layui.common.alert(res.message, 2);
                                }
                            });
                        }
                    }
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过id获取客服加粉方案（系统生成活码方式）
         * */
        getSystem: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('#welcomeStartMessage').val(res.result.welcomeStartMessage);
                    layui.$('input[name=tencentAdPageId]').val(res.result.tencentAdPageId);
                    layui.$('input[name=baiDuToken]').val(res.result.baiDuToken);
                    layui.$('input[name=invalidRate]').val(res.result.invalidRate);
                    layui.$('input[name=cycle]').val(res.result.cycle);
                    layui.$('input[name=maxAddService]').val(res.result.maxAddService);
                    layui.$('input[name=menuAutoReplyTips]').val(res.result.menuAutoReplyTips);
                    layui.$('input[name=linkUrl]').val(res.result.linkUrl);
                    layui.$('input[name=xiaoetongRuleId]').val(res.result.xiaoetongRuleId);
                    layui.$('input[name=xiaoetongKfUrl]').val(res.result.xiaoetongKfUrl);

                    //加载菜单项配置
                    if (res.result.kfCaseMenu.length > 0) {
                        var menuNames = '';
                        for (var i = 0; i < res.result.kfCaseMenu.length; i++) {
                            if (menuNames != '') {
                                menuNames += '|';
                            }
                            menuNames += res.result.kfCaseMenu[i].name;
                        }
                        layui.$('#menus').val(menuNames)
                    }

                    //加载企微应用列表
                    layui.kfCase.initApp(res.result.workWxAppId);
                    //加载客服列表
                    layui.kfAccount.getAll(res.result.workWxAppId, function (resKf) {
                        var options = '<option value="">请选择微信客服</option>'
                        if (resKf.isSuccess) {
                            for (var i = 0; i < resKf.result.length; i++) {
                                options += '<option ' + (res.result.openKfId == resKf.result[i].open_kfid ? 'selected="true"' : '') + ' value="' + resKf.result[i].open_kfid + '">' + resKf.result[i].name + '</option>';
                            }
                        }
                        layui.$('#openkf-id').html(options);
                        layui.form.render('select');
                    });
                    //加载使用场景
                    layui.dic.query('workwx_scene', function (resScene) {
                        var options = '<option value="">请选择使用场景</option>'
                        if (resScene.isSuccess) {
                            for (var i = 0; i < resScene.result.length; i++) {
                                options += '<option ' + (res.result.scene == resScene.result[i].key ? 'selected="true"' : '') + ' value="' + resScene.result[i].key + '">' + resScene.result[i].value + '</option>';
                            }
                        }
                        if (res.result.scene == 'tenxun_sph') {
                            layui.$(".tencent-ad-account").show();
                            //加载腾讯推广账户
                            layui.tencentAccount.getAll(res.result.workWxAppId, function (resTencent) {
                                var optionsTencent = '<option value="">请选择推广账户</option>'
                                if (resTencent.isSuccess) {
                                    for (var i = 0; i < resTencent.result.length; i++) {
                                        optionsTencent += '<option ' + (res.result.tencentAdAccountId == resTencent.result[i].id ? 'selected="true"' : '') + ' value="' + resTencent.result[i].id + '">' + resTencent.result[i].name + '</option>';
                                    }
                                }
                                layui.$('#tencentAdAccountId').html(optionsTencent);
                                layui.form.render('select');
                            })
                        }
                        layui.$('#scene').html(options);
                        layui.form.render('select');
                    });
                    //渲染承接用户
                    var userIds = [];
                    for (var i = 0; i < res.result.services.length; i++) {
                        userIds.push(res.result.services[i].userId);
                    }
                    layui.kfCase.initWorkUser(res.result.workWxAppId, userIds);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 通过id获取客服加粉方案（系统生成活码方式v3）
        * */
        getV3: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('#welcomeStartMessage').val(res.result.welcomeStartMessage);
                    layui.$('input[name=tencentAdPageId]').val(res.result.tencentAdPageId);
                    layui.$('input[name=baiDuToken]').val(res.result.baiDuToken);
                    layui.$('input[name=cycle]').val(res.result.cycle);
                    layui.$('input[name=maxAddService]').val(res.result.maxAddService);

                    //加载菜单项配置
                    if (res.result.kfCaseMenu.length > 0) {
                        var menuNames = '';
                        for (var i = 0; i < res.result.kfCaseMenu.length; i++) {
                            if (menuNames != '') {
                                menuNames += '|';
                            }
                            menuNames += res.result.kfCaseMenu[i].name;
                        }
                        layui.$('#menus').val(menuNames)
                    }

                    //加载企微应用列表
                    layui.kfCase.initApp(res.result.workWxAppId);
                    //加载客服列表
                    layui.kfAccount.getAll(res.result.workWxAppId, function (resKf) {
                        var options = '<option value="">请选择微信客服</option>'
                        if (resKf.isSuccess) {
                            for (var i = 0; i < resKf.result.length; i++) {
                                options += '<option ' + (res.result.openKfId == resKf.result[i].open_kfid ? 'selected="true"' : '') + ' value="' + resKf.result[i].open_kfid + '">' + resKf.result[i].name + '</option>';
                            }
                        }
                        layui.$('#openkf-id').html(options);
                        layui.form.render('select');
                    });
                    layui.qrcode.queryByWorkWxAppId(res.result.workWxAppId, function (resQrCode) {
                        var options = '<option value="">请选择企微活码</option>'
                        if (resQrCode.isSuccess) {
                            for (var i = 0; i < resQrCode.result.length; i++) {
                                options += '<option ' + (res.result.workWxQrCodeId == resQrCode.result[i].id ? 'selected="true"' : '') + ' value="' + resQrCode.result[i].id + '">' + resQrCode.result[i].name + '</option>';
                            }
                        }
                        layui.$('#workWxQrCodeId').html(options);
                        layui.form.render('select');
                    });
                    //加载使用场景
                    layui.dic.query('workwx_scene', function (resScene) {
                        var options = '<option value="">请选择使用场景</option>'
                        if (resScene.isSuccess) {
                            for (var i = 0; i < resScene.result.length; i++) {
                                options += '<option ' + (res.result.scene == resScene.result[i].key ? 'selected="true"' : '') + ' value="' + resScene.result[i].key + '">' + resScene.result[i].value + '</option>';
                            }
                        }
                        if (res.result.scene == 'tenxun_sph') {
                            layui.$(".tencent-ad-account").show();
                            //加载腾讯推广账户
                            layui.tencentAccount.getAll(res.result.workWxAppId, function (resTencent) {
                                var optionsTencent = '<option value="">请选择推广账户</option>'
                                if (resTencent.isSuccess) {
                                    for (var i = 0; i < resTencent.result.length; i++) {
                                        optionsTencent += '<option ' + (res.result.tencentAdAccountId == resTencent.result[i].id ? 'selected="true"' : '') + ' value="' + resTencent.result[i].id + '">' + resTencent.result[i].name + '</option>';
                                    }
                                }
                                layui.$('#tencentAdAccountId').html(optionsTencent);
                                layui.form.render('select');
                            })
                        }
                        layui.$('#scene').html(options);
                        layui.form.render('select');
                    });
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取企业标签
         * */
        getCorpTag: function () {
            var id = layui.common.getUrlParam('id') || '';
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/corp-tag/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    var getTpl = document.getElementById("tag-tpl").innerHTML
                        , view = document.getElementById('tag-view');
                    layui.laytpl(getTpl).render(res.result, function (html) {
                        view.innerHTML = html;
                    });
                    layui.$(document).on('click', '.tag-btn', function () {
                        var that = layui.$(this);
                        if (that.hasClass('cur')) {
                            that.removeClass('cur');
                        }
                        else {
                            that.addClass('cur');
                        }
                    });
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 通过企微应用id获取客服加粉方案列表
         * */
        queryByWorkWxAppId: function (workWxAppId, callbackFunc, type) {
            var url = '/admin/workwx/kf-case/by-workwx-app-id/query?workWxAppId=' + workWxAppId;
            if (type == 'permission') {
                url = '/admin/workwx/kf-case/permission/by-workwx-app-id/query?workWxAppId=' + workWxAppId;
            }
            layui.request({
                method: 'post',
                url: url,
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                callbackFunc(res);
            })
        },
        /**
         * 获取客服加粉方案列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'kf-case-list', '/admin/workwx/kf-case/query', 'application/json', [
                {
                    field: 'id', title: 'ID', fixed: 'left', templet: function (e) {
                        if (layui.setter.companyId == '6340ec834d7e5a21ed46e995') {
                            return '<a target="_blank" href="https://sg1.sdshenguang.com/pages/dy/sg/ty01/index.html?cid=' + e.id + '" class="link">' + e.id + '</a>';
                        }
                        else {
                            return e.id;
                        }
                    }
                },
                { field: 'name', title: '方案名称' },
                { field: 'workWxAppName', title: '所属企微' },
                { field: 'scene', title: '使用场景' },
                {
                    field: 'invalidRate', title: '无效资源比例', templet: function (e) {
                        return (e.invalidRate * 100) + '%';
                    }
                },
                {
                    title: '标签', minWidth: 180, templet: function (e) {
                        var tags = '';
                        if (e.corpTags != null && e.corpTags.length > 0) {
                            for (var i = 0; i < e.corpTags.length; i++) {
                                tags += '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary">' + e.corpTags[i] + '</button>';
                            }
                        }
                        else {
                            tags = '-';
                        }
                        return tags;
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 180, align: 'left', toolbar: '#kf-case-bar' }
            ]);
            //监听表格事件
            layui.kfCase.tableEvent();
        },
        /**
       * 获取客服加粉方案列表(系统生成活码方式)
       * */
        querySystem: function () {
            layui.tableRequest.request('resource', true, 'kf-case-list', '/admin/workwx/kf-case/query', 'application/json', [
                { type: 'checkbox', fixed: 'left' },
                {
                    field: 'id', title: 'ID', fixed: 'left', templet: function (e) {
                        if (layui.setter.companyId == '6340ec834d7e5a21ed46e995') {
                            return '<a target="_blank" href="https://sg1.sdshenguang.com/pages/dy/sg/ty01/index.html?cid=' + e.id + '" class="link">' + e.id + '</a>';
                        }
                        else {
                            return e.id;
                        }
                    }
                },
                { field: 'name', title: '方案名称' },
                { field: 'workWxAppName', title: '所属企微' },
                { field: 'scene', title: '使用场景' },
                {
                    field: 'serviceNames', title: '承接员工', templet: function (e) {
                        var serviceNames = '';
                        if (e.serviceNames != null && e.serviceNames.length > 0) {
                            for (var i = 0; i < e.serviceNames.length; i++) {
                                if (serviceNames != '') {
                                    serviceNames += ',';
                                }
                                serviceNames += e.serviceNames[i];
                            }
                        }
                        else {
                            serviceNames = '-';
                        }
                        return serviceNames;
                    }
                },
                {
                    field: 'invalidRate', title: '重复进线周期', templet: function (e) {
                        return e.cycle + '小时';
                    }
                },
                {
                    field: 'invalidRate', title: '可添加员工数', templet: function (e) {
                        return e.maxAddService + '个';
                    }
                },
                {
                    field: 'invalidRate', title: '无效资源比例', templet: function (e) {
                        return (e.invalidRate * 100) + '%';
                    }
                },
                {
                    title: '标签', templet: function (e) {
                        var tags = '';
                        if (e.corpTags != null && e.corpTags.length > 0) {
                            for (var i = 0; i < e.corpTags.length; i++) {
                                tags += '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary">' + e.corpTags[i] + '</button>';
                            }
                        }
                        else {
                            tags = '-';
                        }
                        return tags;
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 180, align: 'left', toolbar: '#kf-case-bar' }
            ], { qrType: 2 }, '#topToolBar');
            //监听表格事件
            layui.kfCase.tableEvent();
        },
        /**
      * 获取加粉方案列表(视频号)
      * */
        querySph: function () {
            layui.tableRequest.request('resource', true, 'kf-case-list', '/admin/workwx/kf-case/query', 'application/json', [
                { type: 'checkbox', fixed: 'left' },
                {
                    field: 'id', title: 'ID', fixed: 'left', templet: function (e) {
                        if (layui.setter.companyId == '6340ec834d7e5a21ed46e995') {
                            return '<a target="_blank" href="https://sg1.sdshenguang.com/pages/dy/sg/ty01/index.html?cid=' + e.id + '" class="link">' + e.id + '</a>';
                        }
                        else if (layui.setter.companyId == '6673da5a1e025c772c4a6d73') {
                            return '<a target="_blank" href="https://h5.dnyx.com/pages/mp/qrcode.html?id=' + e.id + '" class="link">' + e.id + '</a>';
                        }
                        else {
                            return e.id;
                        }
                    }
                },
                { field: 'name', title: '方案名称' },
                { field: 'workWxAppName', title: '所属企微' },
                { field: 'scene', title: '使用场景' },
                {
                    field: 'invalidRate', title: '重复进线周期', templet: function (e) {
                        return e.cycle + '小时';
                    }
                },
                {
                    field: 'invalidRate', title: '可添加员工数', templet: function (e) {
                        return e.maxAddService + '个';
                    }
                },
                {
                    field: 'serviceNames', title: '承接员工', templet: function (e) {
                        var serviceNames = '';
                        if (e.serviceNames != null && e.serviceNames.length > 0) {
                            for (var i = 0; i < e.serviceNames.length; i++) {
                                if (serviceNames != '') {
                                    serviceNames += ',';
                                }
                                serviceNames += e.serviceNames[i];
                            }
                        }
                        else {
                            serviceNames = '-';
                        }
                        return serviceNames;
                    }
                },
                {
                    field: 'invalidRate', title: '无效资源比例', templet: function (e) {
                        return (e.invalidRate * 100) + '%';
                    }
                },
                {
                    title: '标签', templet: function (e) {
                        var tags = '';
                        if (e.corpTags != null && e.corpTags.length > 0) {
                            for (var i = 0; i < e.corpTags.length; i++) {
                                tags += '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary">' + e.corpTags[i] + '</button>';
                            }
                        }
                        else {
                            tags = '-';
                        }
                        return tags;
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 180, align: 'left', toolbar: '#kf-case-bar' }
            ], { qrType: 4 }, '#topToolBar');
            //监听表格事件
            layui.kfCase.tableEvent();
        },
        /**
      * 获取客服加粉方案列表(系统生成活码方式v3)
      * */
        queryV3: function () {
            layui.tableRequest.request('resource', true, 'kf-case-list', '/admin/workwx/kf-case/query', 'application/json', [
                {
                    field: 'id', title: 'ID', fixed: 'left'
                },
                { field: 'name', title: '方案名称' },
                { field: 'workWxAppName', title: '所属企微' },
                { field: 'scene', title: '使用场景' },

                {
                    field: 'invalidRate', title: '重复进线周期', templet: function (e) {
                        return e.cycle + '小时';
                    }
                },
                {
                    field: 'invalidRate', title: '可添加员工数', templet: function (e) {
                        return e.maxAddService + '个';
                    }
                },
                {
                    field: 'createdAt', title: '创建时间', width: 170, templet: function (e) {
                        return layui.common.timeFormat(e.createdAt);
                    }
                },
                { fixed: 'right', title: '操作', width: 130, align: 'left', toolbar: '#kf-case-bar' }
            ], { qrType: 3 }, '');
            //监听表格事件
            layui.kfCase.tableEvent();
        },
        /**
        * 创建客服加粉方案
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("kf-case-list");
                    parent.layui.common.alertAutoClose("微信客服加粉方案创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
       * 创建客服加粉方案(系统生成活码方式)
       * */
        createSystem: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/for-system/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("kf-case-list");
                    if (res.result != null && res.result != "") {
                        parent.layui.common.alert(res.result, 0);
                    }
                    else {
                        parent.layui.common.alertAutoClose("创建成功");
                    }
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
       * 创建客服加粉方案(系统生成活码方式v3)
       * */
        createV3: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/v3/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("kf-case-list");
                    parent.layui.common.alertAutoClose("创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },

        /**
        * 编辑客服加粉方案
        * @param {any} data
        */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("kf-case-list");
                    parent.layui.common.alertAutoClose("微信客服加粉方案编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 编辑客服加粉方案(系统生成活码方式)
        * @param {any} data
        */
        updateSystem: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/for-system/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("kf-case-list");
                    if (res.result != null && res.result != "") {
                        parent.layui.common.alert(res.result, 0);
                    }
                    else {
                        parent.layui.common.alertAutoClose("编辑成功");
                    }
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 编辑客服加粉方案(系统生成活码方式v3)
        * @param {any} data
        */
        updateV3: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/v3/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("kf-case-list");
                    parent.layui.common.alertAutoClose("编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 编辑企微标签
        * */
        updateCorpTag: function (data) {
            var id = layui.common.getUrlParam('id') || '';
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/corp-tag/update',
                data: JSON.stringify({ id: id, tags: data.field.tags }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.tableRequest.reload("kf-case-list");
                    parent.layui.common.alertAutoClose("保存成功");
                    parent.layui.common.closeType('iframe');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
      * 编辑企微标签(系统生成活码方式)
      * */
        updateCorpTagSystem: function (data) {
            var id = layui.common.getUrlParam('id') || '';
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/for-system/corp-tag/update',
                data: JSON.stringify({ id: id, tags: data.field.tags }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.tableRequest.reload("kf-case-list");
                    parent.layui.common.alertAutoClose("保存成功");
                    parent.layui.common.closeType('iframe');
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 编辑企微标签(系统生成活码方式)
        * */
        updateServiceSystem: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/services/for-system/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("kf-case-list");
                    if (res.result != null && res.result != "") {
                        parent.layui.common.alert(res.result, 0);
                    }
                    else {
                        parent.layui.common.alertAutoClose("员工配置成功");
                    }
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
        * 删除客服加粉方案
        * @param {any} data
        */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("kf-case-list");
                    layui.common.alertAutoClose("微信客服加粉方案删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除客服加粉方案(系统生成活码方式)
         * @param {any} data
         */
        deleteSystem: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/for-system/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("kf-case-list");
                    layui.common.alertAutoClose("微信客服加粉方案删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除客服加粉方案(系统生成活码方式v3)
         * @param {any} data
         */
        deleteV3: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/kf-case/v3/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("kf-case-list");
                    layui.common.alertAutoClose("删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event == 'edit') {
                    layui.common.openIframe('微信客服加粉方案', 740, 700, 'update.html?id=' + data.id);
                }
                else if (obj.event == 'tag') {
                    layui.common.openIframe('编辑企业标签', 700, 600, 'update-tag.html?id=' + data.id);
                }
                else if (obj.event == 'del') {
                    var confirmIndex = layui.layer.confirm('删除加粉方案后，历史落地页加粉将失效，确定删除该方案吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.kfCase.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event == 'edit-system') {
                    layui.common.openIframe('微信客服加粉方案', 740, 700, 'update-system.html?id=' + data.id);
                } else if (obj.event == 'edit-sph') {
                    layui.common.openIframe('编辑加粉方案', 740, 550, 'update-sph.html?id=' + data.id);
                }
                else if (obj.event == 'tag-system') {
                    layui.common.openIframe('编辑企业标签', 700, 600, 'update-tag-system.html?id=' + data.id);
                }
                else if (obj.event == 'del-system') {
                    var confirmIndex = layui.layer.confirm('删除加粉方案后，历史落地页加粉将失效，确定删除该方案吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.kfCase.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event == 'edit-v3') {
                    layui.common.openIframe('微信客服加粉方案', 740, 700, 'update-v3.html?id=' + data.id);
                }
                else if (obj.event == 'del-v3') {
                    var confirmIndex = layui.layer.confirm('删除加粉方案后，历史落地页加粉将失效，确定删除该方案吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.kfCase.deleteV3(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
            });
        },
        /**
         * 绑定事件
         * */
        bindEvent: function (opType) {
            //菜单类型选择
            layui.form.on('select(menu-type)', function (data) {
                var menuTypeTpl = '';
                var type = layui.$(data.elem).val();
                if (type == 'click') {
                    menuTypeTpl = '<div class="reply-list">';
                    menuTypeTpl += '</div>';
                    menuTypeTpl += '<div class="layui-input-inline" style="margin-bottom:10px; margin-top:10px;width:100px;">';
                    menuTypeTpl += '<button type="button" class="layui-btn layui-btn-primary add-reply">';
                    menuTypeTpl += '<i class="layui-icon">&#xe654;</i> 添加回复';
                    menuTypeTpl += '</button>';
                    menuTypeTpl += '</div>';
                }
                else if (type == 'view') {
                    menuTypeTpl = '<div class="layui-input-inline" style="margin-bottom:10px; margin-top:10px;width:350px;">';
                    menuTypeTpl += '<input type="text" lay-verify="required" placeholder="请输入跳转地址" autocomplete="off" class="layui-input menu-link">';
                    menuTypeTpl += '</div>';
                }
                layui.$(data.elem).parent().parent().find('.reply-container').html(menuTypeTpl);
            });

            //添加菜单行
            layui.$(document).on("click", '.add-menu', function () {
                var menuTpl = '<div class="menu-input-item" style="margin-bottom:10px;float:left">';
                menuTpl += '<div class="layui-input-inline" style="width:120px">';
                menuTpl += '<select lay-filter="menu-type" class="menu-type" lay-verify="required">';
                menuTpl += '<option value="">菜单类型</option>';
                menuTpl += '<option value="click">回复菜单</option>';
                menuTpl += '<option value="view">超链接菜单</option>';
                menuTpl += '</select>';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width: 420px; ">';
                menuTpl += '<div class="layui-input-inline" style="width:350px">';
                menuTpl += '<input type="text" lay-verify="required" placeholder="菜单名称，多个相同回复的不同菜单名称使用 | 分隔" autocomplete="off" class="layui-input menu-name">';
                menuTpl += '</div>';
                menuTpl += '<div class="layui-input-inline" style="width:50px;">';
                menuTpl += '<button type="button" class="layui-btn layui-btn-primary remove-menu">';
                menuTpl += '<i class="layui-icon">&#xe640;</i>';
                menuTpl += '</button>';
                menuTpl += '</div>';
                menuTpl += '<div class="reply-container">';
                menuTpl += '</div>';
                menuTpl += '</div>';
                menuTpl += '</div>';
                layui.$('.menu-input-list').append(menuTpl);
                layui.form.render('select');
            });

            //删除菜单行
            layui.$(document).on("click", '.remove-menu', function () {
                layui.$(this).parent().parent().parent('.menu-input-item').remove();
            });

            //添加回复行
            layui.$(document).on("click", '.add-reply', function () {
                var replyTpl = '<div class="layui-input-inline reply-item" style="width: 420px; margin-top: 10px;">';
                replyTpl += '<div class="layui-input-inline" style="width:350px;">';
                replyTpl += '<div class="layui-input-inline" style="width:100px;">';
                replyTpl += '<select lay-filter="reply-type" class="reply-type" lay-verify="required">';
                replyTpl += '<option value="">消息类型</option>';
                replyTpl += '<option value="image">图片消息</option>';
                replyTpl += '<option value="text">文本消息</option>';
                replyTpl += '</select>';
                replyTpl += '</div>';
                replyTpl += '<div class="layui-input-inline" style="width:240px;margin-right:0;">';
                replyTpl += '<div class="reply-content">';
                replyTpl += '</div>';
                replyTpl += '</div>';
                replyTpl += '</div>';
                replyTpl += '<div class="layui-input-inline" style="width:50px;">';
                replyTpl += '<button type="button" class="layui-btn layui-btn-primary remove-reply">';
                replyTpl += '<i class="layui-icon">&#xe640;</i>';
                replyTpl += '</button>';
                replyTpl += '</div>';
                replyTpl += '</div>';
                layui.$(this).parent().prev('.reply-list').append(replyTpl);
                layui.form.render('select');
            });

            //删除回复行
            layui.$(document).on("click", '.remove-reply', function () {
                layui.$(this).parent().parent('.reply-item').remove();
            });

            //回复框类型选择
            layui.form.on('select(reply-type)', function (data) {
                var uploadId = layui.common.generateGuid();
                if (layui.$('#uploadId').length > 0) {
                    uploadId = layui.common.generateGuid();
                }
                var replyContentTpl = '<button type="button" class="layui-btn layui-btn-primary" id="' + uploadId + '"><i class="layui-icon"></i>上传图片</button>';
                replyContentTpl += '<img class="reply-img" id="img_' + uploadId + '" style="width: 35px; height: 35px;margin-left:10px;" /><input class="imageUrl" id="imgUrl_' + uploadId + '" type="hidden" />';
                replyContentTpl += '<input class="imageId" id="imgId_' + uploadId + '" type="hidden" /><input class="imagePath" id="imgPath_' + uploadId + '" type="hidden" />';
                var type = layui.$(data.elem).val();
                if (type == 'text') {
                    replyContentTpl = '<textarea type="text" lay-verify="required" placeholder="请输入回复文本消息内容" autocomplete="off" class="layui-textarea reply-text"></textarea>';
                }
                layui.$(data.elem).parent().parent().find('.reply-content').html(replyContentTpl);
                //渲染上传控件
                layui.uploadFile('Resource', uploadId, '/admin/workwx/kf-case/qrcode/upload', function (res) {
                    if (res.isSuccess) {
                        layui.$("#imgId_" + uploadId).val(res.result.imageId);
                        layui.$("#imgPath_" + uploadId).val(res.result.imagePath);
                        layui.$("#imgUrl_" + uploadId).val(res.result.url);
                        layui.$('#img_' + uploadId).attr('src', res.result.url);
                        layui.common.alertAutoClose("上传成功");
                    }
                    else {
                        layui.common.alert(res.message, 2);
                    }
                });
            });

            //渲染使用场景
            layui.dic.query('workwx_scene', function (res) {
                var options = '<option value="">请选择使用场景</option>'
                if (res.isSuccess) {
                    for (var i = 0; i < res.result.length; i++) {
                        options += '<option value="' + res.result[i].key + '">' + res.result[i].value + '</option>';
                    }
                }
                layui.$('#scene').html(options);
                layui.form.render('select');
            });

            //企微应用选择后，加载微信客服账号列表
            layui.form.on('select(workwx-app)', function (data) {
                var workWxAppId = data.value;
                layui.kfAccount.getAll(workWxAppId, function (res) {
                    var options = '<option value="">请选择微信客服</option>'
                    if (res.isSuccess) {
                        for (var i = 0; i < res.result.length; i++) {
                            options += '<option value="' + res.result[i].open_kfid + '">' + res.result[i].name + '</option>';
                        }
                    }
                    layui.$('#openkf-id').html(options);
                    layui.form.render('select');
                });
                layui.kfCase.initWorkUser(workWxAppId, []);
            });
            //使用场景选择腾讯视频号时，加载腾讯推广账户列表
            layui.form.on('select(scene)', function (data) {
                if (data.value == 'tenxun_sph') {
                    layui.$(".tencent-ad-account").show();
                    var workWxAppId = layui.$("#workwx-app-view").val();
                    layui.tencentAccount.getAll(workWxAppId, function (res) {
                        var options = '<option value="">请选择推广账户</option>'
                        if (res.isSuccess) {
                            for (var i = 0; i < res.result.length; i++) {
                                options += '<option value="' + res.result[i].id + '">' + res.result[i].name + '</option>';
                            }
                        }
                        layui.$('#tencentAdAccountId').html(options);
                        layui.form.render('select');
                    })
                }
                else {
                    layui.$(".tencent-ad-account").hide();
                }
            });
            //提交数据
            layui.form.on('submit(kf-case-submit)', function (data) {
                //腾讯视频号必须选择推广账户，否则数据无法回传
                if (data.field.scene == 'tenxun_sph' && data.field.tencentAdAccountId == '') {
                    layui.common.alertAutoClose('请选择腾讯推广账户');
                    return;
                }
                var menuList = layui.$(".menu-input-item");
                //if (data.field.scene != 'tenxun_sph_zrl') {
                //    if (opType != 'create-system' && opType != 'update-system') {
                //        if (menuList.length == 0) {
                //            layui.common.alertAutoClose('请添加菜单配置项');
                //            return;
                //        }
                //    }
                //    if (data.field.openKfId == '') {
                //        layui.common.alertAutoClose('请选择微信客服');
                //        return;
                //    }
                //}

                if (opType != 'create-system' && opType != 'update-system' && opType != 'create-sph' && opType != 'update-sph') {
                    var menus = [];
                    for (var i = 0; i < menuList.length; i++) {
                        var option = { id: '', name: '', type: '', url: '', autoReplyMsgList: [] };
                        var that = menuList.eq(i);
                        var menuType = that.find('.menu-type').val();
                        option.type = menuType;
                        option.name = that.find('.menu-name').val();
                        if (menuType == 'view') {
                            //打开链接
                            option.url = that.find('.menu-link').val();
                        }
                        else if (menuType == 'click') {
                            //点击回复消息
                            var replyList = that.find('.reply-item');
                            if (replyList.length == 0) {
                                layui.common.alertAutoClose('请配置回复项');
                                return;
                            }
                            //循环获取回复列表
                            for (var j = 0; j < replyList.length; j++) {
                                var replyThat = replyList.eq(j);
                                var replyType = replyThat.find('.reply-type').val();
                                if (replyType == 'text') {
                                    //自动回复文本
                                    var replyContent = replyThat.find('.reply-text').val();
                                    option.autoReplyMsgList.push({ type: 'text', content: replyContent, imageId: '', imagePath: '', imageUrl: '' });
                                }
                                else if (replyType == 'image') {
                                    //自动回复图片
                                    var imageId = replyThat.find('.imageId').val();
                                    var imagePath = replyThat.find('.imagePath').val();
                                    var imageUrl = replyThat.find('.imageUrl').val();
                                    option.autoReplyMsgList.push({ type: 'image', content: '', imageId: imageId, imagePath: imagePath, imageUrl: imageUrl });
                                }
                            }
                        }
                        menus.push(option);
                    }
                    data.field.kfCaseMenu = menus;
                }
                if (opType == 'create') {
                    layui.kfCase.create(data);
                }
                else if (opType == 'update') {
                    layui.kfCase.update(data);
                }
                else if (opType == 'create-system' || opType == 'create-sph') {
                    var users = xmSelUser.getValue();
                    if (users == null || users.length == 0) {
                        layui.common.alertAutoClose('请选择承接人员');
                        return;
                    }
                    var services = [];
                    for (var i = 0; i < users.length; i++) {
                        services.push({
                            userId: users[i].value,
                            userName: users[i].name
                        });
                    }
                    data.field.services = services;
                    if (opType == 'create-sph') {
                        data.field.qrType = 4;
                    }
                    layui.kfCase.createSystem(data);
                }
                else if (opType == 'update-system' || opType == 'update-sph') {
                    var users = xmSelUser.getValue();
                    if (users == null || users.length == 0) {
                        layui.common.alertAutoClose('请选择承接人员');
                        return;
                    }
                    var services = [];
                    for (var i = 0; i < users.length; i++) {
                        services.push({
                            userId: users[i].value,
                            userName: users[i].name
                        });
                    }
                    data.field.services = services;
                    layui.kfCase.updateSystem(data);
                }
                return false; //阻止表单跳转
            });
        }
    }

    exports('kfCase', func);
});