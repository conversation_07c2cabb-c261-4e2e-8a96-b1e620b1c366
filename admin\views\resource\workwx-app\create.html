﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-label { width: 150px; }
        .layui-form-item .layui-input-inline { width: 500px; }
    </style>
</head>
<body>
    <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
        <div class="layui-form-item">
            <label class="layui-form-label">名称</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="required" placeholder="请输入企微名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">企业微信ID</label>
            <div class="layui-input-inline">
                <input type="text" name="corpId" placeholder="请输入企业微信CorpId" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">客户Secret</label>
            <div class="layui-input-inline">
                <input type="text" name="corpSecret" placeholder="请输入企业微信客户应用CorpSecret" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">客户Token</label>
            <div class="layui-input-inline">
                <input type="text" name="token" placeholder="请输入企业微信客户应用Token" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">客户EncodingAESKey</label>
            <div class="layui-input-inline">
                <input type="text" name="encodingAESKey" placeholder="请输入企业微信客户应用EncodingAESKey" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">客服AgentId</label>
            <div class="layui-input-inline">
                <input type="text" name="kfAgentId" placeholder="请输入企业微信客服应用id" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">客服Secret</label>
            <div class="layui-input-inline">
                <input type="text" name="kfCorpSecret" placeholder="请输入企业微信客服应用CorpSecret" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">客服Token</label>
            <div class="layui-input-inline">
                <input type="text" name="kfToken" placeholder="请输入企业微信客服应用Token" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">客服EncodingAESKey</label>
            <div class="layui-input-inline">
                <input type="text" name="kfEncodingAESKey" placeholder="请输入企业微信客服应用EncodingAESKey" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">自建应用AgentId</label>
            <div class="layui-input-inline">
                <input type="text" name="mailListAgentId" placeholder="请输入企业微信自建应用id" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">自建应用Secret</label>
            <div class="layui-input-inline">
                <input type="text" name="mailListSecret" placeholder="请输入企业微信通讯录自建应用Secret" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">会话存档Secret</label>
            <div class="layui-input-inline">
                <input type="text" name="msgCorpSecret" placeholder="请输入企业微信会话存档应用Secret" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">会话存档私钥</label>
            <div class="layui-input-inline">
                <textarea name="msgPrivateKey" placeholder="请输入企业微信会话存档应用私钥" autocomplete="off" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="app-create-submit" value="确认添加">
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'workWxApp'], function () {
            layui.form.on('submit(app-create-submit)', function (data) {
                layui.workWxApp.create(data);
                return false; //阻止表单跳转
            });
        })
    </script>
</body>
</html>