﻿layui.define(['laytpl', 'request', 'tableRequest', 'common'], function (exports) {
    var func = {
        /**
        * 获取所有应用列表
        * */
        getAll: function (callbackFunc) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/oceanengine/app/get-all',
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    callbackFunc(res);
                }
            })
        },
        /**
         * 通过id获取单个巨量应用
         * */
        get: function () {
            var id = layui.common.getUrlParam('id');
            if (id == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/oceanengine/app/get?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.$('input[name=id]').val(res.result.id);
                    layui.$('input[name=name]').val(res.result.name);
                    layui.$('input[name=appId]').val(res.result.appId);
                    layui.$('input[name=secret]').val(res.result.secret);
                    layui.$('input[name=scope]').val(res.result.scope);
                    layui.$('input[name=callbackUrl]').val(res.result.callbackUrl);
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 获取巨量应用列表
         * */
        query: function () {
            layui.tableRequest.request('resource', true, 'app-list', '/admin/workwx/oceanengine/app/query', 'application/json', [
                { field: 'appId', title: '应用AppID' },
                { field: 'name', title: '应用名称' },
                { field: 'callbackUrl', title: '回调地址' },
                { field: 'scope', title: '授权范围' },
                { field: 'state', title: '自定义参数' },
                { field: 'accessToken', title: '授权凭证' },
                {
                    field: 'accessTokenExpireTime', title: '授权凭证过期时间', templet: function (e) {
                        if (e.accessTokenExpireTime != null) {
                            return layui.common.timeFormat(e.accessTokenExpireTime);
                        }
                        else {
                            return '';
                        }
                    }
                },
                { field: 'refreshToken', title: '刷新凭证' },
                {
                    field: 'refreshTokenExpireTime', title: '刷新凭证过期时间', templet: function (e) {
                        if (e.refreshTokenExpireTime != null) {
                            return layui.common.timeFormat(e.refreshTokenExpireTime);
                        }
                        else {
                            return '';
                        }
                    }
                },
                { fixed: 'right', title: '操作', width: 200, align: 'left', toolbar: '#app-bar' }
            ]);
            //监听表格事件
            layui.oceanengineApp.tableEvent();
        },
        /**
        * 创建巨量应用
        * */
        create: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/oceanengine/app/create',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("app-list");
                    parent.layui.common.alertAutoClose("巨量应用创建成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 编辑巨量应用
         * @param {any} data
         */
        update: function (data) {
            layui.$(data.elem).addClass('layui-btn-disabled').attr("disabled", "disabled");
            layui.request({
                method: 'post',
                url: '/admin/workwx/oceanengine/app/update',
                data: JSON.stringify(data.field),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("app-list");
                    parent.layui.common.alertAutoClose("巨量应用编辑成功");
                }
                else {
                    layui.$(data.elem).removeClass('layui-btn-disabled').removeAttr("disabled");;
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 删除巨量应用
         * @param {any} data
         */
        delete: function (id) {
            layui.request({
                method: 'post',
                url: '/admin/workwx/oceanengine/app/delete?id=' + id,
            }).then(function (res) {
                if (res.isSuccess) {
                    layui.tableRequest.reload("app-list");
                    layui.common.alertAutoClose("巨量应用删除成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 保存accesstoken
         * */
        saveAccessToken: function () {
            var state = layui.common.getUrlParam('state');
            if (state == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("state参数有误");
                return;
            }
            var auth_code = layui.common.getUrlParam('auth_code');
            if (auth_code == '') {
                parent.layui.common.closeType('iframe');
                parent.layui.common.alertAutoClose("auth_code参数有误");
                return;
            }
            layui.request({
                method: 'post',
                url: '/admin/workwx/oceanengine/app/access-token/save',
                data: JSON.stringify({
                    authCode: auth_code,
                    state: state
                }),
                headers: { 'Content-Type': 'application/json' },
            }).then(function (res) {
                if (res.isSuccess) {
                    parent.layui.common.closeType('iframe');
                    parent.layui.tableRequest.reload("app-list");
                    parent.layui.common.alertAutoClose("巨量应用授权成功");
                }
                else {
                    layui.common.alert(res.message, 0);
                }
            })
        },
        /**
         * 监听表格单击事件
         * */
        tableEvent: function () {
            layui.table.on('tool(list)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layui.common.openIframe('编辑巨量应用', 600, 400, 'update.html?id=' + data.id);
                }
                else if (obj.event === 'del') {
                    var confirmIndex = layui.layer.confirm('确定删除该巨量应用吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.oceanengineApp.delete(data.id);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });
                }
                else if (obj.event === 'auth') {
                    layui.request({
                        method: 'post',
                        url: '/admin/workwx/oceanengine/app/auth-url/get?id=' + data.id,
                    }).then(function (res) {
                        if (res.isSuccess) {
                            layui.common.openIframe('授权巨量账户', 800, 600, res.result);
                        }
                        else {
                            layui.common.alert(res.message, 0);
                        }
                    })
                }
            });
        }
    }

    exports('oceanengineApp', func);
});