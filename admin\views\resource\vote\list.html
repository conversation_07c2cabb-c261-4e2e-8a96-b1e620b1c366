﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 300px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="txb_keywords" name="keywords" placeholder="姓名/手机号" type="text" class="layui-input" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="vote-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                        <button class="layui-btn  layuiadmin-btn-list layui-btn-primary" style="display:none;" id="vote-export" lay-submit lay-filter="vote-export">
                            <i class="layui-icon layui-icon-export layuiadmin-button-btn"></i>导出
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="vote-list" lay-filter="list"></table>
            </div>
        </div>
    </div>

    <script id="workwx-app-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <option value="{{item.id}}">{{item.name}}</option>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'vote'], function () {
            layui.vote.query();
            //监听查询按钮
            layui.form.on('submit(vote-search)', function (data) {
                var field = data.field;
                //执行重载
                layui.tableRequest.reload("vote-list", {
                    where: field
                });
            });
            if (layui.common.getPermission('6369c6efd544954399357c36', 'resource.product.vote.export')) {
                layui.$('#vote-export').show();
                layui.form.on('submit(vote-export)', function (data) {
                    var confirmIndex = layui.layer.confirm('确定导出投票数据吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function () {
                        layui.vote.export(data);
                        layui.layer.close(confirmIndex);
                    }, function () {
                        layui.layer.close(confirmIndex);
                    });

                });
            }
        });
    </script>
</body>
</html>