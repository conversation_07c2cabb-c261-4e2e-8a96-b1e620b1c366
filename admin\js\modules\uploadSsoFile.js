layui.define(['upload', 'setter', 'common'], function (exports) {
    var uploadSsoFile = function (requestBase, domId, chooseCallbackFunc, option) {
        var baseUrl = '';
        if (requestBase == "sso") {
            baseUrl = layui.setter.request.ssoBaseUrl;
        }
        var headers = {};
        headers[layui.setter.request.tokenName] = layui.common.getCookie(layui.setter.request.tokenName);
        var userStr = localStorage.getItem('ly-admin-user');
        if (userStr != null) {
            var userObj = JSON.parse(userStr);
            headers['uid'] = userObj.id;
        }
        var loddingIndex;
        layui.upload.render({
            headers: headers,
            multiple: true,
            acceptMime: option ? option.acceptMime : ""
            , ext: option ? option.ext : ""
            , elem: '#' + domId
            , url: ""
            , auto: false
            , accept: 'file'
            , before: function (obj) {
                loddingIndex = layui.layer.msg('上传中', { icon: 16, time: 0 });
            }
            , choose: function (obj) {
                if (chooseCallbackFunc != null && chooseCallbackFunc != undefined) {
                    chooseCallbackFunc(obj);
                    this.elem.next()[0].value = ''
                }
            }
            , done: function (res, index, upload) {
                layui.layer.close(loddingIndex);
                if (!res.isSuccess && res.code == 22000) {
                    layui.common.delCookie(layui.setter.request.tokenName);
                    var appId = layui.common.getUrlParam('appId') || '';
                    var loginUrl = '/admin/login.html';
                    if (appId != '') {
                        loginUrl += '?appId=' + appId;
                    }
                    location.href = loginUrl;
                }
            }
        });
    }
    //输出 uploadFile 接口
    exports('uploadSsoFile', uploadSsoFile);
});