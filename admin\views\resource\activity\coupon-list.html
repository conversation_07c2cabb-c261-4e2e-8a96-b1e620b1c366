﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 200px; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input id="keywords" name="keywords" type="text" class="layui-input" placeholder="请输入领取人手机号" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">使用状态</label>
                        <div class="layui-input-inline">
                            <select name="useStatus">
                                <option value="">请选择使用状态</option>
                                <option value="1">未使用</option>
                                <option value="2">已使用</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="coupon-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table id="coupon-list" lay-filter="list"></table>
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['form', 'common', 'activity'], function () {
            layui.activity.queryCoupon();
            //监听查询按钮
            layui.form.on('submit(coupon-search)', function (data) {
                var field = data.field;
                if (field.useStatus == '') {
                    field.useStatus = -1;
                }
                //执行重载
                layui.tableRequest.reload("coupon-list", {
                    where: field
                });
            });
        });
    </script>
</body>
</html>