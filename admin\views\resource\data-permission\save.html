﻿<!DOCTYPE html>
<html style="background:#fff">
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="/lib/font-awesome/font-awesome.min.css" rel="stylesheet" />
    <link href="/admin/css/base.css" rel="stylesheet" />
    <style>
        .layui-form-item .layui-input-inline { width: 300px; }
        .inp { margin-bottom: 5px; float: left; }
    </style>
</head>
<body>
    <div class=" layui-form" style="margin-bottom:80px;">
        <div style="padding:20px;box-sizing:border-box;">
            <div id="subsidiary-view"></div>
        </div>
        <div class="layui-form-item" style="margin-top:20px;">
            <div class="layui-form-item" style="margin-top: 20px; position: fixed; bottom: 0; text-align: center; background: #fff; width: 100%; margin: 0; padding: 15px 0; border-top: 1px solid #e6e6e6">
                <input type="button" lay-submit="" class="layui-btn layui-btn-normal" lay-filter="permission-save-submit" value="保存设置">
                <input type="button" lay-submit="" class="layui-btn layui-btn-primary" lay-filter="cancel" value="返回上一页">
            </div>
        </div>
    </div>

    <script id="subsidiary-tpl" type="text/html">
        {{#  layui.each(d, function(index, item){ }}
        <span class="inp">
            <input type="checkbox" name="subsidiaryItems" class="subsidiaryItems" value="{{item.id}}" title="{{item.name}}" {{# if(item.isChecked){}} checked {{# }}} lay-skin="primary">
        </span>
        {{#  }); }}
    </script>
    <script src="/lib/layui/layui.js"></script>
    <script src="/admin/js/global.js"></script>
    <script type="text/javascript">
        layui.use(['dataPermission'], function () {
            layui.dataPermission.getAll();
        });
    </script>
</body>
</html>